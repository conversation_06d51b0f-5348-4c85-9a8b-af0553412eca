class KeyManager {
    constructor() {
        this.keys = [];
        this.filteredKeys = [];
        this.currentFilter = 'all';
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadKeys();
        this.updateStats();
        this.renderKeys();
    }

    setupEventListeners() {
        // Search functionality
        document.getElementById('searchKeys').addEventListener('input', (e) => {
            this.filterKeys();
        });

        // Filter tabs
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.filter;
                this.filterKeys();
            });
        });
    }

    async loadKeys() {
        try {
            document.getElementById('loadingIndicator').style.display = 'block';
            
            const response = await fetch('/api/admin/licenses');
            const data = await response.json();
            
            if (data.success) {
                this.keys = data.licenses || [];
            } else {
                this.showNotification('Failed to load keys', 'error');
            }
        } catch (error) {
            console.error('Failed to load keys:', error);
            this.showNotification('Failed to connect to server', 'error');
        } finally {
            document.getElementById('loadingIndicator').style.display = 'none';
        }
    }

    filterKeys() {
        const searchTerm = document.getElementById('searchKeys').value.toLowerCase();
        
        this.filteredKeys = this.keys.filter(key => {
            // Search filter
            const matchesSearch = !searchTerm || 
                key.key.toLowerCase().includes(searchTerm) ||
                (key.hardwareId && key.hardwareId.toLowerCase().includes(searchTerm)) ||
                (key.notes && key.notes.toLowerCase().includes(searchTerm));

            // Status filter
            const now = new Date();
            const expiresAt = key.expiresAt ? new Date(key.expiresAt) : null;
            const isExpired = expiresAt && expiresAt <= now;
            const isExpiring = expiresAt && !isExpired && (expiresAt - now) <= (7 * 24 * 60 * 60 * 1000); // 7 days
            const isLifetime = !expiresAt;
            const isUsed = !!key.hardwareId;

            let matchesFilter = true;
            switch (this.currentFilter) {
                case 'active':
                    matchesFilter = !isExpired;
                    break;
                case 'expired':
                    matchesFilter = isExpired;
                    break;
                case 'expiring':
                    matchesFilter = isExpiring;
                    break;
                case 'lifetime':
                    matchesFilter = isLifetime;
                    break;
                case 'used':
                    matchesFilter = isUsed;
                    break;
                case 'unused':
                    matchesFilter = !isUsed;
                    break;
                default:
                    matchesFilter = true;
            }

            return matchesSearch && matchesFilter;
        });

        this.renderKeys();
    }

    updateStats() {
        const now = new Date();
        const total = this.keys.length;
        let active = 0, expired = 0, expiring = 0;

        this.keys.forEach(key => {
            const expiresAt = key.expiresAt ? new Date(key.expiresAt) : null;
            const isExpired = expiresAt && expiresAt <= now;
            const isExpiring = expiresAt && !isExpired && (expiresAt - now) <= (7 * 24 * 60 * 60 * 1000);

            if (isExpired) {
                expired++;
            } else if (isExpiring) {
                expiring++;
            } else {
                active++;
            }
        });

        document.getElementById('totalKeys').textContent = total;
        document.getElementById('activeKeys').textContent = active;
        document.getElementById('expiredKeys').textContent = expired;
        document.getElementById('expiringKeys').textContent = expiring;
    }

    renderKeys() {
        const container = document.getElementById('keysList');
        
        if (this.filteredKeys.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: var(--muted-text);">
                    <div style="font-size: 18px;">📭 No keys found</div>
                    <div style="margin-top: 10px;">Try adjusting your search or filter criteria</div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.filteredKeys.map(key => this.renderKeyCard(key)).join('');
    }

    renderKeyCard(key) {
        const now = new Date();
        const expiresAt = key.expiresAt ? new Date(key.expiresAt) : null;
        const isExpired = expiresAt && expiresAt <= now;
        const isExpiring = expiresAt && !isExpired && (expiresAt - now) <= (7 * 24 * 60 * 60 * 1000);
        
        let statusBadge = '';
        if (isExpired) {
            statusBadge = '<span class="status-badge status-expired">Expired</span>';
        } else if (isExpiring) {
            statusBadge = '<span class="status-badge status-expiring">Expiring Soon</span>';
        } else {
            statusBadge = '<span class="status-badge status-active">Active</span>';
        }

        const createdDate = new Date(key.createdAt).toLocaleDateString();
        const expiryDate = expiresAt ? expiresAt.toLocaleDateString() : 'Never';
        const timeRemaining = this.getTimeRemaining(expiresAt);

        return `
            <div class="key-card">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: var(--text-color);">License Key</h3>
                    ${statusBadge}
                </div>
                
                <div class="key-display">
                    ${key.key}
                    <button class="copy-btn" onclick="keyManager.copyToClipboard('${key.key}', this)">Copy</button>
                </div>
                
                <div class="key-info">
                    <div class="info-item">
                        <div class="info-label">Duration</div>
                        <div class="info-value">${key.duration || 'Unknown'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Created</div>
                        <div class="info-value">${createdDate}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Expires</div>
                        <div class="info-value">${expiryDate}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Time Remaining</div>
                        <div class="info-value">${timeRemaining}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Hardware ID</div>
                        <div class="info-value">${key.hardwareId ? key.hardwareId.substring(0, 16) + '...' : 'Not Used'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Notes</div>
                        <div class="info-value">${key.notes || 'None'}</div>
                    </div>
                </div>
                
                <div class="actions">
                    <button class="btn-small btn-info" onclick="keyManager.copyToClipboard('${key.key}', this)">Copy Key</button>
                    ${key.hardwareId ? `<button class="btn-small btn-warning" onclick="keyManager.resetHardwareId('${key.key}')">Reset HWID</button>` : ''}
                    <button class="btn-small btn-success" onclick="keyManager.extendKey('${key.key}')">Extend</button>
                    <button class="btn-small btn-danger" onclick="keyManager.deleteKey('${key.key}')">Delete</button>
                </div>
            </div>
        `;
    }

    getTimeRemaining(expiresAt) {
        if (!expiresAt) return 'Lifetime';
        
        const now = new Date();
        const expiry = new Date(expiresAt);
        const diff = expiry - now;
        
        if (diff <= 0) return 'Expired';
        
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        
        if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
        if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
        return 'Less than 1 hour';
    }

    async copyToClipboard(text, button) {
        try {
            await navigator.clipboard.writeText(text);
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            button.classList.add('copied');
            
            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('copied');
            }, 2000);
            
            this.showNotification('Key copied to clipboard!', 'success');
        } catch (error) {
            console.error('Failed to copy:', error);
            this.showNotification('Failed to copy key', 'error');
        }
    }

    async resetHardwareId(licenseKey) {
        if (!confirm('Are you sure you want to reset the hardware ID for this key?')) return;
        
        try {
            const response = await fetch('/api/admin/reset-hwid', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseKey })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Hardware ID reset successfully!', 'success');
                await this.refreshData();
            } else {
                this.showNotification(data.message || 'Failed to reset hardware ID', 'error');
            }
        } catch (error) {
            console.error('Failed to reset hardware ID:', error);
            this.showNotification('Failed to reset hardware ID', 'error');
        }
    }

    async deleteKey(licenseKey) {
        if (!confirm('Are you sure you want to delete this key? This action cannot be undone.')) return;
        
        try {
            const response = await fetch('/api/admin/delete-license', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseKey })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Key deleted successfully!', 'success');
                await this.refreshData();
            } else {
                this.showNotification(data.message || 'Failed to delete key', 'error');
            }
        } catch (error) {
            console.error('Failed to delete key:', error);
            this.showNotification('Failed to delete key', 'error');
        }
    }

    async extendKey(licenseKey) {
        const duration = prompt('Enter extension duration (1hour, 1day, 1week, 1month, 3months, 6months, 1year, lifetime):');
        if (!duration) return;
        
        try {
            const response = await fetch('/api/admin/extend-license', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ licenseKey, duration })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showNotification('Key extended successfully!', 'success');
                await this.refreshData();
            } else {
                this.showNotification(data.message || 'Failed to extend key', 'error');
            }
        } catch (error) {
            console.error('Failed to extend key:', error);
            this.showNotification('Failed to extend key', 'error');
        }
    }

    async createBulkKeys() {
        const duration = prompt('Enter duration for new keys (1hour, 1day, 1week, 1month, 3months, 6months, 1year, lifetime):', '1month');
        if (!duration) return;
        
        try {
            const promises = [];
            for (let i = 0; i < 10; i++) {
                promises.push(
                    fetch('/api/admin/create-license', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ duration, notes: `Bulk created ${new Date().toISOString()}` })
                    })
                );
            }
            
            await Promise.all(promises);
            this.showNotification('10 keys created successfully!', 'success');
            await this.refreshData();
        } catch (error) {
            console.error('Failed to create bulk keys:', error);
            this.showNotification('Failed to create bulk keys', 'error');
        }
    }

    async extendExpiring() {
        const expiringKeys = this.keys.filter(key => {
            const expiresAt = key.expiresAt ? new Date(key.expiresAt) : null;
            const now = new Date();
            return expiresAt && expiresAt > now && (expiresAt - now) <= (7 * 24 * 60 * 60 * 1000);
        });
        
        if (expiringKeys.length === 0) {
            this.showNotification('No expiring keys found', 'info');
            return;
        }
        
        const duration = prompt(`Extend ${expiringKeys.length} expiring keys by:`, '1month');
        if (!duration) return;
        
        try {
            const promises = expiringKeys.map(key =>
                fetch('/api/admin/extend-license', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ licenseKey: key.key, duration })
                })
            );
            
            await Promise.all(promises);
            this.showNotification(`Extended ${expiringKeys.length} keys successfully!`, 'success');
            await this.refreshData();
        } catch (error) {
            console.error('Failed to extend expiring keys:', error);
            this.showNotification('Failed to extend expiring keys', 'error');
        }
    }

    async deleteExpired() {
        const expiredKeys = this.keys.filter(key => {
            const expiresAt = key.expiresAt ? new Date(key.expiresAt) : null;
            return expiresAt && expiresAt <= new Date();
        });
        
        if (expiredKeys.length === 0) {
            this.showNotification('No expired keys found', 'info');
            return;
        }
        
        if (!confirm(`Delete ${expiredKeys.length} expired keys? This cannot be undone.`)) return;
        
        try {
            const promises = expiredKeys.map(key =>
                fetch('/api/admin/delete-license', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ licenseKey: key.key })
                })
            );
            
            await Promise.all(promises);
            this.showNotification(`Deleted ${expiredKeys.length} expired keys!`, 'success');
            await this.refreshData();
        } catch (error) {
            console.error('Failed to delete expired keys:', error);
            this.showNotification('Failed to delete expired keys', 'error');
        }
    }

    exportKeys() {
        const exportData = {
            exportDate: new Date().toISOString(),
            totalKeys: this.keys.length,
            keys: this.keys.map(key => ({
                key: key.key,
                duration: key.duration,
                createdAt: key.createdAt,
                expiresAt: key.expiresAt,
                hardwareId: key.hardwareId,
                notes: key.notes
            }))
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `octane-keys-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showNotification('Keys exported successfully!', 'success');
    }

    async refreshData() {
        await this.loadKeys();
        this.updateStats();
        this.filterKeys();
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            animation: slideIn 0.3s ease;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            case 'warning':
                notification.style.background = '#ffc107';
                notification.style.color = '#000';
                break;
            default:
                notification.style.background = '#17a2b8';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize the key manager
const keyManager = new KeyManager();

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
