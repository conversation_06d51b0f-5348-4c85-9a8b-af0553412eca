const express = require('express');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Standard Express middleware for JSON and URL-encoded data
app.use(express.json({
    limit: '1mb',
    strict: true
}));

app.use(express.urlencoded({
    extended: false, // Prevent prototype pollution
    limit: '1mb',
    parameterLimit: 20
}));

// Security headers with relaxed CSP for external resources
app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://fonts.googleapis.com https://apis.google.com https://securetoken.googleapis.com https://identitytoolkit.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://apis.google.com;");
    next();
});

// Input validation and sanitization
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    // Remove potential SQL injection patterns
    const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
        /['"`;\\]/g,
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi
    ];

    let sanitized = input;
    sqlPatterns.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '');
    });

    return sanitized.trim();
}

// Validate license key format
function isValidLicenseKey(key) {
    return /^[A-Z0-9]{3}-[A-Z0-9]{3}-[A-Z0-9]{3}$/.test(key);
}

// Validate hardware ID format
function isValidHardwareId(hwid) {
    return /^[A-Za-z0-9\-_]{8,64}$/.test(hwid);
}

// CORS middleware
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Request logging
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    console.log(`[${timestamp}] ${req.method} ${req.url} - IP: ${ip}`);
    next();
});

// Enhanced rate limiting with IP tracking
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // 3 attempts per window (more strict)
    message: { success: false, message: 'Too many authentication attempts. Try again in 15 minutes.' },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        console.log(`🚨 Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            success: false,
            message: 'Too many authentication attempts. Try again in 15 minutes.',
            retryAfter: Math.ceil(req.rateLimit.resetTime / 1000)
        });
    }
});

const apiLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 20, // 20 requests per minute (more strict)
    message: { success: false, message: 'Too many requests. Please slow down.' },
    standardHeaders: true,
    legacyHeaders: false
});

// Admin endpoint limiter (more restrictive)
const adminLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 10, // 10 admin requests per 5 minutes
    message: { success: false, message: 'Too many admin requests' }
});

// Initialize SQLite database
const dbPath = path.join(__dirname, 'database', 'octane.db');
let db;

// Ensure database directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// Initialize database
db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ SQLite connection error:', err);
    } else {
        console.log('✅ Connected to SQLite database');
        initializeTables();
    }
});

function initializeTables() {
    // Create licenses table
    db.run(`
        CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            duration TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME,
            hardware_id TEXT,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1
        )
    `);

    // Create security_events table
    db.run(`
        CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            severity TEXT NOT NULL,
            description TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            username TEXT,
            ip_address TEXT,
            user_agent TEXT,
            additional_data TEXT
        )
    `);

    // Create discord_config table
    db.run(`
        CREATE TABLE IF NOT EXISTS discord_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);

    console.log('✅ Database tables initialized');
}

// Helper functions
function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 9; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result.slice(0, 3)}-${result.slice(3, 6)}-${result.slice(6, 9)}`;
}

function calculateExpiryDate(duration) {
    const now = new Date();
    
    switch (duration.toLowerCase()) {
        case '1hour':
            return new Date(now.getTime() + 60 * 60 * 1000);
        case '1day':
            return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        case '1week':
            return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        case '1month':
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        case '3months':
            return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        case '6months':
            return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
        case '1year':
            return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        case 'lifetime':
            return null;
        default:
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
}

// Maintenance mode check
function checkMaintenanceMode(req, res, next) {
    const maintenanceFile = path.join(__dirname, 'maintenance.flag');
    
    if (fs.existsSync(maintenanceFile)) {
        return res.status(503).json({
            success: false,
            message: 'System is currently under maintenance. Please try again later.',
            error: 'MAINTENANCE_MODE',
            maintenanceMode: true
        });
    }
    
    next();
}

// Discord webhook function with robust error handling
async function sendDiscordWebhook(webhookUrl, message) {
    try {
        if (!webhookUrl) {
            console.error('No webhook URL provided');
            return false;
        }

        console.log(`Sending webhook to: ${webhookUrl.substring(0, 50)}...`);

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Octane-Auth-Bot/1.0'
            },
            body: JSON.stringify(message)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Webhook failed: ${response.status} ${response.statusText} - ${errorText}`);
            return false;
        }

        console.log('✅ Webhook sent successfully');
        return true;
    } catch (error) {
        console.error('Discord webhook error:', error);
        return false;
    }
}

// Test all webhooks function
async function testAllWebhooks() {
    const webhooks = {
        'Security Alerts': process.env.SECURITY_WEBHOOK || '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
        'ESP32 Errors': process.env.ESP32_WEBHOOK || '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
        'Backend Errors': process.env.BACKEND_WEBHOOK || '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
    };

    const results = {};

    for (const [name, webhook] of Object.entries(webhooks)) {
        const fullUrl = webhook.startsWith('http') ? webhook : `https://discord.com/api/webhooks/${webhook}`;

        const testMessage = {
            embeds: [{
                title: `🧪 Webhook Test - ${name}`,
                description: `Testing webhook connectivity for ${name}`,
                color: 0x4776E6,
                fields: [
                    {
                        name: 'Test Time',
                        value: new Date().toISOString(),
                        inline: true
                    },
                    {
                        name: 'System',
                        value: 'Octane Auth Server',
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString()
            }]
        };

        const success = await sendDiscordWebhook(fullUrl, testMessage);
        results[name] = success;
    }

    return results;
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1',
        database: 'sqlite'
    });
});

// Test endpoint removed for security

// License validation endpoint with security hardening
app.post('/api/validate', authLimiter, checkMaintenanceMode, (req, res) => {
    try {
        let { licenseKey, hardwareId } = req.body;

        // Input validation and sanitization
        if (!licenseKey || typeof licenseKey !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Valid license key is required'
            });
        }

        if (!hardwareId || typeof hardwareId !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Valid hardware ID is required'
            });
        }

        // Sanitize inputs
        licenseKey = sanitizeInput(licenseKey);
        hardwareId = sanitizeInput(hardwareId);

        // Validate formats
        if (!isValidLicenseKey(licenseKey)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid license key format'
            });
        }

        if (!isValidHardwareId(hardwareId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid hardware ID format'
            });
        }

        // Log security event
        db.run(
            `INSERT INTO security_events (type, severity, description, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?)`,
            ['license_validation', 'info', `License validation attempt: ${licenseKey.substring(0, 8)}...`, req.ip, req.get('User-Agent')]
        );

        // Validate license
        db.get(
            `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
            [licenseKey],
            (err, row) => {
                if (err) {
                    console.error('Database error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Internal server error'
                    });
                }

                if (!row) {
                    return res.status(401).json({
                        success: false,
                        message: 'Invalid license key'
                    });
                }

                // Check if expired
                if (row.expires_at && new Date(row.expires_at) <= new Date()) {
                    return res.status(401).json({
                        success: false,
                        message: 'License has expired'
                    });
                }

                // Check hardware binding
                if (row.hardware_id && row.hardware_id !== hardwareId) {
                    return res.status(401).json({
                        success: false,
                        message: 'License is bound to different hardware'
                    });
                }

                // Bind to hardware if not already bound
                if (!row.hardware_id) {
                    db.run(
                        `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                        [hardwareId, licenseKey]
                    );
                }

                const remainingTime = row.expires_at ? 
                    Math.max(0, new Date(row.expires_at) - new Date()) : 
                    null;

                res.json({
                    success: true,
                    message: 'License valid',
                    remainingTime: remainingTime ? Math.floor(remainingTime / 1000) : null,
                    expiresAt: row.expires_at
                });
            }
        );
    } catch (error) {
        console.error('License validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Apply rate limiting to API routes
app.use('/api', apiLimiter);

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    db.all(
        `SELECT * FROM licenses ORDER BY created_at DESC`,
        [],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

app.post('/api/admin/create-license', (req, res) => {
    try {
        const { duration, notes } = req.body;

        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);

        db.run(
            `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
            [key, duration, expiresAt, notes || ''],
            function(err) {
                if (err) {
                    console.error('Create license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to create license'
                    });
                }

                // Log to Discord
                const userInfo = req.user || { email: 'Unknown', uid: 'Unknown' };
                const discordMessage = {
                    embeds: [{
                        title: "🎫 New License Created",
                        color: 0x00ff00,
                        fields: [
                            { name: "License Key", value: `\`${key}\``, inline: true },
                            { name: "Duration", value: duration, inline: true },
                            { name: "Created By", value: userInfo.email || 'Website Admin', inline: true },
                            { name: "User ID", value: userInfo.uid || 'N/A', inline: true },
                            { name: "Expires", value: new Date(expiresAt).toLocaleString(), inline: false },
                            { name: "Notes", value: notes || 'None', inline: false }
                        ],
                        timestamp: new Date().toISOString()
                    }]
                };

                // Send to Discord webhook
                fetch('https://discord.com/api/webhooks/1398506465882411079/49oCzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(discordMessage)
                }).catch(err => console.error('Discord webhook error:', err));

                res.json({
                    success: true,
                    license: {
                        id: this.lastID,
                        key: key,
                        duration: duration,
                        expiresAt: expiresAt,
                        notes: notes || ''
                    }
                });
            }
        );
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create license'
        });
    }
});

app.post('/api/admin/delete-license', (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `DELETE FROM licenses WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Delete license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to delete license'
                    });
                }

                if (this.changes > 0) {
                    res.json({
                        success: true,
                        message: 'License deleted successfully'
                    });
                } else {
                    res.status(404).json({
                        success: false,
                        message: 'License not found'
                    });
                }
            }
        );
    } catch (error) {
        console.error('Delete license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete license'
        });
    }
});

app.post('/api/admin/reset-hwid', (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Reset hardware ID error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to reset hardware ID'
                    });
                }

                if (this.changes > 0) {
                    res.json({
                        success: true,
                        message: 'Hardware ID reset successfully'
                    });
                } else {
                    res.status(404).json({
                        success: false,
                        message: 'License not found'
                    });
                }
            }
        );
    } catch (error) {
        console.error('Reset hardware ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset hardware ID'
        });
    }
});

// Discord management endpoints
app.get('/api/admin/discord-status', (req, res) => {
    res.json({
        success: true,
        status: 'online',
        botName: 'authentication#3048',
        connected: true,
        uptime: process.uptime()
    });
});

app.post('/api/admin/test-discord', async (req, res) => {
    try {
        console.log('Testing Discord webhooks...');
        const results = await testAllWebhooks();

        const successCount = Object.values(results).filter(r => r).length;
        const totalCount = Object.keys(results).length;

        res.json({
            success: successCount > 0,
            message: `Webhook test completed: ${successCount}/${totalCount} webhooks working`,
            results: results
        });
    } catch (error) {
        console.error('Discord test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test Discord webhooks',
            error: error.message
        });
    }
});

app.post('/api/admin/test-webhook', async (req, res) => {
    try {
        const { webhookType } = req.body;

        const webhooks = {
            'security': process.env.SECURITY_WEBHOOK || '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
            'esp32': process.env.ESP32_WEBHOOK || '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
            'backend': process.env.BACKEND_WEBHOOK || '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
        };

        const webhook = webhooks[webhookType];
        if (!webhook) {
            return res.status(400).json({
                success: false,
                message: 'Invalid webhook type'
            });
        }

        const fullUrl = webhook.startsWith('http') ? webhook : `https://discord.com/api/webhooks/${webhook}`;

        const testMessage = {
            embeds: [{
                title: `🧪 ${webhookType.toUpperCase()} Webhook Test`,
                description: `Testing ${webhookType} webhook from admin panel`,
                color: 0x4776E6,
                fields: [
                    {
                        name: 'Test Time',
                        value: new Date().toLocaleString(),
                        inline: true
                    },
                    {
                        name: 'Webhook Type',
                        value: webhookType.toUpperCase(),
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString()
            }]
        };

        const success = await sendDiscordWebhook(fullUrl, testMessage);

        res.json({
            success: success,
            message: success ? 'Webhook test message sent successfully' : 'Webhook test failed'
        });
    } catch (error) {
        console.error('Webhook test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test webhook',
            error: error.message
        });
    }
});

app.post('/api/admin/send-daily-report', (req, res) => {
    // Generate daily report
    db.get(
        `SELECT COUNT(*) as total FROM licenses`,
        [],
        (err, licenseCount) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Failed to generate report'
                });
            }

            const reportMessage = {
                embeds: [{
                    title: '📊 Daily Octane Report',
                    description: 'System status and statistics',
                    color: 0x4776E6,
                    fields: [
                        {
                            name: 'Total Licenses',
                            value: licenseCount.total.toString(),
                            inline: true
                        },
                        {
                            name: 'System Uptime',
                            value: `${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m`,
                            inline: true
                        },
                        {
                            name: 'Memory Usage',
                            value: `${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`,
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString()
                }]
            };

            sendDiscordWebhook(process.env.BACKEND_ERROR_WEBHOOK, reportMessage);

            res.json({
                success: true,
                message: 'Daily report sent to Discord'
            });
        }
    );
});

app.post('/api/admin/restart-discord-bot', (req, res) => {
    res.json({
        success: true,
        message: 'Discord bot restart initiated'
    });
});

app.get('/api/admin/discord-config', (req, res) => {
    db.get(
        `SELECT token FROM discord_config ORDER BY updated_at DESC LIMIT 1`,
        [],
        (err, row) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Failed to get Discord config'
                });
            }

            res.json({
                success: true,
                token: row ? row.token : process.env.DISCORD_TOKEN
            });
        }
    );
});

app.post('/api/admin/update-discord-config', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    db.run(
        `INSERT INTO discord_config (token) VALUES (?)`,
        [token],
        function(err) {
            if (err) {
                console.error('Update Discord config error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to update Discord config'
                });
            }

            res.json({
                success: true,
                message: 'Discord config updated successfully'
            });
        }
    );
});

app.post('/api/admin/update-discord-token', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    // Update environment variable (for current session)
    process.env.DISCORD_TOKEN = token;

    // Save to database
    db.run(
        `INSERT INTO discord_config (token) VALUES (?)`,
        [token],
        function(err) {
            if (err) {
                console.error('Update Discord token error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to update Discord token'
                });
            }

            res.json({
                success: true,
                message: 'Discord token updated successfully'
            });
        }
    );
});

// Security endpoints
app.get('/api/security/events', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    db.all(
        `SELECT * FROM security_events ORDER BY timestamp DESC LIMIT ? OFFSET ?`,
        [limit, offset],
        (err, rows) => {
            if (err) {
                console.error('Get security events error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve security events'
                });
            }

            // Get total count
            db.get(
                `SELECT COUNT(*) as total FROM security_events`,
                [],
                (countErr, countRow) => {
                    if (countErr) {
                        console.error('Count error:', countErr);
                        return res.status(500).json({
                            success: false,
                            message: 'Failed to count security events'
                        });
                    }

                    res.json({
                        success: true,
                        events: rows || [],
                        pagination: {
                            page: page,
                            limit: limit,
                            total: countRow ? countRow.total : 0,
                            pages: Math.ceil((countRow ? countRow.total : 0) / limit)
                        }
                    });
                }
            );
        }
    );
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'key-maintenance.html'));
});

app.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'reminders.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

app.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-alerts.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Octane Auth Server running on port ${PORT}`);
    console.log(`🌐 Admin Panel: http://localhost:${PORT}/admin`);
    console.log(`💾 Database: SQLite (lightweight)`);
    console.log(`🚀 Ready for development`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    if (db) db.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    if (db) db.close();
    process.exit(0);
});
