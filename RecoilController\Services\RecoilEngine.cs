using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using RecoilController.Models;
using System.Runtime.InteropServices;
using System.Diagnostics;

namespace RecoilController.Services
{
    /// <summary>
    /// Advanced recoil compensation engine with humanisation and security features
    /// </summary>
    public class RecoilEngine : IDisposable
    {
        private readonly RecoilSettings _settings;
        private readonly Random _random;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _recoilTask;
        private Task _antiAfkTask;
        private bool _isActive = false;
        private bool _isFiring = false;
        private int _currentBullet = 0;
        private DateTime _lastShotTime = DateTime.MinValue;
        private RustWeapon _currentWeapon;

        // Windows API for mouse control and cursor detection
        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern short GetAsyncKeyState(int vKey);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, System.Text.StringBuilder text, int count);

        [DllImport("user32.dll")]
        private static extern bool ShowCursor(bool bShow);

        [DllImport("user32.dll")]
        private static extern CURSORINFO GetCursorInfo();

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct CURSORINFO
        {
            public int cbSize;
            public int flags;
            public IntPtr hCursor;
            public POINT ptScreenPos;
        }

        // Virtual key codes
        private const int VK_LBUTTON = 0x01;
        private const int VK_RBUTTON = 0x02;
        private const int VK_CONTROL = 0x11;

        public event EventHandler<string> StatusChanged;
        public event EventHandler<RecoilEventArgs> RecoilExecuted;

        public RecoilEngine(RecoilSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _random = new Random();
            _settings.PropertyChanged += OnSettingsChanged;
        }

        public bool IsActive => _isActive;
        public bool IsFiring => _isFiring;
        public int CurrentBullet => _currentBullet;

        public async Task StartAsync()
        {
            if (_isActive) return;

            _cancellationTokenSource = new CancellationTokenSource();
            _isActive = true;

            // Start main recoil monitoring task
            _recoilTask = Task.Run(async () => await RecoilMonitorLoop(_cancellationTokenSource.Token));

            // Start anti-AFK task if enabled
            if (_settings.AntiAfkEnabled)
            {
                _antiAfkTask = Task.Run(async () => await AntiAfkLoop(_cancellationTokenSource.Token));
            }

            StatusChanged?.Invoke(this, "Recoil engine started");
            await Task.CompletedTask;
        }

        public async Task StopAsync()
        {
            if (!_isActive) return;

            _isActive = false;
            _cancellationTokenSource?.Cancel();

            try
            {
                await Task.WhenAll(_recoilTask ?? Task.CompletedTask, _antiAfkTask ?? Task.CompletedTask);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancelling
            }

            StatusChanged?.Invoke(this, "Recoil engine stopped");
        }

        private async Task RecoilMonitorLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if cursor is visible (in menu)
                    if (_settings.CursorCheckEnabled && IsCursorVisible())
                    {
                        await Task.Delay(50, cancellationToken);
                        continue;
                    }

                    // Check if Rust is the active window
                    if (!IsRustActive())
                    {
                        await Task.Delay(100, cancellationToken);
                        continue;
                    }

                    // Update current weapon
                    _currentWeapon = RustWeaponsData.GetWeapon(_settings.SelectedWeapon);
                    if (_currentWeapon == null)
                    {
                        await Task.Delay(100, cancellationToken);
                        continue;
                    }

                    // Check firing state
                    bool isLeftMouseDown = (GetAsyncKeyState(VK_LBUTTON) & 0x8000) != 0;
                    bool isRightMouseDown = (GetAsyncKeyState(VK_RBUTTON) & 0x8000) != 0;

                    _settings.IsAiming = isRightMouseDown;

                    if (isLeftMouseDown && _settings.RecoilEnabled)
                    {
                        await HandleFiring(cancellationToken);
                    }
                    else
                    {
                        HandleStopFiring();
                    }

                    // Handle rapidfire for semi-auto weapons
                    if (_settings.RapidfireEnabled && isLeftMouseDown && !_currentWeapon.IsAutomatic)
                    {
                        await HandleRapidfire(cancellationToken);
                    }

                    await Task.Delay(1, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    StatusChanged?.Invoke(this, $"Error in recoil loop: {ex.Message}");
                    await Task.Delay(100, cancellationToken);
                }
            }
        }

        private async Task HandleFiring(CancellationToken cancellationToken)
        {
            if (!_isFiring)
            {
                _isFiring = true;
                _currentBullet = 0;
                _lastShotTime = DateTime.Now;
                StatusChanged?.Invoke(this, $"Started firing {_currentWeapon.DisplayName}");
            }

            var timeSinceLastShot = DateTime.Now - _lastShotTime;
            var effectiveDelay = _currentWeapon.RepeatDelay * _settings.EffectiveDelayMultiplier;

            // Add humanisation timing variation
            if (_settings.HumanisationEnabled)
            {
                var variation = _random.NextDouble() * _settings.Humanisation.TimingVariation;
                effectiveDelay += (variation - _settings.Humanisation.TimingVariation / 2);
            }

            if (timeSinceLastShot.TotalMilliseconds >= effectiveDelay)
            {
                await ExecuteRecoilCompensation(cancellationToken);
                _currentBullet++;
                _lastShotTime = DateTime.Now;
            }
        }

        private void HandleStopFiring()
        {
            if (_isFiring)
            {
                _isFiring = false;
                _currentBullet = 0;
                StatusChanged?.Invoke(this, "Stopped firing");
            }
        }

        private async Task ExecuteRecoilCompensation(CancellationToken cancellationToken)
        {
            if (_currentWeapon?.Recoil == null) return;

            // Get recoil delta for current bullet
            bool useADS = _settings.IsAiming && _currentWeapon.HasADS;
            if (!useADS && !_settings.HipfireEnabled) return;

            var recoilDelta = _currentWeapon.Recoil.GetDeltaMovement(_currentBullet, useADS);

            // Apply sensitivity and FOV conversion
            var multiplier = CalculateRecoilMultiplier();
            var pixelX = recoilDelta.X / multiplier * _settings.EffectiveHorizontalMultiplier;
            var pixelY = recoilDelta.Y / multiplier * _settings.EffectiveVerticalMultiplier;

            // Apply humanisation
            if (_settings.HumanisationEnabled)
            {
                var humanisedMovement = ApplyHumanisation(pixelX, pixelY);
                pixelX = humanisedMovement.X;
                pixelY = humanisedMovement.Y;
            }

            // Apply randomisation
            if (_settings.RandomisationEnabled)
            {
                var randomisedMovement = ApplyRandomisation(pixelX, pixelY);
                pixelX = randomisedMovement.X;
                pixelY = randomisedMovement.Y;
            }

            // Execute mouse movement
            if (Math.Abs(pixelX) > 0.1 || Math.Abs(pixelY) > 0.1)
            {
                await ExecuteMouseMovement((int)Math.Round(pixelX), (int)Math.Round(pixelY), cancellationToken);
            }

            RecoilExecuted?.Invoke(this, new RecoilEventArgs
            {
                BulletNumber = _currentBullet + 1,
                WeaponName = _currentWeapon.DisplayName,
                MovementX = (int)Math.Round(pixelX),
                MovementY = (int)Math.Round(pixelY),
                IsADS = useADS
            });
        }

        private double CalculateRecoilMultiplier()
        {
            // Rust's recoil calculation formula
            return -0.03 * (_settings.Sensitivity * 3.0) * (_settings.FOV / 100.0);
        }

        private RecoilPoint ApplyHumanisation(double x, double y)
        {
            var variation = _settings.Humanisation.MovementVariation;
            var smoothing = _settings.Humanisation.SmoothingFactor;

            // Add slight movement variation
            var xVariation = (_random.NextDouble() - 0.5) * variation;
            var yVariation = (_random.NextDouble() - 0.5) * variation;

            // Apply smoothing
            x = x * smoothing + xVariation * (1 - smoothing);
            y = y * smoothing + yVariation * (1 - smoothing);

            // Add micro-adjustments
            if (_settings.Humanisation.MicroAdjustments && _random.NextDouble() < 0.3)
            {
                x += (_random.NextDouble() - 0.5) * 0.5;
                y += (_random.NextDouble() - 0.5) * 0.5;
            }

            return new RecoilPoint(x, y);
        }

        private RecoilPoint ApplyRandomisation(double x, double y)
        {
            var hDev = _settings.Randomisation.HorizontalDeviation;
            var vDev = _settings.Randomisation.VerticalDeviation;

            // Apply random deviations
            x += (_random.NextDouble() - 0.5) * hDev;
            y += (_random.NextDouble() - 0.5) * vDev;

            // Adaptive randomisation based on bullet count
            if (_settings.Randomisation.AdaptiveRandomisation && _currentBullet > 5)
            {
                var adaptiveFactor = Math.Min(1.5, 1.0 + (_currentBullet - 5) * 0.1);
                x *= adaptiveFactor;
                y *= adaptiveFactor;
            }

            return new RecoilPoint(x, y);
        }

        private async Task ExecuteMouseMovement(int deltaX, int deltaY, CancellationToken cancellationToken)
        {
            GetCursorPos(out POINT currentPos);
            SetCursorPos(currentPos.X + deltaX, currentPos.Y + deltaY);
            await Task.Delay(1, cancellationToken);
        }

        private async Task HandleRapidfire(CancellationToken cancellationToken)
        {
            // Simulate rapid clicking for semi-auto weapons
            // This would need to be implemented with proper input simulation
            await Task.Delay(50, cancellationToken);
        }

        private async Task AntiAfkLoop(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _settings.AntiAfkEnabled)
            {
                try
                {
                    await Task.Delay(_settings.AfkInterval, cancellationToken);
                    
                    if (IsRustActive())
                    {
                        // Simulate crouch key press
                        // This would need proper key simulation implementation
                        StatusChanged?.Invoke(this, "Anti-AFK activated");
                    }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }
        }

        private bool IsCursorVisible()
        {
            // Implementation to check if cursor is visible
            return false; // Placeholder
        }

        private bool IsRustActive()
        {
            try
            {
                var foregroundWindow = GetForegroundWindow();
                var windowText = new System.Text.StringBuilder(256);
                GetWindowText(foregroundWindow, windowText, 256);
                return windowText.ToString().ToLower().Contains("rust");
            }
            catch
            {
                return false;
            }
        }

        private void OnSettingsChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // Handle settings changes
            if (e.PropertyName == nameof(RecoilSettings.AntiAfkEnabled))
            {
                if (_settings.AntiAfkEnabled && _isActive && _antiAfkTask?.IsCompleted != false)
                {
                    _antiAfkTask = Task.Run(async () => await AntiAfkLoop(_cancellationTokenSource.Token));
                }
            }
        }

        public void Dispose()
        {
            StopAsync().Wait();
            _cancellationTokenSource?.Dispose();
            _settings.PropertyChanged -= OnSettingsChanged;
        }
    }

    /// <summary>
    /// Event arguments for recoil execution events
    /// </summary>
    public class RecoilEventArgs : EventArgs
    {
        public int BulletNumber { get; set; }
        public string WeaponName { get; set; }
        public int MovementX { get; set; }
        public int MovementY { get; set; }
        public bool IsADS { get; set; }
    }
}
