// Octane Admin Panel - License Management
class AdminPanel {
    constructor() {
        this.licenses = [];
        this.selectedDuration = 'lifetime';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDurationSelector();
    }

    setupEventListeners() {
        // Login form
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', this.handleLogin.bind(this));
        }

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }

        // Create license form
        const createLicenseForm = document.getElementById('createLicenseForm');
        if (createLicenseForm) {
            createLicenseForm.addEventListener('submit', this.handleCreateLicense.bind(this));
        }

        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', this.loadLicenses.bind(this));
        }

        // Discord test button
        const testDiscordBtn = document.getElementById('testDiscordBtn');
        if (testDiscordBtn) {
            testDiscordBtn.addEventListener('click', this.testDiscord.bind(this));
        }

        // Daily report button
        const dailyReportBtn = document.getElementById('dailyReportBtn');
        if (dailyReportBtn) {
            dailyReportBtn.addEventListener('click', this.sendDailyReport.bind(this));
        }

        // Update Discord token button
        const updateDiscordBtn = document.getElementById('updateDiscordBtn');
        if (updateDiscordBtn) {
            updateDiscordBtn.addEventListener('click', this.showDiscordTokenModal.bind(this));
        }

        // Discord token form
        const discordTokenForm = document.getElementById('discordTokenForm');
        if (discordTokenForm) {
            discordTokenForm.addEventListener('submit', this.updateDiscordToken.bind(this));
        }

        // Cancel token update
        const cancelTokenUpdate = document.getElementById('cancelTokenUpdate');
        if (cancelTokenUpdate) {
            cancelTokenUpdate.addEventListener('click', this.hideDiscordTokenModal.bind(this));
        }

        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Key maintenance
        this.setupKeyMaintenance();
    }

    setupDurationSelector() {
        const durationButtons = document.querySelectorAll('.duration-btn');
        durationButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Remove active class from all buttons
                durationButtons.forEach(b => b.classList.remove('active'));
                
                // Add active class to clicked button
                e.target.classList.add('active');
                
                // Update selected duration
                this.selectedDuration = e.target.dataset.duration;
                
                // Update custom date field visibility
                const customDateGroup = document.getElementById('customDateGroup');
                if (this.selectedDuration === 'custom') {
                    customDateGroup.classList.remove('hidden');
                } else {
                    customDateGroup.classList.add('hidden');
                }
            });
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const loginBtn = document.getElementById('loginBtn');
        const loginError = document.getElementById('loginError');
        
        if (!email || !password) {
            this.showLoginError('Please enter both email and password');
            return;
        }
        
        // Show loading state
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<span class="loading"></span> Signing in...';
        
        try {
            const result = await window.firebaseAuth.signInWithEmail(email, password);
            
            if (result.success) {
                this.showAlert('Login successful!', 'success');
            } else {
                this.showLoginError(result.error);
            }
        } catch (error) {
            this.showLoginError('Login failed. Please try again.');
        } finally {
            loginBtn.disabled = false;
            loginBtn.textContent = 'Login with Firebase';
        }
    }

    async handleLogout() {
        try {
            const result = await window.firebaseAuth.signOut();
            if (result.success) {
                this.showAlert('Logged out successfully', 'success');
            } else {
                this.showAlert('Logout failed: ' + result.error, 'error');
            }
        } catch (error) {
            this.showAlert('Logout failed', 'error');
        }
    }

    async handleCreateLicense(e) {
        e.preventDefault();
        
        const notes = document.getElementById('notes').value;
        const createBtn = document.querySelector('#createLicenseForm .btn');
        
        // We send duration directly to the backend
        
        // Show loading state
        createBtn.disabled = true;
        createBtn.innerHTML = '<span class="loading"></span> Creating License...';
        
        try {
            const response = await fetch('/api/admin/licenses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({
                    duration: this.selectedDuration,
                    notes: notes || `${this.getDurationLabel()} license created via admin panel`
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showAlert(`License created successfully: ${data.license.license_key}`, 'success');
                document.getElementById('createLicenseForm').reset();
                this.resetDurationSelector();
                this.loadLicenses();
            } else {
                this.showAlert(data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Failed to create license: ' + error.message, 'error');
        } finally {
            createBtn.disabled = false;
            createBtn.textContent = 'Create License';
        }
    }

    calculateExpiryDate(duration) {
        const now = new Date();
        
        switch (duration) {
            case '1hour':
                return new Date(now.getTime() + 60 * 60 * 1000).toISOString();
            case '1day':
                return new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString();
            case '1week':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString();
            case '1month':
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString();
            case '3months':
                return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000).toISOString();
            case '6months':
                return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000).toISOString();
            case '1year':
                return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString();
            case 'custom':
                const customDate = document.getElementById('customDate').value;
                return customDate ? new Date(customDate).toISOString() : null;
            default:
                return null;
        }
    }

    getDurationLabel() {
        const labels = {
            '1hour': '1 Hour',
            '1day': '1 Day',
            '1week': '1 Week',
            '1month': '1 Month',
            '3months': '3 Months',
            '6months': '6 Months',
            '1year': '1 Year',
            'lifetime': 'Lifetime'
        };
        return labels[this.selectedDuration] || 'Unknown';
    }

    resetDurationSelector() {
        // Reset to lifetime
        document.querySelectorAll('.duration-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector('[data-duration="lifetime"]').classList.add('active');
        this.selectedDuration = 'lifetime';
        document.getElementById('customDateGroup').classList.add('hidden');
    }

    async loadLicenses() {
        try {
            const response = await fetch('/api/admin/licenses', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.licenses = data.licenses;
                this.displayLicenses(data.licenses);
                this.updateStats(data.licenses);
            } else {
                this.showAlert(data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Failed to load licenses: ' + error.message, 'error');
        }
    }

    displayLicenses(licenses) {
        const tbody = document.getElementById('licensesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        licenses.forEach(license => {
            const row = document.createElement('tr');
            
            const status = this.getLicenseStatus(license);
            const expiryText = license.expires_at 
                ? new Date(license.expires_at).toLocaleDateString() 
                : 'Never';
            
            row.innerHTML = `
                <td><code class="license-key">${license.license_key}</code></td>
                <td>${new Date(license.created_at).toLocaleDateString()}</td>
                <td>${expiryText}</td>
                <td><span class="status-badge status-${status.class}">${status.text}</span></td>
                <td>${license.hardware_id ? 'Bound' : 'Unbound'}</td>
                <td>${license.usage_count || 0}</td>
                <td>${license.notes || '-'}</td>
                <td>
                    <div class="actions">
                        <button onclick="adminPanel.resetHardwareId('${license.license_key}')" class="btn btn-secondary btn-sm">Reset HWID</button>
                        <button onclick="adminPanel.toggleLicense('${license.license_key}', ${license.active})" class="btn ${license.active ? 'btn-danger' : 'btn-success'} btn-sm">
                            ${license.active ? 'Deactivate' : 'Activate'}
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    getLicenseStatus(license) {
        if (!license.active) {
            return { class: 'inactive', text: 'Inactive' };
        }
        
        if (license.expires_at) {
            const now = new Date();
            const expiry = new Date(license.expires_at);
            
            if (now > expiry) {
                return { class: 'expired', text: 'Expired' };
            }
        }
        
        return { class: 'active', text: 'Active' };
    }

    updateStats(licenses) {
        const total = licenses.length;
        const active = licenses.filter(l => l.active && (!l.expires_at || new Date(l.expires_at) > new Date())).length;
        const bound = licenses.filter(l => l.hardware_id).length;
        const expired = licenses.filter(l => l.expires_at && new Date(l.expires_at) <= new Date()).length;
        
        document.getElementById('totalLicenses').textContent = total;
        document.getElementById('activeLicenses').textContent = active;
        document.getElementById('boundLicenses').textContent = bound;
        document.getElementById('expiredLicenses').textContent = expired;
    }

    async resetHardwareId(licenseKey) {
        if (!confirm('Reset hardware ID for this license?')) return;
        
        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}/reset-hwid`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showAlert('Hardware ID reset successfully', 'success');
                this.loadLicenses();
            } else {
                this.showAlert(data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Failed to reset hardware ID: ' + error.message, 'error');
        }
    }

    async toggleLicense(licenseKey, isActive) {
        const action = isActive ? 'deactivate' : 'activate';
        if (!confirm(`${action.charAt(0).toUpperCase() + action.slice(1)} this license?`)) return;
        
        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}/${action}`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showAlert(`License ${action}d successfully`, 'success');
                this.loadLicenses();
            } else {
                this.showAlert(data.message, 'error');
            }
        } catch (error) {
            this.showAlert(`Failed to ${action} license: ` + error.message, 'error');
        }
    }

    loadReminders() {
        // Placeholder for reminders functionality
        console.log('Loading reminders...');
    }

    loadDiscordManagement() {
        // Placeholder for Discord management functionality
        console.log('Loading Discord management...');
    }

    loadSystemStatus() {
        // Placeholder for system status functionality
        console.log('Loading system status...');
    }

    showAlert(message, type) {
        const alertContainer = document.getElementById('alertContainer');
        if (!alertContainer) return;
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type}`;
        alertDiv.textContent = message;
        
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alertDiv);
        
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }

    showLoginError(message) {
        const loginError = document.getElementById('loginError');
        if (!loginError) return;

        loginError.textContent = message;
        loginError.style.display = 'block';

        setTimeout(() => {
            loginError.style.display = 'none';
        }, 5000);
    }

    async testDiscord() {
        const testBtn = document.getElementById('testDiscordBtn');
        const originalText = testBtn.textContent;

        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '<span class="loading"></span> Testing...';

            const response = await fetch('/api/admin/test-discord', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('Discord test successful! Check your DMs.', 'success');
            } else {
                this.showAlert('Discord test failed: ' + data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Discord test failed: ' + error.message, 'error');
        } finally {
            testBtn.disabled = false;
            testBtn.textContent = originalText;
        }
    }

    async sendDailyReport() {
        const reportBtn = document.getElementById('dailyReportBtn');
        const originalText = reportBtn.textContent;

        try {
            reportBtn.disabled = true;
            reportBtn.innerHTML = '<span class="loading"></span> Sending...';

            const response = await fetch('/api/admin/send-daily-report', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('Daily report sent to Discord!', 'success');
            } else {
                this.showAlert('Failed to send daily report: ' + data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Failed to send daily report: ' + error.message, 'error');
        } finally {
            reportBtn.disabled = false;
            reportBtn.textContent = originalText;
        }
    }

    async loadDiscordStatus() {
        try {
            const response = await fetch('/api/admin/discord-status', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                const statusElement = document.getElementById('discordStatus');
                const tagElement = document.getElementById('discordBotTag');

                if (data.status.online) {
                    statusElement.textContent = '✅';
                    statusElement.style.color = '#28a745';
                    tagElement.textContent = data.status.botTag || 'Online';
                } else {
                    statusElement.textContent = '❌';
                    statusElement.style.color = '#dc3545';
                    tagElement.textContent = 'Offline';
                }
            }
        } catch (error) {
            console.error('Failed to load Discord status:', error);
            const statusElement = document.getElementById('discordStatus');
            const tagElement = document.getElementById('discordBotTag');
            statusElement.textContent = '⚠️';
            statusElement.style.color = '#ffc107';
            tagElement.textContent = 'Error';
        }
    }

    showDiscordTokenModal() {
        document.getElementById('discordTokenModal').style.display = 'flex';
    }

    hideDiscordTokenModal() {
        document.getElementById('discordTokenModal').style.display = 'none';
        document.getElementById('discordToken').value = '';
    }

    async updateDiscordToken(e) {
        e.preventDefault();

        const token = document.getElementById('discordToken').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        try {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span> Updating...';

            const response = await fetch('/api/admin/update-discord-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({ token })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('Discord token updated successfully! Bot will reconnect shortly.', 'success');
                this.hideDiscordTokenModal();

                // Reload Discord status after a short delay
                setTimeout(() => {
                    this.loadDiscordStatus();
                }, 3000);
            } else {
                this.showAlert('Failed to update Discord token: ' + data.message, 'error');
            }
        } catch (error) {
            this.showAlert('Failed to update Discord token: ' + error.message, 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        // Load tab-specific data
        if (tabName === 'maintenance') {
            this.loadKeyMaintenance();
        } else if (tabName === 'security') {
            this.loadSecurityData();
        } else if (tabName === 'reminders') {
            this.loadReminders();
        } else if (tabName === 'discord') {
            this.loadDiscordManagement();
        } else if (tabName === 'system') {
            this.loadSystemStatus();

        }
    }

    setupKeyMaintenance() {
        // Search functionality
        document.getElementById('searchKeysBtn')?.addEventListener('click', () => {
            this.searchKeys();
        });

        document.getElementById('keySearch')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchKeys();
            }
        });

        // Filter functionality
        document.getElementById('statusFilter')?.addEventListener('change', () => {
            this.filterKeys();
        });

        document.getElementById('durationFilter')?.addEventListener('change', () => {
            this.filterKeys();
        });

        // Refresh
        document.getElementById('refreshKeysBtn')?.addEventListener('click', () => {
            this.loadKeyMaintenance();
        });

        // Bulk actions
        document.getElementById('selectAllBtn')?.addEventListener('click', () => {
            this.selectAllKeys(true);
        });

        document.getElementById('deselectAllBtn')?.addEventListener('click', () => {
            this.selectAllKeys(false);
        });

        document.getElementById('bulkDeleteBtn')?.addEventListener('click', () => {
            this.bulkDeleteKeys();
        });

        document.getElementById('bulkSuspendBtn')?.addEventListener('click', () => {
            this.bulkSuspendKeys();
        });

        document.getElementById('bulkActivateBtn')?.addEventListener('click', () => {
            this.bulkActivateKeys();
        });
    }

    async loadKeyMaintenance() {
        try {
            const response = await fetch('/api/admin/licenses', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.renderKeysTable(data.licenses);
            } else {
                this.showError('Failed to load licenses');
            }
        } catch (error) {
            this.showError('Failed to load licenses: ' + error.message);
        }
    }

    renderKeysTable(licenses) {
        const container = document.getElementById('keysTableContainer');

        if (licenses.length === 0) {
            container.innerHTML = '<div class="no-data">No license keys found</div>';
            return;
        }

        const now = new Date();

        const tableHTML = `
            <table class="keys-table">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAllCheckbox" class="key-checkbox"></th>
                        <th>License Key</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Created</th>
                        <th>Expires</th>
                        <th>Hardware ID</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${licenses.map(license => {
                        const isExpired = license.expiresAt && new Date(license.expiresAt) <= now;
                        const isSuspended = license.suspended || false;

                        let status = 'active';
                        let statusText = '✅ Active';

                        if (isSuspended) {
                            status = 'suspended';
                            statusText = '⏸️ Suspended';
                        } else if (isExpired) {
                            status = 'expired';
                            statusText = '❌ Expired';
                        }

                        return `
                            <tr data-key="${license.key}">
                                <td><input type="checkbox" class="key-checkbox" data-key="${license.key}"></td>
                                <td class="key-cell" title="${license.key}">${license.key.substring(0, 12)}...</td>
                                <td><span class="key-status ${status}">${statusText}</span></td>
                                <td>${license.duration || 'Unknown'}</td>
                                <td>${new Date(license.createdAt).toLocaleDateString()}</td>
                                <td>${license.expiresAt ? new Date(license.expiresAt).toLocaleDateString() : 'Never'}</td>
                                <td class="key-cell" title="${license.hardwareId || 'Not set'}">${license.hardwareId ? license.hardwareId.substring(0, 8) + '...' : 'Not set'}</td>
                                <td class="key-cell" title="${license.notes || 'None'}">${license.notes || 'None'}</td>
                                <td class="key-actions">
                                    <button class="btn btn-sm btn-warning" onclick="adminPanel.resetHwid('${license.key}')">🔄 Reset HWID</button>
                                    ${isSuspended ?
                                        `<button class="btn btn-sm btn-success" onclick="adminPanel.activateKey('${license.key}')">▶️ Activate</button>` :
                                        `<button class="btn btn-sm btn-warning" onclick="adminPanel.suspendKey('${license.key}')">⏸️ Suspend</button>`
                                    }
                                    <button class="btn btn-sm btn-danger" onclick="adminPanel.deleteKey('${license.key}')">🗑️ Delete</button>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;

        // Setup select all checkbox
        document.getElementById('selectAllCheckbox')?.addEventListener('change', (e) => {
            this.selectAllKeys(e.target.checked);
        });

        // Setup individual checkboxes
        document.querySelectorAll('.key-checkbox[data-key]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCount();
            });
        });

        this.updateSelectedCount();
    }

    selectAllKeys(select) {
        document.querySelectorAll('.key-checkbox[data-key]').forEach(checkbox => {
            checkbox.checked = select;
        });

        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = select;
        }

        this.updateSelectedCount();
    }

    updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.key-checkbox[data-key]:checked');
        const count = selectedCheckboxes.length;

        const countElement = document.getElementById('selectedCount');
        if (countElement) {
            countElement.textContent = `${count} selected`;
        }
    }

    async deleteKey(licenseKey) {
        if (!confirm(`Are you sure you want to delete license key: ${licenseKey}?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('License key deleted successfully');
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to delete license key: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to delete license key: ' + error.message);
        }
    }

    async suspendKey(licenseKey) {
        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}/suspend`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('License key suspended successfully');
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to suspend license key: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to suspend license key: ' + error.message);
        }
    }

    async activateKey(licenseKey) {
        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}/activate`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('License key activated successfully');
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to activate license key: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to activate license key: ' + error.message);
        }
    }

    async resetHwid(licenseKey) {
        if (!confirm(`Are you sure you want to reset the hardware ID for license key: ${licenseKey}?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/licenses/${licenseKey}/reset-hwid`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Hardware ID reset successfully');
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to reset hardware ID: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to reset hardware ID: ' + error.message);
        }
    }

    async bulkDeleteKeys() {
        const selectedKeys = this.getSelectedKeys();

        if (selectedKeys.length === 0) {
            this.showError('No keys selected');
            return;
        }

        if (!confirm(`Are you sure you want to delete ${selectedKeys.length} license keys?`)) {
            return;
        }

        try {
            const response = await fetch('/api/admin/licenses/bulk-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({ keys: selectedKeys })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`${selectedKeys.length} license keys deleted successfully`);
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to delete license keys: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to delete license keys: ' + error.message);
        }
    }

    async bulkSuspendKeys() {
        const selectedKeys = this.getSelectedKeys();

        if (selectedKeys.length === 0) {
            this.showError('No keys selected');
            return;
        }

        try {
            const response = await fetch('/api/admin/licenses/bulk-suspend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({ keys: selectedKeys })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`${selectedKeys.length} license keys suspended successfully`);
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to suspend license keys: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to suspend license keys: ' + error.message);
        }
    }

    async bulkActivateKeys() {
        const selectedKeys = this.getSelectedKeys();

        if (selectedKeys.length === 0) {
            this.showError('No keys selected');
            return;
        }

        try {
            const response = await fetch('/api/admin/licenses/bulk-activate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({ keys: selectedKeys })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`${selectedKeys.length} license keys activated successfully`);
                this.loadKeyMaintenance();
            } else {
                this.showError('Failed to activate license keys: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to activate license keys: ' + error.message);
        }
    }

    getSelectedKeys() {
        const selectedCheckboxes = document.querySelectorAll('.key-checkbox[data-key]:checked');
        return Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.key);
    }

    searchKeys() {
        const searchTerm = document.getElementById('keySearch').value.toLowerCase();
        const rows = document.querySelectorAll('.keys-table tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    filterKeys() {
        const statusFilter = document.getElementById('statusFilter').value;
        const durationFilter = document.getElementById('durationFilter').value;
        const rows = document.querySelectorAll('.keys-table tbody tr');

        rows.forEach(row => {
            let showRow = true;

            // Status filter
            if (statusFilter !== 'all') {
                const statusElement = row.querySelector('.key-status');
                if (!statusElement.classList.contains(statusFilter)) {
                    showRow = false;
                }
            }

            // Duration filter
            if (durationFilter !== 'all' && showRow) {
                const durationCell = row.cells[3].textContent;
                if (durationCell !== durationFilter) {
                    showRow = false;
                }
            }

            row.style.display = showRow ? '' : 'none';
        });
    }

    // Security Monitor functionality
    async loadSecurityData() {
        try {
            const response = await fetch('/api/security/events');
            const data = await response.json();

            this.updateSecurityStats(data.stats);
            this.displaySecurityEvents(data.events);
        } catch (error) {
            console.error('Failed to load security data:', error);
            this.showAlert('Failed to load security data', 'error');
        }
    }

    updateSecurityStats(stats) {
        const safeStats = stats || {};
        const totalElement = document.getElementById('totalSecurityEvents');
        const criticalElement = document.getElementById('criticalThreats');
        const blockedElement = document.getElementById('blockedUsers');
        const activeElement = document.getElementById('activeConnections');

        if (totalElement) totalElement.textContent = safeStats.total || 0;
        if (criticalElement) criticalElement.textContent = safeStats.critical || 0;
        if (blockedElement) blockedElement.textContent = safeStats.blocked || 0;
        if (activeElement) activeElement.textContent = safeStats.active || 0;
    }

    displaySecurityEvents(events) {
        const container = document.getElementById('securityEventsList');

        if (!events || events.length === 0) {
            container.innerHTML = '<div class="alert-item">No recent security events</div>';
            return;
        }

        container.innerHTML = events.slice(0, 20).map(event => `
            <div class="alert-item ${this.getEventClass(event.severity)}">
                <div class="alert-header">
                    <span class="alert-title">${event.type}</span>
                    <span class="alert-time">${new Date(event.timestamp).toLocaleString()}</span>
                </div>
                <div class="alert-description">${event.description}</div>
                <div class="alert-details">
                    <strong>User:</strong> ${event.username || 'Unknown'} |
                    <strong>IP:</strong> ${event.ipAddress || 'Unknown'} |
                    <strong>Hardware:</strong> ${event.hardwareId?.substring(0, 8) || 'Unknown'}...
                </div>
            </div>
        `).join('');
    }

    getEventClass(severity) {
        switch (severity?.toLowerCase()) {
            case 'critical': return 'critical';
            case 'high': return 'high';
            case 'medium': return 'medium';
            case 'low': return 'low';
            default: return 'medium';
        }
    }
}

// Security action functions
async function clearSecurityLog() {
    if (!confirm('Are you sure you want to clear the security log?')) return;

    try {
        const response = await fetch('/api/security/clear-log', { method: 'POST' });
        if (response.ok) {
            window.adminPanel.showAlert('Security log cleared successfully', 'success');
            window.adminPanel.loadSecurityData();
        } else {
            throw new Error('Failed to clear log');
        }
    } catch (error) {
        window.adminPanel.showAlert('Failed to clear security log: ' + error.message, 'error');
    }
}

async function exportSecurityLog() {
    try {
        const response = await fetch('/api/security/export-log');
        const blob = await response.blob();

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `security-log-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        window.adminPanel.showAlert('Security log exported successfully', 'success');
    } catch (error) {
        window.adminPanel.showAlert('Failed to export security log: ' + error.message, 'error');
    }
}

async function blockAllSuspicious() {
    if (!confirm('Are you sure you want to block all suspicious users?')) return;

    try {
        const response = await fetch('/api/security/block-suspicious', { method: 'POST' });
        const result = await response.json();

        if (response.ok) {
            window.adminPanel.showAlert(`Blocked ${result.blocked} suspicious users`, 'success');
            window.adminPanel.loadSecurityData();
        } else {
            throw new Error(result.error || 'Failed to block users');
        }
    } catch (error) {
        window.adminPanel.showAlert('Failed to block suspicious users: ' + error.message, 'error');
    }
}

// Initialize admin panel
window.adminPanel = new AdminPanel();
