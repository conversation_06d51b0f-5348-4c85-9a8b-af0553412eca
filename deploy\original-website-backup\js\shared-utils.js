// Shared Admin Panel Utilities and Components

// Global admin utilities
window.AdminUtils = {
    // API helper functions
    async apiCall(endpoint, options = {}) {
        try {
            const response = await fetch(endpoint, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`API call failed for ${endpoint}:`, error);
            throw error;
        }
    },

    // Notification system
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <i class="fas ${this.getNotificationIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            background: ${this.getNotificationColor(type)};
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    },

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    },

    getNotificationColor(type) {
        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#4776E6'
        };
        return colors[type] || '#4776E6';
    },

    // Time formatting utilities
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString();
    },

    formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    },

    formatDuration(duration) {
        const durations = {
            '1day': '1 Day',
            '1week': '1 Week', 
            '1month': '1 Month',
            '3months': '3 Months',
            '6months': '6 Months',
            '1year': '1 Year',
            'lifetime': 'Lifetime'
        };
        return durations[duration] || duration;
    },

    // Loading states
    showLoading(element, message = 'Loading...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            element.innerHTML = `
                <div class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>${message}</span>
                </div>
            `;
        }
    },

    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        if (element) {
            const loading = element.querySelector('.loading-state');
            if (loading) loading.remove();
        }
    },

    // Data validation
    validateLicenseKey(key) {
        return /^[A-Z0-9]{3}\.[A-Z0-9]{3}\.[A-Z0-9]{3}$/.test(key);
    },

    // Status indicators
    getStatusClass(isActive) {
        return isActive ? 'status-active' : 'status-inactive';
    },

    getStatusText(isActive) {
        return isActive ? 'Active' : 'Inactive';
    },

    // Table utilities
    createTable(data, columns, options = {}) {
        const table = document.createElement('table');
        table.className = 'admin-table';
        
        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        columns.forEach(col => {
            const th = document.createElement('th');
            th.textContent = col.title;
            if (col.width) th.style.width = col.width;
            headerRow.appendChild(th);
        });
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Create body
        const tbody = document.createElement('tbody');
        if (data.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = columns.length;
            cell.className = 'empty-state';
            cell.innerHTML = '<i class="fas fa-inbox"></i><p>No data available</p>';
            row.appendChild(cell);
            tbody.appendChild(row);
        } else {
            data.forEach(item => {
                const row = document.createElement('tr');
                columns.forEach(col => {
                    const cell = document.createElement('td');
                    if (col.render) {
                        cell.innerHTML = col.render(item[col.key], item);
                    } else {
                        cell.textContent = item[col.key] || '-';
                    }
                    row.appendChild(cell);
                });
                tbody.appendChild(row);
            });
        }
        table.appendChild(tbody);
        
        return table;
    },

    // Form utilities
    serializeForm(form) {
        const formData = new FormData(form);
        const data = {};
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        return data;
    },

    // Modal utilities
    showModal(title, content, actions = []) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${actions.map(action => `
                        <button class="btn ${action.class || 'btn-secondary'}" 
                                onclick="${action.onclick}">${action.text}</button>
                    `).join('')}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal handlers
        modal.querySelector('.modal-close').onclick = () => modal.remove();
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };
        
        return modal;
    }
};

// Shared Navigation Component
window.AdminNav = {
    init() {
        this.setupNavigation();
        this.updateActiveNav();
    },

    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                
                // Handle external links
                if (href && href.startsWith('/') && !href.startsWith('/#')) {
                    // Let the browser handle the navigation
                    return;
                }
                
                // Handle hash navigation
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const section = href.substring(1);
                    this.showSection(section);
                    this.updateActiveNav(link);
                }
            });
        });
    },

    showSection(sectionId) {
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });
        
        const targetSection = document.getElementById(sectionId + '-section');
        if (targetSection) {
            targetSection.classList.add('active');
        }
    },

    updateActiveNav(activeLink = null) {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
        });
        
        if (activeLink) {
            activeLink.closest('.nav-item').classList.add('active');
        } else {
            // Auto-detect active nav based on current page
            const currentPath = window.location.pathname;
            navItems.forEach(item => {
                const link = item.querySelector('.nav-link');
                if (link && link.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                }
            });
        }
    }
};

// Add shared CSS styles
const sharedStyles = document.createElement('style');
sharedStyles.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    .loading-state {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        padding: 20px;
        color: var(--text-secondary);
    }
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        color: var(--text-muted);
        text-align: center;
    }
    .empty-state i {
        font-size: 2rem;
        margin-bottom: 10px;
        opacity: 0.5;
    }
    .status-active { color: var(--success-color); }
    .status-inactive { color: var(--error-color); }
    .admin-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
    }
    .admin-table th,
    .admin-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }
    .admin-table th {
        background: var(--dark-bg);
        font-weight: 600;
        color: var(--text-primary);
    }
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
    }
    .modal-content {
        background: var(--card-bg);
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
    }
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid var(--border-color);
    }
    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--text-secondary);
        cursor: pointer;
    }
    .modal-body {
        padding: 20px;
    }
    .modal-footer {
        padding: 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
`;
document.head.appendChild(sharedStyles);
