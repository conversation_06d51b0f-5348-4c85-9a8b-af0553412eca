@echo off
echo.
echo OCTANE AUTHENTICATION BACKEND - DEPLOYMENT
echo ==========================================
echo.

set VPS_IP=*************
set VPS_USER=root
set LOCAL_PATH=%~dp0..
set REMOTE_PATH=/opt/octane-auth
set SSH_KEY=%~dp0octane_key

echo VPS Details:
echo    IP: %VPS_IP%
echo    User: %VPS_USER%
echo    Remote Path: %REMOTE_PATH%
echo.

set /p CONTINUE="Ready to deploy? (y/N): "
if /i not "%CONTINUE%"=="y" (
    echo Deployment cancelled.
    pause
    exit /b
)

echo.
echo STEP 1: Uploading files...
echo ==========================

echo Creating directory on VPS...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "mkdir -p %REMOTE_PATH%"

echo Uploading application files...
echo Excluding node_modules and other unnecessary files...
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\models" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\routes" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\services" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\middleware" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\public" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" -r "%LOCAL_PATH%\database" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\server.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\package.json" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\package-lock.json" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/

if %ERRORLEVEL% NEQ 0 (
    echo Upload failed!
    pause
    exit /b 1
)

echo Files uploaded successfully!

echo.
echo STEP 2: Server setup...
echo ======================

echo Setting up server...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "export DEBIAN_FRONTEND=noninteractive && apt update -y && apt upgrade -y"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "curl -fsSL https://deb.nodesource.com/setup_18.x | bash -"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "apt-get install -y nodejs"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "npm install -g pm2"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "apt install -y nginx"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "ufw --force enable && ufw allow ssh && ufw allow 80 && ufw allow 443"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "useradd --system --shell /bin/bash --home /opt/octane-auth octane || true"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "chown -R octane:octane /opt/octane-auth"

echo.
echo STEP 3: Application setup...
echo ===========================

echo Installing dependencies...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt/octane-auth && npm install --production"

echo Creating environment file...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt/octane-auth && JWT_SECRET=$(openssl rand -base64 32) && ADMIN_PASSWORD=$(openssl rand -base64 12) && echo 'PORT=3000' > .env && echo 'NODE_ENV=production' >> .env && echo 'JWT_SECRET='$JWT_SECRET >> .env && echo 'ADMIN_USERNAME=admin' >> .env && echo 'ADMIN_PASSWORD='$ADMIN_PASSWORD >> .env && echo 'DB_PATH=/opt/octane-auth/database/octane.json' >> .env && echo 'RATE_LIMIT_WINDOW_MS=900000' >> .env && echo 'RATE_LIMIT_MAX_REQUESTS=100' >> .env && echo 'ALLOWED_ORIGINS=http://*************,https://*************' >> .env && echo 'DISCORD_TOKEN=MTM5ODQ2MDg3NjQwMjU5MzkyNA.GdIfgT.LVgfJoKz1CvkVpKj17lBcBNH2b9WgoFNvGmTWY' >> .env && echo 'DISCORD_SERVER_ID=1350622776943444040' >> .env && echo 'DISCORD_ADMIN_USER_ID=1156721390720925828' >> .env && echo 'DISCORD_CHANNEL_ID=1350622776943444043' >> .env"

echo Setting up directories...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "mkdir -p /opt/octane-auth/database /var/log/octane-auth && chown -R octane:octane /opt/octane-auth/database /var/log/octane-auth"

echo Starting application...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt/octane-auth && sudo -u octane pm2 start server.js --name octane-auth"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 save"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "pm2 startup systemd -u octane --hp /opt/octane-auth"

echo.
echo STEP 4: Nginx setup...
echo =====================

echo Configuring Nginx...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "echo 'server { listen 80; server_name *************; location / { proxy_pass http://localhost:3000; proxy_set_header Host \$host; proxy_set_header X-Real-IP \$remote_addr; proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for; } }' > /etc/nginx/sites-available/octane-auth"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "ln -sf /etc/nginx/sites-available/octane-auth /etc/nginx/sites-enabled/ && rm -f /etc/nginx/sites-enabled/default"
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "nginx -t && systemctl reload nginx"

echo.
echo STEP 5: Testing...
echo ================

echo Checking application status...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 status"

echo.
echo Testing API...
curl -s http://%VPS_IP%/api/health

echo.
echo.
echo DEPLOYMENT COMPLETE!
echo ===================
echo.
echo Your backend is running at:
echo    API: http://%VPS_IP%/api
echo    Admin: http://%VPS_IP%/admin
echo.

echo Getting admin credentials...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd %REMOTE_PATH% && echo 'Username: admin' && echo -n 'Password: ' && grep ADMIN_PASSWORD .env | cut -d'=' -f2"

echo.
echo Opening admin panel...
start http://%VPS_IP%/admin

pause