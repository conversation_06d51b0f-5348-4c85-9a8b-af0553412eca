const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const crypto = require('crypto');

class SQLiteDatabase {
    constructor() {
        this.dbPath = path.join(__dirname, '..', 'data', 'octane.db');
        this.db = null;
        this.init();
    }

    init() {
        // Ensure data directory exists
        const fs = require('fs');
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        this.db = new sqlite3.Database(this.dbPath, (err) => {
            if (err) {
                console.error('❌ SQLite connection error:', err);
            } else {
                console.log('✅ Connected to SQLite database');
                this.createTables();
            }
        });
    }

    createTables() {
        // Create licenses table
        this.db.run(`
            CREATE TABLE IF NOT EXISTS licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                duration TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                hardware_id TEXT,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1
            )
        `);

        // Create security_events table
        this.db.run(`
            CREATE TABLE IF NOT EXISTS security_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT NOT NULL,
                severity TEXT NOT NULL,
                description TEXT NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                username TEXT,
                ip_address TEXT,
                user_agent TEXT,
                additional_data TEXT
            )
        `);

        // Create indexes for performance
        this.db.run(`CREATE INDEX IF NOT EXISTS idx_licenses_key ON licenses(key)`);
        this.db.run(`CREATE INDEX IF NOT EXISTS idx_licenses_hardware ON licenses(hardware_id)`);
        this.db.run(`CREATE INDEX IF NOT EXISTS idx_security_timestamp ON security_events(timestamp)`);
    }

    generateLicenseKey() {
        // Generate shorter, more readable license keys (12 characters)
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 12; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        // Format as XXXX-XXXX-XXXX for readability
        return `${result.slice(0, 4)}-${result.slice(4, 8)}-${result.slice(8, 12)}`;
    }

    calculateExpiryDate(duration) {
        const now = new Date();
        
        switch (duration.toLowerCase()) {
            case '1hour':
                return new Date(now.getTime() + 60 * 60 * 1000);
            case '1day':
                return new Date(now.getTime() + 24 * 60 * 60 * 1000);
            case '1week':
                return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            case '1month':
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            case '3months':
                return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
            case '6months':
                return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
            case '1year':
                return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
            case 'lifetime':
                return null;
            default:
                return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // Default 1 month
        }
    }

    async createLicense(duration, notes = '') {
        return new Promise((resolve, reject) => {
            const key = this.generateLicenseKey();
            const expiresAt = this.calculateExpiryDate(duration);
            
            this.db.run(
                `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
                [key, duration, expiresAt, notes],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({
                            id: this.lastID,
                            key: key,
                            duration: duration,
                            expiresAt: expiresAt,
                            notes: notes
                        });
                    }
                }
            );
        });
    }

    async validateLicense(licenseKey, hardwareId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
                [licenseKey],
                (err, row) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (!row) {
                        resolve({ valid: false, message: 'Invalid license key' });
                        return;
                    }

                    // Check if expired
                    if (row.expires_at && new Date(row.expires_at) <= new Date()) {
                        resolve({ valid: false, message: 'License has expired' });
                        return;
                    }

                    // Check hardware binding
                    if (row.hardware_id && row.hardware_id !== hardwareId) {
                        resolve({ valid: false, message: 'License is bound to different hardware' });
                        return;
                    }

                    // Bind to hardware if not already bound
                    if (!row.hardware_id) {
                        this.db.run(
                            `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                            [hardwareId, licenseKey],
                            (updateErr) => {
                                if (updateErr) {
                                    console.error('Failed to bind hardware:', updateErr);
                                }
                            }
                        );
                    }

                    const remainingTime = row.expires_at ? 
                        Math.max(0, new Date(row.expires_at) - new Date()) : 
                        null;

                    resolve({
                        valid: true,
                        message: 'License valid',
                        remainingTime: remainingTime,
                        expiresAt: row.expires_at
                    });
                }
            );
        });
    }

    async getAllLicenses() {
        return new Promise((resolve, reject) => {
            this.db.all(
                `SELECT * FROM licenses ORDER BY created_at DESC`,
                [],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(rows.map(row => ({
                            key: row.key,
                            duration: row.duration,
                            createdAt: row.created_at,
                            expiresAt: row.expires_at,
                            hardwareId: row.hardware_id,
                            notes: row.notes,
                            isActive: row.is_active
                        })));
                    }
                }
            );
        });
    }

    async deleteLicense(licenseKey) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `DELETE FROM licenses WHERE key = ?`,
                [licenseKey],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ deleted: this.changes > 0 });
                    }
                }
            );
        });
    }

    async resetHardwareId(licenseKey) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
                [licenseKey],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ updated: this.changes > 0 });
                    }
                }
            );
        });
    }

    async logSecurityEvent(type, severity, description, username = null, ipAddress = null, userAgent = null, additionalData = null) {
        return new Promise((resolve, reject) => {
            this.db.run(
                `INSERT INTO security_events (type, severity, description, username, ip_address, user_agent, additional_data) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [type, severity, description, username, ipAddress, userAgent, additionalData],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ id: this.lastID });
                    }
                }
            );
        });
    }

    async getSecurityEvents(limit = 100, offset = 0) {
        return new Promise((resolve, reject) => {
            this.db.all(
                `SELECT * FROM security_events ORDER BY timestamp DESC LIMIT ? OFFSET ?`,
                [limit, offset],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(rows);
                    }
                }
            );
        });
    }

    async getSecurityEventCount() {
        return new Promise((resolve, reject) => {
            this.db.get(
                `SELECT COUNT(*) as count FROM security_events`,
                [],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(row.count);
                    }
                }
            );
        });
    }

    close() {
        if (this.db) {
            this.db.close((err) => {
                if (err) {
                    console.error('Error closing database:', err);
                } else {
                    console.log('Database connection closed');
                }
            });
        }
    }
}

module.exports = new SQLiteDatabase();
