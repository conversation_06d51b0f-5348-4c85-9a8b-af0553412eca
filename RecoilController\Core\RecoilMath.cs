using System;
using System.Runtime.InteropServices;
using System.Threading;
using RecoilController.Models;

namespace RecoilController.Core
{
    /// <summary>
    /// Advanced recoil mathematics engine based on professional template
    /// Converted from C++ template with enhanced precision and security
    /// </summary>
    public static class RecoilMath
    {
        // Windows API for precise mouse control
        [DllImport("user32.dll")]
        private static extern bool SetCursorPos(int x, int y);

        [DllImport("user32.dll")]
        private static extern bool GetCursorPos(out POINT lpPoint);

        [DllImport("user32.dll")]
        private static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint dwData, UIntPtr dwExtraInfo);

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int X;
            public int Y;
        }

        // Mouse event constants
        private const uint MOUSEEVENTF_MOVE = 0x0001;
        private const uint MOUSEEVENTF_ABSOLUTE = 0x8000;

        // Recoil calculation constants (from template)
        private const double RUST_SENSITIVITY_SCALE = 3.0;
        private const double RUST_FOV_SCALE = 100.0;
        private const double RUST_RECOIL_SCALE = -0.03;
        private const double PIXEL_PRECISION = 0.1;

        /// <summary>
        /// Calculates precise recoil compensation movement
        /// Based on professional template mathematics
        /// </summary>
        public static RecoilMovement CalculateRecoilMovement(
            RustWeapon weapon,
            int bulletIndex,
            RecoilSettings settings,
            bool isADS = false)
        {
            if (weapon?.Recoil == null || bulletIndex < 0)
                return RecoilMovement.Zero;

            try
            {
                // Get base recoil delta from weapon pattern
                var recoilDelta = weapon.Recoil.GetDeltaMovement(bulletIndex, isADS);
                
                // Apply Rust's recoil calculation formula (from template)
                var sensitivity = settings.Sensitivity;
                var fov = settings.FOV;
                
                // Core recoil math (converted from C++ template)
                var recoilMultiplier = CalculateRecoilMultiplier(sensitivity, fov);
                
                // Convert recoil units to pixel movement
                var pixelX = recoilDelta.X / recoilMultiplier;
                var pixelY = recoilDelta.Y / recoilMultiplier;
                
                // Apply user adjustments
                pixelX *= settings.EffectiveHorizontalMultiplier;
                pixelY *= settings.EffectiveVerticalMultiplier;
                
                // Apply weapon-specific modifiers
                pixelX *= weapon.StancePenalty;
                pixelY *= weapon.StancePenalty;
                
                // Apply movement penalty if applicable
                if (settings.IsMoving)
                {
                    pixelX *= (1.0 + weapon.MovePenalty);
                    pixelY *= (1.0 + weapon.MovePenalty);
                }
                
                // Apply FOV offset for scoped weapons
                if (isADS && weapon.HasADS)
                {
                    var fovMultiplier = CalculateFOVMultiplier(weapon.FovOffset, weapon.ZoomFactor);
                    pixelX *= fovMultiplier;
                    pixelY *= fovMultiplier;
                }
                
                return new RecoilMovement
                {
                    X = pixelX,
                    Y = pixelY,
                    BulletIndex = bulletIndex,
                    WeaponName = weapon.Name,
                    IsADS = isADS,
                    Timestamp = DateTime.UtcNow
                };
            }
            catch (Exception)
            {
                // Return zero movement on calculation error
                return RecoilMovement.Zero;
            }
        }

        /// <summary>
        /// Calculates the recoil multiplier based on sensitivity and FOV
        /// Direct conversion from C++ template formula
        /// </summary>
        private static double CalculateRecoilMultiplier(double sensitivity, double fov)
        {
            // Rust's recoil calculation: -0.03 * (sensitivity * 3.0) * (fov / 100.0)
            return RUST_RECOIL_SCALE * (sensitivity * RUST_SENSITIVITY_SCALE) * (fov / RUST_FOV_SCALE);
        }

        /// <summary>
        /// Calculates FOV multiplier for scoped weapons
        /// </summary>
        private static double CalculateFOVMultiplier(double fovOffset, double zoomFactor)
        {
            if (Math.Abs(fovOffset) < 0.001 || Math.Abs(zoomFactor - 1.0) < 0.001)
                return 1.0;
            
            // Calculate effective FOV with scope
            var effectiveFOV = RUST_FOV_SCALE + fovOffset;
            return (effectiveFOV / RUST_FOV_SCALE) / zoomFactor;
        }

        /// <summary>
        /// Applies humanization to movement for natural behavior
        /// Enhanced version of template's humanization
        /// </summary>
        public static RecoilMovement ApplyHumanization(
            RecoilMovement movement,
            HumanisationSettings settings,
            Random random)
        {
            if (!settings.Enabled || movement.IsZero)
                return movement;

            var humanizedX = movement.X;
            var humanizedY = movement.Y;

            // Apply timing variation (affects next movement timing)
            var timingVariation = (random.NextDouble() - 0.5) * settings.TimingVariation / 100.0;
            
            // Apply movement variation
            var movementVariation = settings.MovementVariation / 100.0;
            var xVariation = (random.NextDouble() - 0.5) * movementVariation * Math.Abs(humanizedX);
            var yVariation = (random.NextDouble() - 0.5) * movementVariation * Math.Abs(humanizedY);
            
            humanizedX += xVariation;
            humanizedY += yVariation;
            
            // Apply smoothing factor
            var smoothing = settings.SmoothingFactor;
            humanizedX = movement.X * smoothing + humanizedX * (1.0 - smoothing);
            humanizedY = movement.Y * smoothing + humanizedY * (1.0 - smoothing);
            
            // Add micro-adjustments for natural feel
            if (settings.MicroAdjustments && random.NextDouble() < 0.3)
            {
                humanizedX += (random.NextDouble() - 0.5) * 0.5;
                humanizedY += (random.NextDouble() - 0.5) * 0.5;
            }

            return new RecoilMovement
            {
                X = humanizedX,
                Y = humanizedY,
                BulletIndex = movement.BulletIndex,
                WeaponName = movement.WeaponName,
                IsADS = movement.IsADS,
                Timestamp = movement.Timestamp,
                TimingVariation = timingVariation
            };
        }

        /// <summary>
        /// Applies randomization to reduce pattern detectability
        /// </summary>
        public static RecoilMovement ApplyRandomization(
            RecoilMovement movement,
            RandomisationSettings settings,
            Random random,
            int bulletCount)
        {
            if (!settings.Enabled || movement.IsZero)
                return movement;

            var randomizedX = movement.X;
            var randomizedY = movement.Y;

            // Apply horizontal deviation
            var hDeviation = (random.NextDouble() - 0.5) * settings.HorizontalDeviation;
            randomizedX += hDeviation;

            // Apply vertical deviation
            var vDeviation = (random.NextDouble() - 0.5) * settings.VerticalDeviation;
            randomizedY += vDeviation;

            // Apply adaptive randomization based on bullet count
            if (settings.AdaptiveRandomisation && bulletCount > 5)
            {
                var adaptiveFactor = Math.Min(1.5, 1.0 + (bulletCount - 5) * 0.1);
                randomizedX *= adaptiveFactor;
                randomizedY *= adaptiveFactor;
            }

            return new RecoilMovement
            {
                X = randomizedX,
                Y = randomizedY,
                BulletIndex = movement.BulletIndex,
                WeaponName = movement.WeaponName,
                IsADS = movement.IsADS,
                Timestamp = movement.Timestamp,
                TimingVariation = movement.TimingVariation
            };
        }

        /// <summary>
        /// Executes precise mouse movement with sub-pixel accuracy
        /// Based on template's high-precision movement system
        /// </summary>
        public static void ExecuteMouseMovement(RecoilMovement movement)
        {
            if (movement.IsZero || Math.Abs(movement.X) < PIXEL_PRECISION && Math.Abs(movement.Y) < PIXEL_PRECISION)
                return;

            try
            {
                // Get current cursor position
                GetCursorPos(out POINT currentPos);
                
                // Calculate new position with sub-pixel precision
                var newX = currentPos.X + (int)Math.Round(movement.X);
                var newY = currentPos.Y + (int)Math.Round(movement.Y);
                
                // Execute movement using absolute positioning for precision
                SetCursorPos(newX, newY);
                
                // Small delay for natural movement (from template)
                Thread.Sleep(1);
            }
            catch (Exception)
            {
                // Fail silently to avoid disrupting gameplay
            }
        }

        /// <summary>
        /// Validates movement bounds to prevent extreme movements
        /// Security feature to prevent malicious pattern injection
        /// </summary>
        public static RecoilMovement ValidateMovement(RecoilMovement movement, double maxMovement = 50.0)
        {
            if (movement.IsZero)
                return movement;

            var clampedX = Math.Max(-maxMovement, Math.Min(maxMovement, movement.X));
            var clampedY = Math.Max(-maxMovement, Math.Min(maxMovement, movement.Y));

            return new RecoilMovement
            {
                X = clampedX,
                Y = clampedY,
                BulletIndex = movement.BulletIndex,
                WeaponName = movement.WeaponName,
                IsADS = movement.IsADS,
                Timestamp = movement.Timestamp,
                TimingVariation = movement.TimingVariation
            };
        }
    }

    /// <summary>
    /// Represents a calculated recoil movement
    /// </summary>
    public struct RecoilMovement
    {
        public double X { get; set; }
        public double Y { get; set; }
        public int BulletIndex { get; set; }
        public string WeaponName { get; set; }
        public bool IsADS { get; set; }
        public DateTime Timestamp { get; set; }
        public double TimingVariation { get; set; }

        public bool IsZero => Math.Abs(X) < 0.001 && Math.Abs(Y) < 0.001;

        public static RecoilMovement Zero => new RecoilMovement
        {
            X = 0,
            Y = 0,
            BulletIndex = 0,
            WeaponName = string.Empty,
            IsADS = false,
            Timestamp = DateTime.UtcNow,
            TimingVariation = 0
        };
    }
}
