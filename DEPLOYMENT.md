# Octane Auth Backend Deployment Guide

## Overview
This guide covers deploying the Octane Auth Backend to your VPS server.

## Prerequisites
- VPS server with Ubuntu/Debian
- Node.js 18+ installed
- PM2 process manager
- SSH access to the VPS

## Quick Deployment

### Option 1: Using the Deployment Script (Linux/macOS)
```bash
# Configure VPS details in deploy.sh
nano deploy.sh

# Make script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### Option 2: Manual Deployment

#### 1. Prepare the Package
```bash
# Create deployment package
tar -czf octane-auth.tar.gz \
  --exclude='.git' \
  --exclude='node_modules' \
  --exclude='.env' \
  --exclude='database.db' \
  --exclude='logs' \
  .
```

#### 2. Upload to VPS
```bash
# Upload package
scp octane-auth.tar.gz root@your-vps-ip:/tmp/

# Connect to VPS
ssh root@your-vps-ip
```

#### 3. Deploy on VPS
```bash
# Stop existing services
pm2 stop octane-auth discord-bot || true

# Backup current installation
if [ -d "/opt/octane-auth" ]; then
    mv /opt/octane-auth /opt/octane-auth.backup.$(date +%Y%m%d-%H%M%S)
fi

# Extract new version
cd /tmp
tar -xzf octane-auth.tar.gz
mv octane-auth /opt/octane-auth
cd /opt/octane-auth

# Install dependencies
npm install --production

# Setup environment
cp /opt/octane-auth.backup.*/.env .env 2>/dev/null || echo "Creating new .env"
cp /opt/octane-auth.backup.*/database.db database.db 2>/dev/null || echo "Will create new database"

# Create directories
mkdir -p logs .pm2/logs

# Set permissions
chown -R root:root /opt/octane-auth

# Start services
pm2 start ecosystem.config.js
pm2 save

# Check status
pm2 status
```

## Environment Configuration

Create or update `.env` file:
```env
NODE_ENV=production
PORT=3000
DISCORD_TOKEN=your_discord_token_here
DATABASE_PATH=./database.db
```

## Service Management

### Start Services
```bash
pm2 start ecosystem.config.js
```

### Stop Services
```bash
pm2 stop octane-auth discord-bot
```

### Restart Services
```bash
pm2 restart octane-auth discord-bot
```

### View Logs
```bash
# All logs
pm2 logs

# Specific service
pm2 logs octane-auth
pm2 logs discord-bot
```

### Monitor Services
```bash
pm2 monit
```

## Verification

### Health Check
```bash
curl http://localhost:3000/health
```

### Test Endpoints
```bash
# Main page
curl http://localhost:3000/

# Admin panel
curl http://localhost:3000/admin

# Login page
curl http://localhost:3000/login

# API health
curl http://localhost:3000/api/licenses/count
```

## Nginx Configuration (Optional)

If using Nginx as a reverse proxy:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Troubleshooting

### Common Issues

1. **Port 3000 already in use**
   ```bash
   sudo lsof -i :3000
   sudo kill -9 <PID>
   ```

2. **Permission denied**
   ```bash
   sudo chown -R $USER:$USER /opt/octane-auth
   ```

3. **Database locked**
   ```bash
   sudo fuser -k database.db
   ```

4. **Out of memory**
   ```bash
   # Check memory usage
   free -h
   
   # Restart services with lower memory limits
   pm2 restart ecosystem.config.js
   ```

### Log Locations
- Application logs: `/opt/octane-auth/.pm2/logs/`
- PM2 logs: `~/.pm2/logs/`
- System logs: `/var/log/`

### Performance Monitoring
```bash
# System resources
htop
df -h
free -h

# PM2 monitoring
pm2 monit

# Process status
pm2 status
```

## Security Considerations

1. **Firewall Configuration**
   ```bash
   # Allow only necessary ports
   ufw allow 22    # SSH
   ufw allow 80    # HTTP
   ufw allow 443   # HTTPS
   ufw enable
   ```

2. **SSL Certificate (Recommended)**
   ```bash
   # Using Let's Encrypt
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

3. **Regular Updates**
   ```bash
   # Update system packages
   sudo apt update && sudo apt upgrade
   
   # Update Node.js dependencies
   cd /opt/octane-auth
   npm audit fix
   ```

## Backup Strategy

### Database Backup
```bash
# Create backup
cp /opt/octane-auth/database.db /opt/backups/database-$(date +%Y%m%d).db

# Automated backup (add to crontab)
0 2 * * * cp /opt/octane-auth/database.db /opt/backups/database-$(date +\%Y\%m\%d).db
```

### Full Backup
```bash
# Create full backup
tar -czf /opt/backups/octane-auth-$(date +%Y%m%d).tar.gz /opt/octane-auth
```

## Support

For deployment issues:
1. Check the logs: `pm2 logs`
2. Verify system resources: `htop`, `df -h`
3. Test connectivity: `curl http://localhost:3000/health`
4. Review configuration: `cat /opt/octane-auth/.env`

## New Features in v4.2.1

- ✅ Modern admin dashboard with real-time statistics
- ✅ Clean login interface with secure authentication
- ✅ Improved Discord bot management
- ✅ Enhanced security monitoring
- ✅ Mobile-responsive design
- ✅ Better error handling and logging
- ✅ Optimized for 1GB VPS memory usage
