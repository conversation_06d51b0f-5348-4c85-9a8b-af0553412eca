<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Recoil Scripts - Admin Dashboard</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-left">
                    <h1>Dashboard Overview</h1>
                    <p>Monitor and manage your Octane Recoil Scripts system</p>
                </div>
                <div class="header-right">
                    <div class="status-indicator online">
                        <i class="fas fa-circle"></i>
                        <span>System Online</span>
                    </div>
                    <div class="current-time" id="current-time">19:50:26</div>
                </div>
            </header>
            
            <div class="dashboard-content">
                <!-- Overview Section -->
                <section id="overview-section" class="content-section active">
                    <div class="dashboard-grid">
                        <!-- Stats Cards -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Total Licenses</h3>
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="card-content">
                                <div class="stat-number" id="total-licenses">7</div>
                                <div class="stat-change positive">+2 this week</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Active Users</h3>
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <div class="stat-number" id="active-users">6</div>
                                <div class="stat-change positive">+1 today</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Security Alerts</h3>
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="card-content">
                                <div class="stat-number" id="security-alerts">0</div>
                                <div class="stat-change neutral">No new alerts</div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Server Uptime</h3>
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="card-content">
                                <div class="stat-number" id="server-uptime">0h</div>
                                <div class="stat-change positive">99.9% this month</div>
                            </div>
                        </div>
                        
                        <!-- Recent Activity -->
                        <div class="dashboard-card full-width">
                            <div class="card-header">
                                <h3>Recent Activity</h3>
                            </div>
                            <div class="card-content">
                                <div id="activity-list" class="activity-list">
                                    <!-- Activity items will be loaded here -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- System Health -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>System Health</h3>
                                <div class="health-indicator healthy">
                                    <i class="fas fa-heartbeat"></i>
                                    <span>Healthy</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="health-metrics">
                                    <div class="metric">
                                        <span class="metric-label">CPU Usage</span>
                                        <span class="metric-value">35%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">Memory Usage</span>
                                        <span class="metric-value">62%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">Disk Usage</span>
                                        <span class="metric-value">28%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="card-content">
                                <div class="quick-actions">
                                    <button class="btn btn-primary" onclick="window.location.href='key-maintenance.html'">
                                        <i class="fas fa-key"></i>
                                        Create License
                                    </button>
                                    <button class="btn btn-secondary" onclick="viewLogs()">
                                        <i class="fas fa-file-alt"></i>
                                        View Logs
                                    </button>
                                    <button class="btn btn-info" onclick="testDiscord()">
                                        <i class="fab fa-discord"></i>
                                        Test Discord
                                    </button>
                                    <button class="btn btn-warning" onclick="window.location.href='reminders.html'">
                                        <i class="fas fa-tools"></i>
                                        Maintenance
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- VPS Information -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>VPS Information</h3>
                        <div class="status-indicator online">
                            <i class="fas fa-circle"></i>
                            <span>Online</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="vps-info">
                            <div class="metric">
                                <span class="metric-label">IP Address</span>
                                <span class="metric-value">*************</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Location</span>
                                <span class="metric-value">Germany</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Memory</span>
                                <span class="metric-value">1GB RAM</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/admin-modern.js"></script>
    <script>
        // Initialize shared components
        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
        });
    </script>
</body>
</html>
