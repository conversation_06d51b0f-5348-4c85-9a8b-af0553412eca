class DiscordManagement {
    constructor() {
        this.currentTab = 'overview';
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDiscordStatus();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Refresh button
        document.getElementById('refreshBtn')?.addEventListener('click', () => {
            this.loadDiscordStatus();
            this.loadLogs();
        });

        // Logout button
        document.getElementById('logoutBtn')?.addEventListener('click', () => {
            localStorage.removeItem('octane_admin_token');
            window.location.href = '/admin';
        });

        // Quick Actions
        document.getElementById('testBotBtn')?.addEventListener('click', () => this.testBot());
        document.getElementById('sendTestMessageBtn')?.addEventListener('click', () => this.sendTestMessage());
        document.getElementById('dailyReportBtn')?.addEventListener('click', () => this.sendDailyReport());
        document.getElementById('restartBotBtn')?.addEventListener('click', () => this.restartBot());

        // Configuration
        document.getElementById('botConfigForm')?.addEventListener('submit', (e) => this.saveBotConfig(e));

        // Commands
        document.getElementById('registerCommandsBtn')?.addEventListener('click', () => this.registerCommands());
        document.getElementById('clearCommandsBtn')?.addEventListener('click', () => this.clearCommands());

        // Logs
        document.getElementById('refreshLogsBtn')?.addEventListener('click', () => this.loadLogs());
        document.getElementById('clearLogsBtn')?.addEventListener('click', () => this.clearLogs());

        // Testing
        document.getElementById('manualCommandForm')?.addEventListener('submit', (e) => this.executeManualCommand(e));
        document.getElementById('commandType')?.addEventListener('change', (e) => this.updateCommandForm(e.target.value));
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific data
        if (tabName === 'logs') {
            this.loadLogs();
        } else if (tabName === 'configuration') {
            this.loadConfiguration();
        }
    }

    async loadDiscordStatus() {
        try {
            const response = await fetch('/api/admin/discord-status', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.updateStatusDisplay(data.status);
            } else {
                this.showError('Failed to load Discord status');
            }
        } catch (error) {
            console.error('Failed to load Discord status:', error);
            this.showError('Network error loading Discord status');
        }
    }

    updateStatusDisplay(status) {
        // Bot Status
        const statusElement = document.getElementById('botStatus');
        const tagElement = document.getElementById('botTag');

        if (status.online) {
            statusElement.textContent = 'Online';
            statusElement.className = 'status-badge status-online';
            tagElement.textContent = status.botTag || 'authentication#3048';
        } else {
            statusElement.textContent = 'Offline';
            statusElement.className = 'status-badge status-offline';
            tagElement.textContent = 'Not Connected';
        }

        // Server info
        document.getElementById('channelId').textContent = status.channelId || 'Not Set';
        
        // Update configuration form
        document.getElementById('discordChannelId').value = status.channelId || '';
    }

    async testBot() {
        const btn = document.getElementById('testBotBtn');
        const originalText = btn.textContent;
        
        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span> Testing...';

            const response = await fetch('/api/admin/test-discord', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Bot test successful! Check your Discord channel.');
            } else {
                this.showError('Bot test failed: ' + data.message);
            }
        } catch (error) {
            this.showError('Test failed: ' + error.message);
        } finally {
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    async sendTestMessage() {
        try {
            const response = await fetch('/api/admin/send-test-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify({
                    message: '🧪 **Test Message from Admin Panel**\n' +
                            `Sent at: ${new Date().toLocaleString()}\n` +
                            'Discord bot is working correctly! ✅'
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Test message sent successfully!');
            } else {
                this.showError('Failed to send test message: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to send test message: ' + error.message);
        }
    }

    async sendDailyReport() {
        const btn = document.getElementById('dailyReportBtn');
        const originalText = btn.textContent;
        
        try {
            btn.disabled = true;
            btn.innerHTML = '<span class="loading"></span> Sending...';

            const response = await fetch('/api/admin/send-daily-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Daily report sent successfully!');
            } else {
                this.showError('Failed to send daily report: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to send daily report: ' + error.message);
        } finally {
            btn.disabled = false;
            btn.textContent = originalText;
        }
    }

    async restartBot() {
        if (!confirm('Are you sure you want to restart the Discord bot?')) {
            return;
        }

        try {
            const response = await fetch('/api/admin/restart-discord-bot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Discord bot restarted successfully!');
                setTimeout(() => this.loadDiscordStatus(), 3000);
            } else {
                this.showError('Failed to restart bot: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to restart bot: ' + error.message);
        }
    }

    async saveBotConfig(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const config = {
            token: document.getElementById('discordToken').value,
            serverId: document.getElementById('discordServerId').value,
            channelId: document.getElementById('discordChannelId').value,
            adminId: document.getElementById('discordAdminId').value
        };

        try {
            const response = await fetch('/api/admin/update-discord-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify(config)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('Discord configuration saved successfully!');
                setTimeout(() => this.loadDiscordStatus(), 2000);
            } else {
                this.showError('Failed to save configuration: ' + data.message);
            }
        } catch (error) {
            this.showError('Failed to save configuration: ' + error.message);
        }
    }

    async loadLogs() {
        try {
            const response = await fetch('/api/admin/discord-logs', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                }
            });

            const data = await response.json();

            if (data.success) {
                document.getElementById('logsContainer').textContent = data.logs || 'No logs available';
            } else {
                document.getElementById('logsContainer').textContent = 'Failed to load logs';
            }
        } catch (error) {
            document.getElementById('logsContainer').textContent = 'Error loading logs: ' + error.message;
        }
    }

    updateCommandForm(commandType) {
        const durationGroup = document.getElementById('durationGroup');
        const licenseKeyGroup = document.getElementById('licenseKeyGroup');

        if (commandType === 'create-license') {
            durationGroup.style.display = 'block';
            licenseKeyGroup.style.display = 'none';
        } else if (commandType === 'reset-hwid' || commandType === 'check-license') {
            durationGroup.style.display = 'none';
            licenseKeyGroup.style.display = 'block';
        } else {
            durationGroup.style.display = 'none';
            licenseKeyGroup.style.display = 'none';
        }
    }

    async executeManualCommand(e) {
        e.preventDefault();
        
        const commandType = document.getElementById('commandType').value;
        const duration = document.getElementById('duration').value;
        const licenseKey = document.getElementById('testLicenseKey').value;
        const notes = document.getElementById('testNotes').value;

        let payload = { commandType, notes };

        if (commandType === 'create-license') {
            payload.duration = duration;
        } else if (commandType === 'reset-hwid' || commandType === 'check-license') {
            if (!licenseKey) {
                this.showError('License key is required for this command');
                return;
            }
            payload.licenseKey = licenseKey;
        }

        try {
            const response = await fetch('/api/admin/execute-discord-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('octane_admin_token') || 'firebase-auth'}`
                },
                body: JSON.stringify(payload)
            });

            const data = await response.json();
            
            const resultsContainer = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            
            if (data.success) {
                resultsContainer.textContent += `[${timestamp}] ✅ ${commandType}: ${data.message}\n`;
                this.showSuccess('Command executed successfully!');
            } else {
                resultsContainer.textContent += `[${timestamp}] ❌ ${commandType}: ${data.message}\n`;
                this.showError('Command failed: ' + data.message);
            }
            
            resultsContainer.scrollTop = resultsContainer.scrollHeight;
        } catch (error) {
            this.showError('Failed to execute command: ' + error.message);
        }
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            if (this.currentTab === 'overview') {
                this.loadDiscordStatus();
            }
        }, 30000); // Refresh every 30 seconds
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'error');
    }

    async loadConfiguration() {
        try {
            const response = await fetch('/api/admin/discord-config');
            const data = await response.json();

            if (data.success) {
                // Update configuration display
                const configContainer = document.getElementById('configurationContent');
                if (configContainer) {
                    configContainer.innerHTML = `
                        <div class="config-section">
                            <h3>Discord Bot Configuration</h3>
                            <div class="config-item">
                                <label>Bot Token:</label>
                                <div class="token-display">
                                    <input type="password" value="${data.config.token || 'Not set'}" readonly>
                                    <button onclick="this.previousElementSibling.type = this.previousElementSibling.type === 'password' ? 'text' : 'password'">👁️</button>
                                    <button onclick="navigator.clipboard.writeText('${data.config.token || ''}'); this.textContent = 'Copied!'; setTimeout(() => this.textContent = 'Copy', 2000)">Copy</button>
                                </div>
                            </div>
                            <div class="config-item">
                                <label>Server ID:</label>
                                <div class="token-display">
                                    <input type="text" value="${data.config.serverId || 'Not set'}" readonly>
                                    <button onclick="navigator.clipboard.writeText('${data.config.serverId || ''}'); this.textContent = 'Copied!'; setTimeout(() => this.textContent = 'Copy', 2000)">Copy</button>
                                </div>
                            </div>
                            <div class="config-item">
                                <label>Channel ID:</label>
                                <div class="token-display">
                                    <input type="text" value="${data.config.channelId || 'Not set'}" readonly>
                                    <button onclick="navigator.clipboard.writeText('${data.config.channelId || ''}'); this.textContent = 'Copied!'; setTimeout(() => this.textContent = 'Copy', 2000)">Copy</button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        } catch (error) {
            this.showError('Failed to load configuration: ' + error.message);
        }
    }

    showAlert(message, type) {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.textContent = message;
        alert.style.position = 'fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.style.padding = '15px 20px';
        alert.style.borderRadius = '5px';
        alert.style.color = 'white';
        alert.style.fontWeight = 'bold';
        alert.style.backgroundColor = type === 'success' ? '#28a745' : '#dc3545';

        document.body.appendChild(alert);

        // Remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);
    }
}

// Initialize Discord Management
window.discordManagement = new DiscordManagement();
