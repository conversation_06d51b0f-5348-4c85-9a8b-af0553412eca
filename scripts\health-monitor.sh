#!/bin/bash

# Octane Health Monitor
# Continuously monitors system health and auto-restarts if needed

MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
LOAD_THRESHOLD=2.0
LOG_FILE="/opt/octane-auth/logs/health-monitor.log"

# Create log directory
mkdir -p /opt/octane-auth/logs

# Function to log with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
    echo "$1"
}

# Function to check memory usage
check_memory() {
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    if (( $(echo "$MEMORY_USAGE > $MEMORY_THRESHOLD" | bc -l) )); then
        log_message "⚠️  High memory usage: $MEMORY_USAGE%"
        
        # Clear caches
        sync
        echo 3 > /proc/sys/vm/drop_caches
        
        # Restart PM2 if memory is critically high
        if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
            log_message "🔄 Restarting PM2 due to critical memory usage"
            sudo -u octane pm2 restart octane-auth
        fi
        
        return 1
    fi
    
    return 0
}

# Function to check disk usage
check_disk() {
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$DISK_USAGE" -gt "$DISK_THRESHOLD" ]; then
        log_message "⚠️  High disk usage: $DISK_USAGE%"
        
        # Clean logs
        find /opt/octane-auth/.pm2/logs -name "*.log" -mtime +1 -delete
        journalctl --vacuum-time=1d
        
        return 1
    fi
    
    return 0
}

# Function to check load average
check_load() {
    LOAD_AVG=$(uptime | awk -F'load average:' '{ print $2 }' | awk '{ print $1 }' | sed 's/,//')
    
    if (( $(echo "$LOAD_AVG > $LOAD_THRESHOLD" | bc -l) )); then
        log_message "⚠️  High load average: $LOAD_AVG"
        return 1
    fi
    
    return 0
}

# Function to check PM2 status
check_pm2() {
    if ! sudo -u octane pm2 list | grep -q "online"; then
        log_message "❌ PM2 process not running, restarting..."
        sudo -u octane pm2 start /opt/octane-auth/ecosystem.config.js
        sleep 5
        
        if sudo -u octane pm2 list | grep -q "online"; then
            log_message "✅ PM2 process restarted successfully"
        else
            log_message "❌ Failed to restart PM2 process"
            return 1
        fi
    fi
    
    return 0
}

# Function to check service endpoints
check_endpoints() {
    # Check health endpoint
    if ! curl -s -f http://localhost:3000/api/health > /dev/null; then
        log_message "❌ Health endpoint not responding"
        sudo -u octane pm2 restart octane-auth
        return 1
    fi
    
    return 0
}

# Function to optimize system if needed
optimize_system() {
    log_message "🔧 Running system optimization..."
    
    # Clear temporary files
    find /tmp -type f -atime +1 -delete 2>/dev/null
    
    # Optimize memory settings
    echo 5 > /proc/sys/vm/swappiness
    echo 1 > /proc/sys/vm/dirty_ratio
    
    # Clear caches
    sync
    echo 1 > /proc/sys/vm/drop_caches
    
    log_message "✅ System optimization completed"
}

# Main monitoring loop
main() {
    log_message "🚀 Health monitor started"
    
    while true; do
        ISSUES=0
        
        # Run all checks
        check_memory || ((ISSUES++))
        check_disk || ((ISSUES++))
        check_load || ((ISSUES++))
        check_pm2 || ((ISSUES++))
        check_endpoints || ((ISSUES++))
        
        # If multiple issues, run optimization
        if [ "$ISSUES" -gt 2 ]; then
            optimize_system
        fi
        
        # Log status every hour
        if [ $(($(date +%M) % 60)) -eq 0 ]; then
            MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
            DISK_USAGE=$(df / | tail -1 | awk '{print $5}')
            LOAD_AVG=$(uptime | awk -F'load average:' '{ print $2 }' | awk '{ print $1 }' | sed 's/,//')
            
            log_message "📊 Status: Memory: $MEMORY_USAGE%, Disk: $DISK_USAGE, Load: $LOAD_AVG"
        fi
        
        # Wait 30 seconds before next check
        sleep 30
    done
}

# Handle signals
trap 'log_message "🛑 Health monitor stopped"; exit 0' SIGTERM SIGINT

# Start monitoring
main
