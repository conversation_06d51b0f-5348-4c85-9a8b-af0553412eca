const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1'
    });
});

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// Admin authentication endpoint
app.post('/api/auth/login', (req, res) => {
    const { email, password, rememberMe } = req.body;
    
    // Simple admin authentication (in production, use proper password hashing)
    const validCredentials = [
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'octane2024' }
    ];
    
    const user = validCredentials.find(cred => 
        cred.email === email && cred.password === password
    );
    
    if (user) {
        // Generate a simple token (in production, use JWT)
        const token = Buffer.from(`${email}:${Date.now()}`).toString('base64');
        
        res.json({
            success: true,
            message: 'Login successful',
            token: token,
            user: {
                email: email,
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid email or password'
        });
    }
});

// Dashboard statistics endpoints (mock data for testing)
app.get('/api/licenses/count', (req, res) => {
    res.json({ success: true, count: Math.floor(Math.random() * 100) + 50 });
});

app.get('/api/users/active', (req, res) => {
    res.json({ success: true, count: Math.floor(Math.random() * 50) + 20 });
});

app.get('/api/security/alerts', (req, res) => {
    res.json({ success: true, count: Math.floor(Math.random() * 10) });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ success: false, message: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ success: false, message: 'Not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Octane Auth Backend Test Server running on port ${PORT}`);
    console.log(`📊 Admin Panel: http://localhost:${PORT}/admin`);
    console.log(`🔐 Login Page: http://localhost:${PORT}/login`);
    console.log(`❤️ Health Check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});
