using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;
using RecoilController.Models;

namespace RecoilController.Services
{
    /// <summary>
    /// Service for managing user configurations (save/load/delete)
    /// </summary>
    public class ConfigurationService
    {
        private readonly string _configDirectory;
        private readonly string _settingsFile;

        public ConfigurationService()
        {
            _configDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "LagRecoilConfigs");
            _settingsFile = Path.Combine(_configDirectory, "settings.json");
            
            // Ensure config directory exists
            Directory.CreateDirectory(_configDirectory);
        }

        /// <summary>
        /// Saves a configuration to disk
        /// </summary>
        public bool SaveConfiguration(UserConfiguration config)
        {
            try
            {
                config.UpdateModified();
                var fileName = GetConfigFileName(config.ConfigName);
                var filePath = Path.Combine(_configDirectory, fileName);
                
                var json = JsonConvert.SerializeObject(config, Formatting.Indented);
                File.WriteAllText(filePath, json);
                
                return true;
            }
            catch (Exception ex)
            {
                // Log error (implement logging service later)
                Console.WriteLine($"Error saving configuration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads a configuration from disk
        /// </summary>
        public UserConfiguration LoadConfiguration(string configName)
        {
            try
            {
                var fileName = GetConfigFileName(configName);
                var filePath = Path.Combine(_configDirectory, fileName);
                
                if (!File.Exists(filePath))
                    return null;
                
                var json = File.ReadAllText(filePath);
                return JsonConvert.DeserializeObject<UserConfiguration>(json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading configuration: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets all available configuration names
        /// </summary>
        public List<string> GetAvailableConfigurations()
        {
            try
            {
                var configFiles = Directory.GetFiles(_configDirectory, "*.json")
                    .Where(f => !Path.GetFileName(f).Equals("settings.json", StringComparison.OrdinalIgnoreCase))
                    .Select(f => Path.GetFileNameWithoutExtension(f))
                    .ToList();
                
                return configFiles;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting configurations: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Deletes a configuration file
        /// </summary>
        public bool DeleteConfiguration(string configName)
        {
            try
            {
                var fileName = GetConfigFileName(configName);
                var filePath = Path.Combine(_configDirectory, fileName);
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting configuration: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the auto-load configuration name from settings
        /// </summary>
        public string GetAutoLoadConfiguration()
        {
            try
            {
                if (File.Exists(_settingsFile))
                {
                    var json = File.ReadAllText(_settingsFile);
                    var settings = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
                    
                    if (settings.ContainsKey("autoLoadConfig"))
                        return settings["autoLoadConfig"].ToString();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting auto-load config: {ex.Message}");
            }
            
            return null;
        }

        /// <summary>
        /// Sets the auto-load configuration name in settings
        /// </summary>
        public bool SetAutoLoadConfiguration(string configName)
        {
            try
            {
                var settings = new Dictionary<string, object>();
                
                // Load existing settings if they exist
                if (File.Exists(_settingsFile))
                {
                    var json = File.ReadAllText(_settingsFile);
                    settings = JsonConvert.DeserializeObject<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();
                }
                
                settings["autoLoadConfig"] = configName;
                
                var newJson = JsonConvert.SerializeObject(settings, Formatting.Indented);
                File.WriteAllText(_settingsFile, newJson);
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting auto-load config: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves an encrypted license key to disk
        /// </summary>
        public bool SaveLicenseKey(string licenseKey)
        {
            try
            {
                var encryptedKey = EncryptString(licenseKey);
                var licenseFile = Path.Combine(_configDirectory, "license.dat");
                File.WriteAllText(licenseFile, encryptedKey);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving license key: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads and decrypts the saved license key
        /// </summary>
        public string GetSavedLicenseKey()
        {
            try
            {
                var licenseFile = Path.Combine(_configDirectory, "license.dat");
                if (!File.Exists(licenseFile))
                    return null;

                var encryptedKey = File.ReadAllText(licenseFile);
                return DecryptString(encryptedKey);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading license key: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Clears the saved license key
        /// </summary>
        public bool ClearSavedLicenseKey()
        {
            try
            {
                var licenseFile = Path.Combine(_configDirectory, "license.dat");
                if (File.Exists(licenseFile))
                {
                    File.Delete(licenseFile);
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error clearing license key: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Creates a safe filename from a configuration name
        /// </summary>
        private string GetConfigFileName(string configName)
        {
            // Remove invalid filename characters
            var invalidChars = Path.GetInvalidFileNameChars();
            var safeName = string.Join("_", configName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
            return $"{safeName}.json";
        }

        /// <summary>
        /// Encrypts a string using DPAPI (Windows Data Protection API)
        /// </summary>
        private string EncryptString(string plainText)
        {
            try
            {
                var data = Encoding.UTF8.GetBytes(plainText);
                var encryptedData = ProtectedData.Protect(data, null, DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encryptedData);
            }
            catch (Exception)
            {
                // Fallback to simple base64 encoding if DPAPI fails
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(plainText));
            }
        }

        /// <summary>
        /// Decrypts a string using DPAPI (Windows Data Protection API)
        /// </summary>
        private string DecryptString(string encryptedText)
        {
            try
            {
                var encryptedData = Convert.FromBase64String(encryptedText);
                var data = ProtectedData.Unprotect(encryptedData, null, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(data);
            }
            catch (Exception)
            {
                // Fallback to simple base64 decoding if DPAPI fails
                try
                {
                    var data = Convert.FromBase64String(encryptedText);
                    return Encoding.UTF8.GetString(data);
                }
                catch
                {
                    return null;
                }
            }
        }
    }
}
