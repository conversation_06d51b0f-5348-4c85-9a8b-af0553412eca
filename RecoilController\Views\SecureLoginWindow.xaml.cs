using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Management;
using System.Linq;
using System.Windows.Media;

namespace RecoilController.Views
{
    public partial class SecureLoginWindow : Window
    {
        private const string BACKEND_URL = "http://217.154.58.14:3000";
        private const string FLASHER_ERROR_WEBHOOK = "https://discord.com/api/webhooks/1398553421698695228/zwqLShQkFYzOnE2ONUbwTXK9Fz6cNnjnxEV9OnWiwuuKSir3wnG8LjOEKSdlTylYafEQ";
        
        public string ValidatedLicenseKey { get; private set; }
        public TimeSpan RemainingTime { get; private set; }
        public bool IsAuthenticated { get; private set; }

        public SecureLoginWindow()
        {
            InitializeComponent();
            InitializeSecureLogin();
        }

        private void InitializeSecureLogin()
        {
            try
            {
                // Generate and display hardware ID
                HardwareIdTextBox.Text = GenerateHardwareId();
                
                // Load saved credentials if available
                LoadSavedCredentials();
                
                // Set focus to license key input
                LicenseKeyTextBox.Focus();
                
                // Check if running in debug mode
                CheckDebugMode();
            }
            catch (Exception ex)
            {
                ShowStatus($"Initialization error: {ex.Message}", Brushes.Red);
                _ = SendErrorToWebhook("Login Initialization Error", ex.Message, ex.StackTrace);
            }
        }

        private void CheckDebugMode()
        {
            #if DEBUG
            ShowStatus("⚠️ SOFTWARE RUNNING IN DEBUG MODE - SECURITY REDUCED", Brushes.Orange);
            _ = SendErrorToWebhook("Security Warning", "Application started in DEBUG mode", "Production security measures disabled");
            #endif
        }

        private string GenerateHardwareId()
        {
            try
            {
                var components = new StringBuilder();
                
                // Get CPU ID
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["ProcessorId"]?.ToString() ?? "");
                        break;
                    }
                }
                
                // Get Motherboard Serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }
                
                // Get BIOS Serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }
                
                // Create hash
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(components.ToString()));
                    return Convert.ToHexString(hash)[..16]; // First 16 characters
                }
            }
            catch (Exception ex)
            {
                _ = SendErrorToWebhook("Hardware ID Generation Error", ex.Message, ex.StackTrace);
                return "HWID_ERROR_" + DateTime.Now.Ticks.ToString()[..8];
            }
        }

        private void LoadSavedCredentials()
        {
            try
            {
                // TODO: Implement secure credential storage
                // For now, just placeholder
            }
            catch (Exception ex)
            {
                _ = SendErrorToWebhook("Credential Loading Error", ex.Message, ex.StackTrace);
            }
        }

        private async void AuthenticateButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformAuthentication();
        }

        private async Task PerformAuthentication()
        {
            try
            {
                var licenseKey = LicenseKeyTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(licenseKey))
                {
                    ShowStatus("Please enter a license key", Brushes.Red);
                    return;
                }

                if (licenseKey.Length < 16)
                {
                    ShowStatus("License key must be at least 16 characters", Brushes.Red);
                    return;
                }

                // Show progress
                AuthProgressBar.Visibility = Visibility.Visible;
                AuthenticateButton.IsEnabled = false;
                ShowStatus("Authenticating...", Brushes.Yellow);

                // First, verify backend connectivity
                if (!await VerifyBackendConnectivity())
                {
                    ShowStatus("Cannot connect to authentication server", Brushes.Red);
                    return;
                }

                // Perform authentication
                var authResult = await AuthenticateWithBackend(licenseKey, HardwareIdTextBox.Text);
                
                if (authResult.Success)
                {
                    ValidatedLicenseKey = licenseKey;
                    RemainingTime = authResult.RemainingTime;
                    IsAuthenticated = true;
                    
                    ShowStatus("Authentication successful!", Brushes.Green);
                    
                    // Save credentials if remember me is checked
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        SaveCredentials(licenseKey);
                    }
                    
                    await Task.Delay(1000);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    ShowStatus($"Authentication failed: {authResult.Message}", Brushes.Red);
                    _ = SendErrorToWebhook("Authentication Failed", $"License: {licenseKey}, Hardware: {HardwareIdTextBox.Text}, Error: {authResult.Message}", "");
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Authentication error: {ex.Message}", Brushes.Red);
                _ = SendErrorToWebhook("Authentication Exception", ex.Message, ex.StackTrace);
            }
            finally
            {
                AuthProgressBar.Visibility = Visibility.Collapsed;
                AuthenticateButton.IsEnabled = true;
            }
        }

        private async Task<bool> VerifyBackendConnectivity()
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);
                
                var response = await client.GetAsync($"{BACKEND_URL}/api/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private async Task<AuthResult> AuthenticateWithBackend(string licenseKey, string hardwareId)
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(30);
                
                var authData = new
                {
                    licenseKey = licenseKey,
                    hardwareId = hardwareId,
                    applicationVersion = "4.0.0",
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };
                
                var json = JsonSerializer.Serialize(authData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await client.PostAsync($"{BACKEND_URL}/api/auth/validate", content);
                var responseText = await response.Content.ReadAsStringAsync();
                
                if (response.IsSuccessStatusCode)
                {
                    var result = JsonSerializer.Deserialize<JsonElement>(responseText);
                    
                    return new AuthResult
                    {
                        Success = result.GetProperty("success").GetBoolean(),
                        Message = result.TryGetProperty("message", out var msg) ? msg.GetString() : "Success",
                        RemainingTime = result.TryGetProperty("remainingTime", out var time) ? 
                            TimeSpan.FromSeconds(time.GetInt64()) : TimeSpan.Zero
                    };
                }
                else
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = $"Server error: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = ex.Message
                };
            }
        }

        private void SaveCredentials(string licenseKey)
        {
            try
            {
                // TODO: Implement secure credential storage
                // Use Windows Credential Manager or encrypted storage
            }
            catch (Exception ex)
            {
                _ = SendErrorToWebhook("Credential Save Error", ex.Message, ex.StackTrace);
            }
        }

        private async Task SendErrorToWebhook(string errorType, string errorMessage, string stackTrace)
        {
            try
            {
                var errorData = new
                {
                    embeds = new[]
                    {
                        new
                        {
                            title = $"🔐 Login System Error: {errorType}",
                            description = $"```{errorMessage}```",
                            color = 0xff4444,
                            fields = new[]
                            {
                                new
                                {
                                    name = "Stack Trace",
                                    value = $"```{(string.IsNullOrEmpty(stackTrace) ? "No stack trace" : stackTrace.Substring(0, Math.Min(stackTrace.Length, 1000)))}```",
                                    inline = false
                                },
                                new
                                {
                                    name = "Timestamp",
                                    value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    inline = true
                                },
                                new
                                {
                                    name = "Hardware ID",
                                    value = HardwareIdTextBox?.Text ?? "Unknown",
                                    inline = true
                                }
                            }
                        }
                    }
                };

                using var client = new HttpClient();
                var json = JsonSerializer.Serialize(errorData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                await client.PostAsync(FLASHER_ERROR_WEBHOOK, content);
            }
            catch
            {
                // Silently fail - don't create infinite error loops
            }
        }

        private void ShowStatus(string message, Brush color)
        {
            StatusTextBlock.Text = message;
            StatusTextBlock.Foreground = color;
            StatusTextBlock.Visibility = Visibility.Visible;
        }

        private void LicenseKeyTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            // Auto-format license key with dashes
            var textBox = sender as System.Windows.Controls.TextBox;
            var text = textBox.Text.Replace("-", "").ToUpper();
            
            if (text.Length > 0)
            {
                var formatted = "";
                for (int i = 0; i < text.Length; i++)
                {
                    if (i > 0 && i % 4 == 0) formatted += "-";
                    formatted += text[i];
                }
                
                if (formatted != textBox.Text)
                {
                    textBox.Text = formatted;
                    textBox.CaretIndex = formatted.Length;
                }
            }
        }

        private void Header_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private class AuthResult
        {
            public bool Success { get; set; }
            public string Message { get; set; }
            public TimeSpan RemainingTime { get; set; }
        }
    }
}
