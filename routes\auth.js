const express = require('express');
const router = express.Router();

// Admin authentication endpoint
router.post('/login', (req, res) => {
    const { email, password, rememberMe } = req.body;

    // Simple admin authentication (in production, use proper password hashing)
    const validCredentials = [
        { email: '<EMAIL>', password: 'admin123' }
    ];

    const user = validCredentials.find(u => u.email === email && u.password === password);

    if (user) {
        // Create session (in production, use proper session management)
        req.session.user = { email: user.email, role: 'admin' };
        
        res.json({
            success: true,
            message: 'Login successful',
            user: { email: user.email, role: 'admin' }
        });
    } else {
        res.status(401).json({
            success: false,
            error: 'Invalid credentials'
        });
    }
});

// Firebase authentication endpoint
router.post('/firebase-login', (req, res) => {
    const { idToken, rememberMe } = req.body;

    // In a real implementation, you would verify the Firebase ID token
    // For now, we'll accept any token and create a session
    if (idToken) {
        req.session.user = { email: '<EMAIL>', role: 'admin' };
        
        res.json({
            success: true,
            message: 'Firebase login successful',
            user: { email: '<EMAIL>', role: 'admin' }
        });
    } else {
        res.status(401).json({
            success: false,
            error: 'Invalid Firebase token'
        });
    }
});

module.exports = router;
