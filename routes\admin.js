const express = require('express');
const router = express.Router();
const database = require('../models/database');
const discordService = require('../services/discord');

// Simple auth middleware
function simpleAuth(req, res, next) {
    req.user = { email: '<EMAIL>', role: 'admin' };
    next();
}

// Get all licenses
router.get('/licenses', simpleAuth, async (req, res) => {
    try {
        const licenses = await database.getAllLicenses();
        res.json({
            success: true,
            licenses: licenses
        });
    } catch (error) {
        console.error('Get licenses error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch licenses'
        });
    }
});

// Create new license
router.post('/licenses', simpleAuth, async (req, res) => {
    try {
        const { duration, notes } = req.body;
        
        console.log('Creating license with:', { duration, notes });
        
        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const validDurations = ['1hour', '1day', '1week', '1month', '3months', '6months', '1year', 'lifetime'];
        if (!validDurations.includes(duration)) {
            return res.status(400).json({
                success: false,
                message: `Invalid duration. Must be one of: ${validDurations.join(', ')}`
            });
        }

        const license = await database.createLicense(duration, notes || '');
        
        // Send Discord notification
        if (discordService.isOnline()) {
            const message = `🎫 **New License Created**\n` +
                          `**Key:** \`${license.key}\`\n` +
                          `**Duration:** ${duration}\n` +
                          `**Expires:** ${license.expiresAt ? new Date(license.expiresAt).toLocaleString() : 'Never'}\n` +
                          `**Notes:** ${notes || 'None'}`;
            
            await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, message);
        }
        
        res.json({
            success: true,
            message: 'License created successfully',
            license: license
        });
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to create license'
        });
    }
});

// Test Discord
router.post('/test-discord', simpleAuth, async (req, res) => {
    try {
        if (!discordService.isOnline()) {
            return res.status(500).json({
                success: false,
                message: 'Discord bot is not online'
            });
        }

        const testMessage = `🧪 **Discord Test**\n` +
                           `Time: ${new Date().toLocaleString()}\n` +
                           `Status: ✅ Connection successful!\n` +
                           `Admin Panel: Working correctly`;

        const sent = await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, testMessage);
        
        if (sent) {
            res.json({
                success: true,
                message: 'Discord test message sent successfully'
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to send Discord test message'
            });
        }
    } catch (error) {
        console.error('Discord test error:', error);
        res.status(500).json({
            success: false,
            message: 'Discord test failed'
        });
    }
});

// Send daily report
router.post('/send-daily-report', simpleAuth, async (req, res) => {
    try {
        const licenses = await database.getAllLicenses();
        const now = new Date();

        const stats = {
            total: licenses.length,
            active: licenses.filter(l => !l.expiresAt || new Date(l.expiresAt) > now).length,
            expired: licenses.filter(l => l.expiresAt && new Date(l.expiresAt) <= now).length
        };

        if (discordService.isOnline()) {
            const report = `📊 **Daily License Report**\n` +
                          `**Date:** ${now.toLocaleDateString()}\n` +
                          `**Total Licenses:** ${stats.total}\n` +
                          `**Active:** ${stats.active}\n` +
                          `**Expired:** ${stats.expired}\n` +
                          `**System Status:** ✅ Online`;

            await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, report);
        }

        res.json({
            success: true,
            message: 'Daily report sent successfully',
            stats: stats
        });
    } catch (error) {
        console.error('Daily report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send daily report'
        });
    }
});

// Update Discord token
router.post('/update-discord-token', simpleAuth, async (req, res) => {
    try {
        const { token } = req.body;

        if (!token || !token.trim()) {
            return res.status(400).json({
                success: false,
                message: 'Discord token is required'
            });
        }

        // Basic token validation
        if (!token.match(/^[A-Za-z0-9._-]+$/)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid token format'
            });
        }

        const fs = require('fs').promises;
        const path = require('path');
        const envPath = path.join(__dirname, '..', '.env');

        // Read current .env file
        let envContent = await fs.readFile(envPath, 'utf8');

        // Update or add Discord token
        if (envContent.includes('DISCORD_TOKEN=')) {
            envContent = envContent.replace(/DISCORD_TOKEN=.*$/m, `DISCORD_TOKEN=${token}`);
        } else {
            envContent += `\nDISCORD_TOKEN=${token}`;
        }

        // Write back to .env file
        await fs.writeFile(envPath, envContent);

        // Restart Discord service
        await discordService.initialize();

        res.json({
            success: true,
            message: 'Discord token updated successfully. Bot will reconnect shortly.'
        });

    } catch (error) {
        console.error('Update Discord token error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update Discord token: ' + error.message
        });
    }
});

// Get Discord status
router.get('/discord-status', simpleAuth, async (req, res) => {
    try {
        const isOnline = discordService.isOnline();
        const botTag = discordService.client && discordService.client.user ? discordService.client.user.tag : 'Unknown';

        res.json({
            success: true,
            status: {
                online: isOnline,
                botTag: botTag,
                hasToken: !!process.env.DISCORD_TOKEN,
                serverId: process.env.DISCORD_SERVER_ID,
                channelId: process.env.DISCORD_CHANNEL_ID
            }
        });
    } catch (error) {
        console.error('Discord status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get Discord status'
        });
    }
});

// Get Discord configuration
router.get('/discord-config', simpleAuth, async (req, res) => {
    try {
        res.json({
            success: true,
            config: {
                token: process.env.DISCORD_TOKEN,
                serverId: process.env.DISCORD_SERVER_ID,
                channelId: process.env.DISCORD_CHANNEL_ID,
                adminUserId: process.env.DISCORD_ADMIN_USER_ID
            }
        });
    } catch (error) {
        console.error('Discord config error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get Discord configuration'
        });
    }
});

// Send test message to Discord
router.post('/send-test-message', simpleAuth, async (req, res) => {
    try {
        const { message } = req.body;
        const testMessage = message || '🧪 **Test Message from Admin Panel**\n' +
                                     `Sent at: ${new Date().toLocaleString()}\n` +
                                     'Discord bot is working correctly! ✅';

        if (discordService.isOnline()) {
            await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, testMessage);
            res.json({
                success: true,
                message: 'Test message sent successfully'
            });
        } else {
            res.status(503).json({
                success: false,
                message: 'Discord bot is offline'
            });
        }
    } catch (error) {
        console.error('Send test message error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send test message: ' + error.message
        });
    }
});

// Update Discord configuration
router.post('/update-discord-config', simpleAuth, async (req, res) => {
    try {
        const { token, serverId, channelId, adminId } = req.body;

        const fs = require('fs').promises;
        const path = require('path');
        const envPath = path.join(__dirname, '..', '.env');

        // Read current .env file
        let envContent = await fs.readFile(envPath, 'utf8');

        // Update Discord configuration
        const updates = {
            'DISCORD_TOKEN': token,
            'DISCORD_SERVER_ID': serverId,
            'DISCORD_CHANNEL_ID': channelId,
            'DISCORD_ADMIN_USER_ID': adminId
        };

        for (const [key, value] of Object.entries(updates)) {
            if (value) {
                if (envContent.includes(`${key}=`)) {
                    envContent = envContent.replace(new RegExp(`${key}=.*$`, 'm'), `${key}=${value}`);
                } else {
                    envContent += `\n${key}=${value}`;
                }
            }
        }

        // Write back to .env file
        await fs.writeFile(envPath, envContent);

        // Restart Discord service if token was updated
        if (token) {
            await discordService.initialize();
        }

        res.json({
            success: true,
            message: 'Discord configuration updated successfully'
        });

    } catch (error) {
        console.error('Update Discord config error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update Discord configuration: ' + error.message
        });
    }
});

// Restart Discord bot
router.post('/restart-discord-bot', simpleAuth, async (req, res) => {
    try {
        await discordService.initialize();

        res.json({
            success: true,
            message: 'Discord bot restarted successfully'
        });
    } catch (error) {
        console.error('Restart Discord bot error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to restart Discord bot: ' + error.message
        });
    }
});

// Get Discord logs
router.get('/discord-logs', simpleAuth, async (req, res) => {
    try {
        // This would typically read from log files or memory
        // For now, return a placeholder
        const logs = `[${new Date().toISOString()}] Discord bot initialized
[${new Date().toISOString()}] Connected to server: ${process.env.DISCORD_SERVER_ID}
[${new Date().toISOString()}] Registered slash commands
[${new Date().toISOString()}] Bot status: ${discordService.isOnline() ? 'Online' : 'Offline'}`;

        res.json({
            success: true,
            logs: logs
        });
    } catch (error) {
        console.error('Get Discord logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get Discord logs: ' + error.message
        });
    }
});

// Execute Discord command manually
router.post('/execute-discord-command', simpleAuth, async (req, res) => {
    try {
        const { commandType, duration, licenseKey, notes } = req.body;

        let result;

        switch (commandType) {
            case 'create-license':
                const license = await database.createLicense(duration, notes || 'Created via admin panel');
                result = `License created: ${license.key} (${duration})`;

                // Send Discord notification
                if (discordService.isOnline()) {
                    const message = `🎫 **License Created via Admin Panel**\n` +
                                   `**Key:** \`${license.key}\`\n` +
                                   `**Duration:** ${duration}\n` +
                                   `**Notes:** ${notes || 'None'}`;
                    await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, message);
                }
                break;

            case 'reset-hwid':
                const resetSuccess = await database.resetHardwareId(licenseKey);
                if (resetSuccess) {
                    result = `Hardware ID reset for license: ${licenseKey}`;

                    // Send Discord notification
                    if (discordService.isOnline()) {
                        const message = `🔄 **Hardware ID Reset via Admin Panel**\n` +
                                       `**License Key:** \`${licenseKey}\``;
                        await discordService.sendMessage(process.env.DISCORD_CHANNEL_ID, message);
                    }
                } else {
                    throw new Error('License key not found');
                }
                break;

            case 'check-license':
                const licenseInfo = await database.getLicense(licenseKey);
                if (licenseInfo) {
                    const isExpired = licenseInfo.expiresAt && new Date(licenseInfo.expiresAt) <= new Date();
                    result = `License ${licenseKey}: ${isExpired ? 'Expired' : 'Valid'} - Created: ${new Date(licenseInfo.createdAt).toLocaleDateString()}`;
                } else {
                    throw new Error('License key not found');
                }
                break;

            case 'list-licenses':
                const licenses = await database.getAllLicenses();
                result = `Found ${licenses.length} licenses in database`;
                break;

            default:
                throw new Error('Unknown command type');
        }

        res.json({
            success: true,
            message: result
        });

    } catch (error) {
        console.error('Execute Discord command error:', error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// Delete license key
router.delete('/licenses/:key', simpleAuth, async (req, res) => {
    try {
        const { key } = req.params;
        const success = await database.deleteLicense(key);

        if (success) {
            res.json({
                success: true,
                message: 'License key deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License key not found'
            });
        }
    } catch (error) {
        console.error('Delete license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete license key'
        });
    }
});

// Suspend license key
router.post('/licenses/:key/suspend', simpleAuth, async (req, res) => {
    try {
        const { key } = req.params;
        const success = await database.suspendLicense(key);

        if (success) {
            res.json({
                success: true,
                message: 'License key suspended successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License key not found'
            });
        }
    } catch (error) {
        console.error('Suspend license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to suspend license key'
        });
    }
});

// Activate license key
router.post('/licenses/:key/activate', simpleAuth, async (req, res) => {
    try {
        const { key } = req.params;
        const success = await database.activateLicense(key);

        if (success) {
            res.json({
                success: true,
                message: 'License key activated successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License key not found'
            });
        }
    } catch (error) {
        console.error('Activate license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to activate license key'
        });
    }
});

// Reset hardware ID for license key
router.post('/licenses/:key/reset-hwid', simpleAuth, async (req, res) => {
    try {
        const { key } = req.params;
        const success = await database.resetHardwareId(key);

        if (success) {
            res.json({
                success: true,
                message: 'Hardware ID reset successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License key not found'
            });
        }
    } catch (error) {
        console.error('Reset HWID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset hardware ID'
        });
    }
});

// Bulk delete license keys
router.post('/licenses/bulk-delete', simpleAuth, async (req, res) => {
    try {
        const { keys } = req.body;

        if (!Array.isArray(keys) || keys.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No license keys provided'
            });
        }

        let deletedCount = 0;
        for (const key of keys) {
            const success = await database.deleteLicense(key);
            if (success) deletedCount++;
        }

        res.json({
            success: true,
            message: `${deletedCount} license keys deleted successfully`
        });
    } catch (error) {
        console.error('Bulk delete error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete license keys'
        });
    }
});

// Bulk suspend license keys
router.post('/licenses/bulk-suspend', simpleAuth, async (req, res) => {
    try {
        const { keys } = req.body;

        if (!Array.isArray(keys) || keys.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No license keys provided'
            });
        }

        let suspendedCount = 0;
        for (const key of keys) {
            const success = await database.suspendLicense(key);
            if (success) suspendedCount++;
        }

        res.json({
            success: true,
            message: `${suspendedCount} license keys suspended successfully`
        });
    } catch (error) {
        console.error('Bulk suspend error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to suspend license keys'
        });
    }
});

// Bulk activate license keys
router.post('/licenses/bulk-activate', simpleAuth, async (req, res) => {
    try {
        const { keys } = req.body;

        if (!Array.isArray(keys) || keys.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No license keys provided'
            });
        }

        let activatedCount = 0;
        for (const key of keys) {
            const success = await database.activateLicense(key);
            if (success) activatedCount++;
        }

        res.json({
            success: true,
            message: `${activatedCount} license keys activated successfully`
        });
    } catch (error) {
        console.error('Bulk activate error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to activate license keys'
        });
    }
});

module.exports = router;
