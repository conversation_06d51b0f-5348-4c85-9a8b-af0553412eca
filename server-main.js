const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-hashes'", "https://www.gstatic.com", "https://apis.google.com"],
            scriptSrcAttr: ["'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://identitytoolkit.googleapis.com", "https://securetoken.googleapis.com"],
        },
    },
    crossOriginOpenerPolicy: false,
    crossOriginEmbedderPolicy: false
}));

app.use(cors({
    origin: ['http://localhost:3000', 'http://*************:3000'],
    credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Database setup
const dbPath = path.join(__dirname, 'octane_auth.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err);
        process.exit(1);
    }
    console.log('✅ Connected to SQLite database');
});

// Initialize database tables
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS licenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        duration TEXT NOT NULL,
        expires_at INTEGER NOT NULL,
        hardware_id TEXT,
        is_active INTEGER DEFAULT 1,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        notes TEXT
    )`);
    
    db.run(`CREATE TABLE IF NOT EXISTS security_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp INTEGER DEFAULT (strftime('%s', 'now')),
        ip_address TEXT,
        event_type TEXT,
        details TEXT,
        severity TEXT DEFAULT 'info'
    )`);
    
    console.log('✅ Database tables initialized');
});

// Utility functions
function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.replace(/[<>'"&]/g, '').trim().substring(0, 255);
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const segments = [];
    for (let i = 0; i < 3; i++) {
        let segment = '';
        for (let j = 0; j < 3; j++) {
            segment += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        segments.push(segment);
    }
    return segments.join('-');
}

function calculateExpiryDate(duration) {
    const now = Date.now();
    const durationMap = {
        '1 hour': 60 * 60 * 1000,
        '1 day': 24 * 60 * 60 * 1000,
        '1 week': 7 * 24 * 60 * 60 * 1000,
        '1 month': 30 * 24 * 60 * 60 * 1000,
        '3 months': 90 * 24 * 60 * 60 * 1000,
        '6 months': 180 * 24 * 60 * 60 * 1000,
        '1 year': 365 * 24 * 60 * 60 * 1000,
        'lifetime': 50 * 365 * 24 * 60 * 60 * 1000
    };
    
    const durationMs = durationMap[duration.toLowerCase()] || durationMap['1 month'];
    return now + durationMs;
}

function logSecurityEvent(ip, eventType, details, severity = 'info') {
    db.run(
        `INSERT INTO security_logs (ip_address, event_type, details, severity) VALUES (?, ?, ?, ?)`,
        [ip, eventType, details, severity],
        (err) => {
            if (err) console.error('Security log error:', err);
        }
    );
}

// Rate limiting
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // 3 requests per window
    message: {
        success: false,
        message: 'Too many authentication attempts. Try again in 15 minutes.',
        retryAfter: Math.floor(Date.now() / 1000) + (15 * 60)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting for health checks and admin from localhost
        return req.path === '/api/health' || 
               (req.path.startsWith('/api/admin') && req.ip === '127.0.0.1');
    }
});

// Apply rate limiting to API routes
app.use('/api/validate', apiLimiter);

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
    });
});

// Main validation endpoint
app.post('/api/validate', (req, res) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    try {
        let { licenseKey, hardwareId, applicationVersion, timestamp } = req.body;

        console.log(`[${new Date().toISOString()}] POST /api/validate - IP: ${clientIP}`);
        console.log(`Request body:`, JSON.stringify(req.body, null, 2));

        if (!licenseKey || typeof licenseKey !== 'string') {
            logSecurityEvent(clientIP, 'INVALID_REQUEST', 'Missing license key', 'warning');
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Sanitize inputs
        licenseKey = sanitizeInput(licenseKey);
        hardwareId = sanitizeInput(hardwareId || '');

        // Validate hardware ID format (if provided)
        if (hardwareId && hardwareId !== 'discord-check' && !/^[A-Za-z0-9+/]{12,}$/.test(hardwareId)) {
            logSecurityEvent(clientIP, 'INVALID_HWID', `Invalid hardware ID format: ${hardwareId}`, 'warning');
            return res.status(400).json({
                success: false,
                message: 'Invalid hardware ID format'
            });
        }

        console.log(`🔍 Validating license ${licenseKey} for hardware ${hardwareId}`);

        db.get(
            `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
            [licenseKey],
            (err, row) => {
                if (err) {
                    console.error('Database error:', err);
                    logSecurityEvent(clientIP, 'DATABASE_ERROR', err.message, 'error');
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!row) {
                    logSecurityEvent(clientIP, 'INVALID_LICENSE', `License not found: ${licenseKey}`, 'warning');
                    return res.json({
                        success: false,
                        message: 'Invalid license key'
                    });
                }

                // Check if license is expired
                const now = new Date();
                const expiresAt = new Date(row.expires_at);

                if (now > expiresAt) {
                    logSecurityEvent(clientIP, 'EXPIRED_LICENSE', `Expired license: ${licenseKey}`, 'info');
                    return res.json({
                        success: false,
                        message: 'License has expired'
                    });
                }

                // Update hardware ID if provided and different
                if (hardwareId && hardwareId !== 'discord-check' && row.hardware_id !== hardwareId) {
                    db.run(
                        `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                        [hardwareId, licenseKey],
                        (updateErr) => {
                            if (updateErr) {
                                console.error('Hardware ID update error:', updateErr);
                            } else {
                                console.log(`🔄 Updated hardware ID for ${licenseKey}`);
                            }
                        }
                    );
                }

                const remainingTime = Math.floor((expiresAt - now) / 1000);

                logSecurityEvent(clientIP, 'VALID_LICENSE', `Valid license: ${licenseKey}`, 'info');

                console.log(`✅ License ${licenseKey} validated successfully`);

                res.json({
                    success: true,
                    message: 'License valid',
                    remainingTime: remainingTime,
                    expiresAt: row.expires_at
                });
            }
        );
    } catch (error) {
        console.error('Validation error:', error);
        logSecurityEvent(clientIP, 'VALIDATION_ERROR', error.message, 'error');
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    db.all(
        `SELECT * FROM licenses ORDER BY created_at DESC LIMIT ?`,
        [Math.min(limit, 50)],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

app.post('/api/admin/create-license', (req, res) => {
    try {
        const { duration, notes } = req.body;

        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);

        db.run(
            `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
            [key, duration, expiresAt, notes || ''],
            function(err) {
                if (err) {
                    console.error('Create license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to create license'
                    });
                }

                console.log(`📝 License created: ${key} (${duration})`);

                res.json({
                    success: true,
                    message: 'License created successfully',
                    license: {
                        id: this.lastID,
                        key: key,
                        duration: duration,
                        expiresAt: expiresAt,
                        notes: notes || ''
                    }
                });
            }
        );
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Discord webhook function
async function sendDiscordWebhook(webhookUrl, message) {
    try {
        if (!webhookUrl) {
            console.error('No webhook URL provided');
            return false;
        }

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Octane-Auth-Bot/1.0'
            },
            body: JSON.stringify(message)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Webhook failed: ${response.status} ${response.statusText} - ${errorText}`);
            return false;
        }

        console.log('✅ Webhook sent successfully');
        return true;
    } catch (error) {
        console.error('Discord webhook error:', error);
        return false;
    }
}

// Test Discord webhooks
app.post('/api/admin/test-discord', async (req, res) => {
    try {
        console.log('Testing Discord webhooks...');

        const webhooks = {
            'Security Alerts': 'https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
            'ESP32 Errors': 'https://discord.com/api/webhooks/1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
            'Backend Errors': 'https://discord.com/api/webhooks/1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
        };

        const results = {};

        for (const [name, webhook] of Object.entries(webhooks)) {
            const testMessage = {
                embeds: [{
                    title: `🧪 Webhook Test - ${name}`,
                    description: `Testing webhook connectivity for ${name}`,
                    color: 0x4776E6,
                    fields: [
                        {
                            name: 'Test Time',
                            value: new Date().toISOString(),
                            inline: true
                        },
                        {
                            name: 'Status',
                            value: 'Testing...',
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString()
                }]
            };

            const success = await sendDiscordWebhook(webhook, testMessage);
            results[name] = success;
        }

        const successCount = Object.values(results).filter(r => r).length;
        const totalCount = Object.keys(results).length;

        res.json({
            success: successCount > 0,
            message: `Webhook test completed: ${successCount}/${totalCount} webhooks working`,
            results: results
        });
    } catch (error) {
        console.error('Discord test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test Discord webhooks',
            error: error.message
        });
    }
});

// Admin routes
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/admin/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

app.get('/key-maintenance.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'key-maintenance.html'));
});

// Root redirect
app.get('/', (req, res) => {
    res.redirect('/admin');
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Octane Auth Server running on port ${PORT}`);
    console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🌐 Admin panel: http://localhost:${PORT}/admin`);
});
