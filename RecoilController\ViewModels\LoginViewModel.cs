using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RecoilController.Services;

namespace RecoilController.ViewModels
{
    /// <summary>
    /// View model for the login window
    /// </summary>
    public partial class LoginViewModel : ObservableObject
    {
        private readonly AuthenticationService _authService;
        private readonly ConfigurationService _configService;

        [ObservableProperty]
        private string _licenseKey = string.Empty;

        [ObservableProperty]
        private bool _rememberMe = true;

        [ObservableProperty]
        private string _statusMessage = string.Empty;

        [ObservableProperty]
        private bool _hasError = false;

        [ObservableProperty]
        private bool _isLoading = false;

        // TODO: REMOVE THIS FOR PRODUCTION - Development mode bypass
        [ObservableProperty]
        private bool _showDevMode = true; // Set to false for production

        public ICommand LoginCommand { get; }

        public event EventHandler<bool> LoginCompleted;

        public LoginViewModel(AuthenticationService authService, ConfigurationService configService)
        {
            _authService = authService;
            _configService = configService;

            LoginCommand = new AsyncRelayCommand(PerformLogin);

            // Try to load saved license key
            LoadSavedLicenseKey();
        }

        private async Task PerformLogin()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(LicenseKey))
                {
                    ShowError("Please enter a license key");
                    return;
                }

                IsLoading = true;
                HasError = false;
                StatusMessage = "Validating license...";

                // TODO: REMOVE THIS FOR PRODUCTION - Development bypass
                if (LicenseKey.Equals("admin", StringComparison.OrdinalIgnoreCase))
                {
                    StatusMessage = "⚠️ DEVELOPMENT MODE - Bypassing authentication";
                    await Task.Delay(1000); // Simulate network delay
                    
                    // Set mock authentication
                    _authService.SetMockAuthentication("admin", DateTime.Now.AddYears(1));
                    
                    if (RememberMe)
                    {
                        SaveLicenseKey();
                    }
                    
                    LoginCompleted?.Invoke(this, true);
                    return;
                }

                // Real authentication
                var result = await _authService.ValidateLicenseKey(LicenseKey);

                if (result.Success)
                {
                    StatusMessage = "Login successful!";
                    
                    if (RememberMe)
                    {
                        SaveLicenseKey();
                    }
                    else
                    {
                        ClearSavedLicenseKey();
                    }

                    await Task.Delay(500); // Brief success message
                    LoginCompleted?.Invoke(this, true);
                }
                else
                {
                    ShowError(result.Message);
                }
            }
            catch (Exception ex)
            {
                ShowError($"Login failed: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void ShowError(string message)
        {
            StatusMessage = message;
            HasError = true;
        }

        private void LoadSavedLicenseKey()
        {
            try
            {
                var savedKey = _configService.GetSavedLicenseKey();
                if (!string.IsNullOrEmpty(savedKey))
                {
                    LicenseKey = savedKey;
                    RememberMe = true;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                Console.WriteLine($"Error loading saved license key: {ex.Message}");
            }
        }

        private void SaveLicenseKey()
        {
            try
            {
                _configService.SaveLicenseKey(LicenseKey);
            }
            catch (Exception ex)
            {
                // Log error but don't fail login
                Console.WriteLine($"Error saving license key: {ex.Message}");
            }
        }

        private void ClearSavedLicenseKey()
        {
            try
            {
                _configService.ClearSavedLicenseKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error clearing saved license key: {ex.Message}");
            }
        }
    }
}
