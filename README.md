# Octane Desktop Application

A C# WPF application for managing ESP32-based recoil control system with authentication and device communication.

## Features Implemented ✅

### 🔐 **Authentication System**
- **Login Window** - Appears on startup requiring license key
- **Remember Me** - Encrypted license key storage using Windows DPAPI
- **Development Mode** - Enter "admin" to bypass authentication (⚠️ REMOVE FOR PRODUCTION)
- **Hardware ID Generation** - Unique system fingerprinting for license binding
- **Information Tab** - Shows license status, expiry date, and hardware ID

### 🎯 **Weapon Control System**
- **Weapon Selection** - Dropdown with available weapons (Assault Rifle implemented)
- **Recoil Pattern Data** - Converted from C++ template with proper scaling
- **Sensitivity Settings** - Mouse sensitivity and ADS sensitivity configuration
- **Field of View** - FOV adjustment for accurate recoil compensation
- **Scope Selection** - Different scope types for varied gameplay

### ⚙️ **Configuration Management**
- **Save/Load Configs** - Named configuration presets
- **JSON Storage** - Local configuration persistence
- **Auto-load** - Option to automatically load last used configuration
- **Settings Encryption** - Secure storage of sensitive data

### 🔌 **Device Communication**
- **USB Serial Detection** - Automatic COM port scanning
- **ESP32 Protocol** - Command structure for device communication
- **Device Status** - Real-time connection monitoring
- **Firmware Version** - Version checking and update prompts

### 🎨 **User Interface**
- **Material Design** - Dark theme with green accent colors
- **Hacker Aesthetic** - Monospace fonts and terminal-style elements
- **Tabbed Interface** - Control, Config, and Information tabs
- **Status Bar** - Real-time status messages and "Made by Lag" branding
- **Responsive Design** - Clean, professional appearance

## Architecture

### MVVM Pattern
- **Models** - Data structures (Weapon, UserConfiguration, DeviceInfo)
- **ViewModels** - Business logic and data binding (MainViewModel, LoginViewModel)
- **Views** - UI components (MainWindow, LoginWindow)
- **Services** - Core functionality (AuthenticationService, DeviceService, ConfigurationService)

### Key Components

**Models:**
- `Weapon` - Recoil pattern data and weapon properties
- `Vector2` - 2D vector for movement calculations
- `UserConfiguration` - User settings and preferences
- `DeviceModels` - ESP32 communication structures

**Services:**
- `AuthenticationService` - License validation and hardware binding
- `DeviceService` - ESP32 USB communication
- `ConfigurationService` - Settings persistence and encryption

**ViewModels:**
- `MainViewModel` - Main application logic
- `LoginViewModel` - Authentication flow

## Development Notes

### ⚠️ **Production Checklist**
Before deploying to production, ensure:

1. **Remove Development Mode**
   - Set `ShowDevMode = false` in `LoginViewModel`
   - Remove `SetMockAuthentication` method from `AuthenticationService`
   - Remove admin bypass logic in `LoginViewModel.PerformLogin`

2. **Configure Authentication Backend**
   - Update `_apiBaseUrl` in `AuthenticationService`
   - Implement proper SSL certificate validation
   - Set up VPS backend API endpoints

3. **Security Hardening**
   - Review encryption implementation
   - Validate all user inputs
   - Implement proper error handling

### 🔧 **Build Instructions**
```bash
cd desktop-app
dotnet build RecoilController.sln
dotnet run --project RecoilController
```

### 📦 **Dependencies**
- .NET 6.0 Windows
- Material Design Themes
- CommunityToolkit.Mvvm
- Newtonsoft.Json
- System.IO.Ports

## Next Steps

1. **ESP32 Firmware Integration** - Connect to actual hardware
2. **Authentication Backend** - Implement VPS API server
3. **Firmware Flasher** - Add ESP32 programming capability
4. **Advanced Features** - Keybinds, auto-updates, cloud sync

## Octane by Lag
