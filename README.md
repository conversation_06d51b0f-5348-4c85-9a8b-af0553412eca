# ESP32-S2 HID Mouse Firmware - Octane Recoil Control System

## Overview
This firmware transforms an ESP32-S2 Mini board into a USB HID mouse device for precise recoil compensation in gaming applications. It provides high-performance mouse movement control via serial communication with sub-millisecond latency.

## Features
- **USB HID Mouse Emulation**: Native ESP32-S2 USB support (no external USB chip required)
- **Serial Communication**: 115200 baud with JSON and simple text command support
- **Command Queue System**: 64-command FIFO queue with 1ms processing intervals
- **LED Status Indication**: Visual feedback on GPIO15 for connection and error states
- **Error Handling**: Robust error detection and recovery mechanisms
- **High Performance**: 1000Hz command processing capability with minimal latency
- **Memory Efficient**: Uses only 8.6% RAM and 23.5% Flash

## Hardware Requirements
- **ESP32-S2 Mini** (Lolin S2 Mini or compatible)
- **4MB Flash Memory** (standard on ESP32-S2 Mini)
- **2MB PSRAM** (standard on ESP32-S2 Mini)
- **USB Cable** (data-capable, not charge-only)

## Firmware Specifications
- **Version**: 2.0.0
- **Target**: ESP32-S2 Mini (Lolin S2 Mini)
- **Framework**: Arduino
- **Build System**: PlatformIO
- **Memory Usage**: 28,132 bytes RAM (8.6%), 308,666 bytes Flash (23.5%)
- **CPU Frequency**: 240MHz
- **USB Mode**: Native USB HID
- **Serial Baud**: 115200

## Communication Protocol

### JSON Commands
```json
{"Type": "init", "Data": {"version": "4.2.1", "app": "OctaneRecoilScripts"}}
{"Type": "mouse_move", "Data": {"x": -5, "y": 12}}
{"Type": "mouse_click", "Data": {"button": "left", "action": "down"}}
{"Type": "ping", "Data": {}}
{"Type": "status", "Data": {}}
```

### Simple Text Commands (Fallback)
```
PING → PONG ESP32-S2 Octane HID Mouse v2.0.0
M-5,12 → MOVED
CLICK_LEFT_DOWN → CLICKED
CLICK_LEFT_UP → RELEASED
STATUS → STATUS:OK,UPTIME:123,QUEUE:0,USB:READY,CONN:YES,VER:2.0.0
VERSION → VERSION:2.0.0
```

## LED Status Indicators
- **Boot Sequence**: 3 quick flashes (200ms on/off)
- **Waiting for Connection**: Slow flashing (500ms intervals)
- **Connected and Ready**: Steady ON
- **Active Processing**: Brief flash during command execution
- **Error State**: Rapid flashing (83ms intervals)

## Building the Firmware

### Prerequisites
1. **PlatformIO** installed (via VS Code extension or CLI)
2. **ESP32 platform** support
3. **Git** (for cloning dependencies)

### Build Commands
```bash
# Navigate to firmware directory
cd "Custom farmer for ESP"

# Build firmware
pio run

# Upload to device (with ESP32-S2 connected)
pio run -t upload

# Monitor serial output
pio device monitor
```

### Build Output
- **Firmware Binary**: `.pio/build/lolin_s2_mini/firmware.bin`
- **Bootloader**: `.pio/build/lolin_s2_mini/bootloader.bin`
- **Partitions**: `.pio/build/lolin_s2_mini/partitions.bin`

## Installation & Flashing

### Method 1: PlatformIO Upload
```bash
pio run -t upload
```

### Method 2: Manual Flashing with esptool
```bash
esptool.py --chip esp32s2 --port COM10 --baud 921600 write_flash -z 0x1000 bootloader.bin 0x8000 partitions.bin 0x10000 firmware.bin
```

### Method 3: Using Octane Flasher
Use the provided Octane Flasher tool for automated flashing with license verification.

## Configuration

### PlatformIO Configuration
The `platformio.ini` file is pre-configured with optimal settings:
- **Board**: lolin_s2_mini
- **Framework**: arduino
- **USB Mode**: Native USB HID (ARDUINO_USB_MODE=0)
- **Build Flags**: Optimized for HID functionality
- **Libraries**: ArduinoJson for command parsing

### Firmware Configuration
Key constants in `main.cpp`:
```cpp
#define FIRMWARE_VERSION "2.0.0"
#define SERIAL_BAUD 115200
#define LED_PIN 15
#define QUEUE_SIZE 64
#define COMMAND_BUFFER_SIZE 512
```

## Testing & Validation

### Hardware Test
1. Connect ESP32-S2 via USB
2. Check Device Manager for "HID-compliant mouse"
3. Verify LED status (steady on when connected)

### Serial Communication Test
1. Open serial monitor at 115200 baud
2. Send `PING` command
3. Expect `PONG ESP32-S2 Octane HID Mouse v2.0.0` response

### Mouse Movement Test
1. Send `M10,5` command
2. Observe mouse cursor movement (10 pixels right, 5 pixels down)
3. Verify smooth movement without stuttering

### Desktop App Integration
1. Run Octane desktop application
2. Verify automatic ESP32 detection
3. Test recoil compensation functionality

## Troubleshooting

### Common Issues

**ESP32 not detected as HID mouse:**
- Check USB cable (must support data transfer)
- Verify firmware flashed correctly
- Try different USB port
- Check Device Manager for errors

**Serial communication fails:**
- Verify baud rate (115200)
- Check COM port in Device Manager
- Try different USB cable
- Reset ESP32 and reconnect

**Mouse movement stuttering:**
- Check USB cable quality
- Verify 1ms command intervals
- Monitor queue status with `STATUS` command
- Check for USB bandwidth limitations

**LED error indication (rapid flashing):**
- Check serial monitor for error messages
- Verify USB HID initialization
- Check memory usage with `STATUS` command
- Try firmware reflash

### Debug Mode
Enable verbose logging by modifying `CORE_DEBUG_LEVEL` in platformio.ini:
```ini
build_flags = 
    -DCORE_DEBUG_LEVEL=3
```

## Performance Specifications
- **Command Processing Latency**: <1ms
- **Movement Capability**: 1000Hz (1000 commands/second)
- **Movement Range**: -127 to +127 pixels per command
- **Queue Capacity**: 64 commands
- **Memory Usage**: <50KB total
- **Uptime Reliability**: 99.9%+ in normal operation

## Integration with Desktop App
This firmware is designed to work seamlessly with the Octane Recoil Control desktop application:
- **Auto-detection**: Responds to ping commands for device discovery
- **Protocol Compatibility**: Supports both JSON and simple text commands
- **Error Handling**: Provides detailed error responses for debugging
- **Status Reporting**: Real-time system status and performance metrics

## License & Support
This firmware is part of the Octane Recoil Control System. For support, documentation, and updates, visit the project repository.

## Version History
- **v2.0.0**: Initial release with full HID mouse functionality
  - USB HID mouse emulation
  - Serial communication protocol
  - Command queue system
  - LED status indication
  - Error handling and recovery
  - Desktop app integration
