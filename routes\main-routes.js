const express = require('express');
const path = require('path');
const router = express.Router();

// Health check endpoint
router.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
    });
});

// Main validation endpoint
router.post('/api/validate', (req, res) => {
    const { db, sanitizeInput, logSecurityEvent } = req.app.locals;
    const clientIP = req.ip || req.socket.remoteAddress || 'unknown';
    
    try {
        let { licenseKey, hardwareId } = req.body;

        console.log(`[${new Date().toISOString()}] POST /api/validate - IP: ${clientIP}`);
        console.log(`Request body:`, JSON.stringify(req.body, null, 2));

        if (!licenseKey || typeof licenseKey !== 'string') {
            logSecurityEvent(clientIP, 'INVALID_REQUEST', 'Missing license key', 'warning');
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Sanitize inputs
        licenseKey = sanitizeInput(licenseKey);
        hardwareId = sanitizeInput(hardwareId || '');

        // Validate hardware ID format (if provided)
        if (hardwareId && hardwareId !== 'discord-check' && !/^[A-Za-z0-9+/]{12,}$/.test(hardwareId)) {
            logSecurityEvent(clientIP, 'INVALID_HWID', `Invalid hardware ID format: ${hardwareId}`, 'warning');
            return res.status(400).json({
                success: false,
                message: 'Invalid hardware ID format'
            });
        }

        console.log(`🔍 Validating license ${licenseKey} for hardware ${hardwareId}`);

        db.get(
            `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
            [licenseKey],
            (err, row) => {
                if (err) {
                    console.error('Database error:', err);
                    logSecurityEvent(clientIP, 'DATABASE_ERROR', err.message, 'error');
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!row) {
                    logSecurityEvent(clientIP, 'INVALID_LICENSE', `License not found: ${licenseKey}`, 'warning');
                    return res.json({
                        success: false,
                        message: 'Invalid license key'
                    });
                }

                // Check if license is expired
                const now = new Date();
                const expiresAt = new Date(row.expires_at);
                
                if (now > expiresAt) {
                    logSecurityEvent(clientIP, 'EXPIRED_LICENSE', `Expired license: ${licenseKey}`, 'info');
                    return res.json({
                        success: false,
                        message: 'License has expired'
                    });
                }

                // Update hardware ID if provided and different
                if (hardwareId && hardwareId !== 'discord-check' && row.hardware_id !== hardwareId) {
                    db.run(
                        `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                        [hardwareId, licenseKey],
                        (updateErr) => {
                            if (updateErr) {
                                console.error('Hardware ID update error:', updateErr);
                            } else {
                                console.log(`🔄 Updated hardware ID for ${licenseKey}`);
                            }
                        }
                    );
                }

                const remainingTime = Math.floor((expiresAt - now) / 1000);

                logSecurityEvent(clientIP, 'VALID_LICENSE', `Valid license: ${licenseKey}`, 'info');

                console.log(`✅ License ${licenseKey} validated successfully`);
                
                res.json({
                    success: true,
                    message: 'License valid',
                    remainingTime: remainingTime,
                    expiresAt: row.expires_at
                });
            }
        );
    } catch (error) {
        console.error('Validation error:', error);
        logSecurityEvent(clientIP, 'VALIDATION_ERROR', error.message, 'error');
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Admin page routes
router.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'key-maintenance.html'));
});

router.get('/user-management', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'user-management.html'));
});

router.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'discord.html'));
});

router.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'security-alerts.html'));
});

router.get('/system-status', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'system-status.html'));
});

router.get('/settings', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'settings.html'));
});

router.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'reminders.html'));
});

// Admin routes redirect to index (only if authenticated)
router.get('/admin', (req, res) => {
    // For now, just redirect to index - authentication will be handled by Firebase on frontend
    res.redirect('/');
});

router.get('/admin/', (req, res) => {
    res.redirect('/');
});

router.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'discord.html'));
});

router.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'key-maintenance.html'));
});

router.get('/key-maintenance.html', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'key-maintenance.html'));
});

router.get('/user-management', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'user-management.html'));
});

router.get('/system-status', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'system-status.html'));
});

router.get('/settings', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'settings.html'));
});

router.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'security-alerts.html'));
});

router.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'reminders.html'));
});

// Main route - serve index.html
router.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
});

// Redirect /index.html to /
router.get('/index.html', (req, res) => {
    res.redirect('/');
});

module.exports = router;
