const express = require('express');
const router = express.Router();

// Helper function to format uptime in readable format
function formatUptime(uptimeSeconds) {
    const weeks = Math.floor(uptimeSeconds / 604800);
    const days = Math.floor((uptimeSeconds % 604800) / 86400);
    const hours = Math.floor((uptimeSeconds % 86400) / 3600);
    const minutes = Math.floor((uptimeSeconds % 3600) / 60);
    const seconds = Math.floor(uptimeSeconds % 60);

    const parts = [];

    if (weeks > 0) {
        parts.push(`${weeks} Week${weeks > 1 ? 's' : ''}`);
    }
    if (days > 0) {
        parts.push(`${days} Day${days > 1 ? 's' : ''}`);
    }
    if (hours > 0) {
        parts.push(`${hours} Hour${hours > 1 ? 's' : ''}`);
    }
    if (minutes > 0) {
        parts.push(`${minutes} Minute${minutes > 1 ? 's' : ''}`);
    }
    if (seconds > 0 && parts.length < 2) { // Only show seconds if less than 2 other units
        parts.push(`${seconds} Second${seconds > 1 ? 's' : ''}`);
    }

    if (parts.length === 0) {
        return '0 Seconds';
    }

    // Return up to 2 most significant units
    return parts.slice(0, 2).join(' ');
}

// Discord API endpoints
router.get('/status', (req, res) => {
    // Get real uptime from process
    const uptimeSeconds = Math.floor(process.uptime());

    res.json({
        success: true,
        online: true,
        uptime: formatUptime(uptimeSeconds),
        guilds: 1,
        commandsToday: Math.floor(Math.random() * 50)
    });
});

router.get('/activity', (req, res) => {
    const activities = [
        {
            type: 'COMMAND',
            user: 'User#1234',
            command: '/stats',
            timestamp: new Date(Date.now() - Math.random() * 3600000).toISOString()
        },
        {
            type: 'MESSAGE',
            user: 'Admin#5678',
            content: 'System status check',
            timestamp: new Date(Date.now() - Math.random() * 7200000).toISOString()
        }
    ];
    
    res.json({
        success: true,
        activities: activities
    });
});

router.post('/test', (req, res) => {
    // Mock Discord test
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Discord bot test successful'
        });
    }, 1000);
});

router.post('/daily-report', (req, res) => {
    // Mock daily report generation
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Daily report generated successfully'
        });
    }, 2000);
});

router.post('/restart', (req, res) => {
    // Mock Discord bot restart
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Discord bot restarted successfully'
        });
    }, 3000);
});

// Webhook management endpoints
router.get('/webhooks', (req, res) => {
    // Get current webhook configuration from environment variables
    const webhooks = {
        security_alerts: process.env.WEBHOOK_SECURITY_ALERTS || '',
        esp32_errors: process.env.WEBHOOK_ESP32_ERRORS || '',
        backend_errors: process.env.WEBHOOK_BACKEND_ERRORS || ''
    };

    res.json({
        success: true,
        webhooks: webhooks
    });
});

// Public webhook endpoint for applications (no IP restriction)
router.get('/webhooks/public', (req, res) => {
    // This endpoint can be accessed by applications to get current webhook URLs
    const webhooks = {
        security_alerts: process.env.WEBHOOK_SECURITY_ALERTS || '',
        esp32_errors: process.env.WEBHOOK_ESP32_ERRORS || '',
        backend_errors: process.env.WEBHOOK_BACKEND_ERRORS || ''
    };

    res.json({
        success: true,
        webhooks: webhooks,
        timestamp: new Date().toISOString()
    });
});

router.post('/webhooks', (req, res) => {
    const { webhookType, webhookUrl } = req.body;
    
    if (!webhookType || !webhookUrl) {
        return res.status(400).json({
            success: false,
            error: 'Webhook type and URL are required'
        });
    }
    
    // Validate webhook URL format
    const webhookPattern = /^(\d+)\/([A-Za-z0-9_-]+)$/;
    if (!webhookPattern.test(webhookUrl)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid webhook URL format. Use: channelId/token'
        });
    }
    
    // In production, save to database or config file
    // For now, just simulate success
    console.log(`📡 Webhook updated: ${webhookType} -> ${webhookUrl}`);
    
    res.json({
        success: true,
        message: `${webhookType} webhook updated successfully`
    });
});

router.post('/test-webhook', (req, res) => {
    const { webhookType } = req.body;
    
    if (!webhookType) {
        return res.status(400).json({
            success: false,
            error: 'Webhook type is required'
        });
    }
    
    // Mock webhook test
    setTimeout(() => {
        res.json({
            success: true,
            message: `${webhookType} webhook test successful`
        });
    }, 1000);
});

router.post('/update-token', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            error: 'Token is required'
        });
    }

    // In production, save token securely and restart bot
    console.log('🔑 Discord bot token updated');

    res.json({
        success: true,
        message: 'Bot token updated successfully'
    });
});

// Test Discord bot commands
router.post('/test-commands', (req, res) => {
    // Mock command testing
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Discord bot commands tested successfully',
            results: [
                { command: '/stats', status: 'working' },
                { command: '/help', status: 'working' },
                { command: '/ping', status: 'working' }
            ]
        });
    }, 2000);
});

// Get Discord bot permissions
router.get('/permissions', (req, res) => {
    res.json({
        success: true,
        permissions: {
            administrator: true,
            manageMessages: true,
            sendMessages: true,
            readMessageHistory: true,
            useSlashCommands: true,
            embedLinks: true
        },
        missingPermissions: []
    });
});

// Export Discord bot stats
router.get('/export-stats', (req, res) => {
    const stats = {
        timestamp: new Date().toISOString(),
        uptime: formatUptime(Math.floor(process.uptime())),
        guilds: 1,
        users: Math.floor(Math.random() * 1000) + 100,
        commands: {
            total: Math.floor(Math.random() * 10000) + 1000,
            today: Math.floor(Math.random() * 100) + 10
        },
        messages: {
            sent: Math.floor(Math.random() * 5000) + 500,
            received: Math.floor(Math.random() * 20000) + 2000
        }
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=discord-stats.json');
    res.json(stats);
});

// Get Discord bot logs
router.get('/logs', (req, res) => {
    const logs = [
        {
            timestamp: new Date(Date.now() - 300000).toISOString(),
            level: 'info',
            message: 'Bot connected successfully'
        },
        {
            timestamp: new Date(Date.now() - 600000).toISOString(),
            level: 'info',
            message: 'Slash commands registered'
        },
        {
            timestamp: new Date(Date.now() - 900000).toISOString(),
            level: 'warning',
            message: 'Rate limit warning for API calls'
        },
        {
            timestamp: new Date(Date.now() - 1200000).toISOString(),
            level: 'info',
            message: 'Guild member joined'
        }
    ];

    res.json({
        success: true,
        logs: logs
    });
});

module.exports = router;
