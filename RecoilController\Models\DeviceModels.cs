using System;

namespace RecoilController.Models
{
    /// <summary>
    /// Represents the connection status of the ESP32 device
    /// </summary>
    public enum DeviceStatus
    {
        Disconnected,
        Connected,
        Flashing,
        Error
    }

    /// <summary>
    /// Information about the connected ESP32 HID device
    /// </summary>
    public class DeviceInfo
    {
        public string PortName { get; set; }
        public string FirmwareVersion { get; set; }
        public DeviceStatus Status { get; set; }
        public DateTime LastSeen { get; set; }
        public string ErrorMessage { get; set; }

        // HID-specific properties
        public string HardwareId { get; set; }
        public bool HasLicense { get; set; }
        public bool IsAuthenticated { get; set; }
        public string CurrentWeapon { get; set; }
        public bool HidConnected { get; set; }
        public float Sensitivity { get; set; } = 1.0f;
        public float ScopeMultiplier { get; set; } = 1.0f;
        public bool RecoilActive { get; set; }

        public DeviceInfo()
        {
            Status = DeviceStatus.Disconnected;
            LastSeen = DateTime.MinValue;
            CurrentWeapon = "Unknown";
        }

        public bool IsConnected => Status == DeviceStatus.Connected;
        public bool IsReady => IsConnected && IsAuthenticated && HidConnected;
        public bool RequiresFirmwareUpdate => !string.IsNullOrEmpty(FirmwareVersion) &&
                                             Version.TryParse(FirmwareVersion, out var current) &&
                                             current < new Version("1.0.0");
    }

    /// <summary>
    /// Commands that can be sent to the ESP32 device
    /// </summary>
    public enum DeviceCommand : byte
    {
        SetWeapon = 0x01,
        StartRecoil = 0x02,
        StopRecoil = 0x03,
        SetDelay = 0x04,
        GetVersion = 0x05,
        SetSensitivity = 0x06,
        SetScope = 0x07,
        Ping = 0x08
    }

    /// <summary>
    /// Response codes from the ESP32 device
    /// </summary>
    public enum DeviceResponse : byte
    {
        Success = 0x00,
        Error = 0x01,
        InvalidCommand = 0x02,
        InvalidParameter = 0x03,
        NotReady = 0x04,
        VersionInfo = 0x05,
        Pong = 0x06,
        StatusMessage = 0x07
    }

    /// <summary>
    /// A command packet to send to the ESP32
    /// </summary>
    public class DeviceCommandPacket
    {
        public DeviceCommand Command { get; set; }
        public byte[] Data { get; set; }

        public DeviceCommandPacket(DeviceCommand command, params byte[] data)
        {
            Command = command;
            Data = data ?? new byte[0];
        }

        /// <summary>
        /// Converts the command packet to bytes for transmission
        /// </summary>
        public byte[] ToBytes()
        {
            var packet = new byte[1 + Data.Length];
            packet[0] = (byte)Command;
            if (Data.Length > 0)
                Array.Copy(Data, 0, packet, 1, Data.Length);
            return packet;
        }
    }

    /// <summary>
    /// A response packet received from the ESP32
    /// </summary>
    public class DeviceResponsePacket
    {
        public DeviceResponse Response { get; set; }
        public byte[] Data { get; set; }
        public DateTime Timestamp { get; set; }

        public DeviceResponsePacket(DeviceResponse response, byte[] data = null)
        {
            Response = response;
            Data = data ?? new byte[0];
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// Gets the response data as a string (for version info, etc.)
        /// </summary>
        public string GetDataAsString()
        {
            return Data?.Length > 0 ? System.Text.Encoding.UTF8.GetString(Data) : string.Empty;
        }
    }
}
