<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Alerts - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-shield-alt"></i> Security Alerts</h1>
                <p>Monitor security events and threat detection</p>
            </div>

            <div class="dashboard-grid">
                <!-- Alert Summary -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Alert Summary</h3>
                    </div>
                    <div class="card-content">
                        <div class="alert-stats">
                            <div class="alert-stat critical">
                                <div class="stat-value" id="critical-alerts">0</div>
                                <div class="stat-label">Critical</div>
                            </div>
                            <div class="alert-stat high">
                                <div class="stat-value" id="high-alerts">0</div>
                                <div class="stat-label">High</div>
                            </div>
                            <div class="alert-stat medium">
                                <div class="stat-value" id="medium-alerts">0</div>
                                <div class="stat-label">Medium</div>
                            </div>
                            <div class="alert-stat low">
                                <div class="stat-value" id="low-alerts">0</div>
                                <div class="stat-label">Low</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-tools"></i> Quick Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="action-buttons">
                            <button class="btn btn-danger" onclick="clearAllAlerts()">
                                <i class="fas fa-trash"></i> Clear All
                            </button>
                            <button class="btn btn-warning" onclick="markAllRead()">
                                <i class="fas fa-check"></i> Mark All Read
                            </button>
                            <button class="btn btn-primary" onclick="exportAlerts()">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <button class="btn btn-secondary" onclick="refreshAlerts()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Alert Filters -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-filter"></i> Filters</h3>
                    </div>
                    <div class="card-content">
                        <div class="filter-controls">
                            <select id="severity-filter">
                                <option value="">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="warning">Warning</option>
                                <option value="info">Info</option>
                            </select>
                            <select id="type-filter">
                                <option value="">All Types</option>
                                <option value="LOGIN_ATTEMPT">Login Attempts</option>
                                <option value="FAILED_AUTH">Failed Auth</option>
                                <option value="SUSPICIOUS_ACTIVITY">Suspicious Activity</option>
                                <option value="SYSTEM_ERROR">System Errors</option>
                            </select>
                            <input type="date" id="date-filter" placeholder="Filter by date">
                        </div>
                    </div>
                </div>

                <!-- Security Events -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> Security Events</h3>
                        <div class="card-actions">
                            <span id="events-count">Loading...</span>
                            <button id="auto-refresh" class="btn btn-sm btn-secondary">
                                <i class="fas fa-sync-alt"></i> Auto Refresh: OFF
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="events-container">
                            <div id="security-events" class="events-list">
                                <div class="loading-state">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <p>Loading security events...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Threat Intelligence -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-brain"></i> Threat Intelligence</h3>
                    </div>
                    <div class="card-content">
                        <div class="threat-grid">
                            <div class="threat-item">
                                <div class="threat-icon">
                                    <i class="fas fa-user-secret"></i>
                                </div>
                                <div class="threat-info">
                                    <h4>Suspicious IPs</h4>
                                    <p id="suspicious-ips">0 detected</p>
                                </div>
                            </div>
                            <div class="threat-item">
                                <div class="threat-icon">
                                    <i class="fas fa-bug"></i>
                                </div>
                                <div class="threat-info">
                                    <h4>Debugger Attempts</h4>
                                    <p id="debugger-attempts">0 blocked</p>
                                </div>
                            </div>
                            <div class="threat-item">
                                <div class="threat-icon">
                                    <i class="fas fa-shield-virus"></i>
                                </div>
                                <div class="threat-info">
                                    <h4>False Flag Detection</h4>
                                    <p id="false-flags">0 identified</p>
                                </div>
                            </div>
                            <div class="threat-item">
                                <div class="threat-icon">
                                    <i class="fas fa-ban"></i>
                                </div>
                                <div class="threat-info">
                                    <h4>Blocked Requests</h4>
                                    <p id="blocked-requests">0 today</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/admin-modern.js"></script>
    <script>
        let autoRefreshInterval = null;
        let autoRefreshEnabled = false;

        document.addEventListener('DOMContentLoaded', function() {
            loadSecurityEvents();
            loadAlertStats();
            loadThreatIntelligence();
            
            // Filter controls
            document.getElementById('severity-filter').addEventListener('change', loadSecurityEvents);
            document.getElementById('type-filter').addEventListener('change', loadSecurityEvents);
            document.getElementById('date-filter').addEventListener('change', loadSecurityEvents);
            
            // Auto refresh toggle
            document.getElementById('auto-refresh').addEventListener('click', toggleAutoRefresh);
        });

        async function loadSecurityEvents() {
            try {
                const severityFilter = document.getElementById('severity-filter').value;
                const typeFilter = document.getElementById('type-filter').value;
                const dateFilter = document.getElementById('date-filter').value;
                
                let url = '/api/security/events?limit=50';
                if (severityFilter) url += `&severity=${severityFilter}`;
                if (typeFilter) url += `&type=${typeFilter}`;
                if (dateFilter) url += `&date=${dateFilter}`;
                
                const response = await fetch(url);
                const result = await response.json();
                
                const eventsContainer = document.getElementById('security-events');
                const eventsCount = document.getElementById('events-count');
                
                if (result.success && result.events.length > 0) {
                    eventsCount.textContent = `${result.events.length} events`;
                    eventsContainer.innerHTML = result.events.map(event => `
                        <div class="event-item ${event.severity}">
                            <div class="event-icon">
                                <i class="fas ${getEventIcon(event.type)}"></i>
                            </div>
                            <div class="event-content">
                                <div class="event-header">
                                    <span class="event-type">${event.type}</span>
                                    <span class="event-severity ${event.severity}">${event.severity.toUpperCase()}</span>
                                    <span class="event-time">${new Date(event.timestamp).toLocaleString()}</span>
                                </div>
                                <div class="event-description">${event.description}</div>
                                <div class="event-details">
                                    ${event.username ? `<span>User: ${event.username}</span>` : ''}
                                    ${event.ip_address ? `<span>IP: ${event.ip_address}</span>` : ''}
                                </div>
                            </div>
                            <div class="event-actions">
                                <button class="btn btn-sm btn-secondary" onclick="viewEventDetails(${event.id})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteEvent(${event.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('');
                } else {
                    eventsCount.textContent = '0 events';
                    eventsContainer.innerHTML = '<div class="empty-state">No security events found</div>';
                }
            } catch (error) {
                console.error('Error loading security events:', error);
                document.getElementById('security-events').innerHTML = '<div class="error-state">Error loading events</div>';
            }
        }

        async function loadAlertStats() {
            try {
                const response = await fetch('/api/security/stats');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('critical-alerts').textContent = result.stats.critical || 0;
                    document.getElementById('high-alerts').textContent = result.stats.high || 0;
                    document.getElementById('medium-alerts').textContent = result.stats.warning || 0;
                    document.getElementById('low-alerts').textContent = result.stats.info || 0;
                }
            } catch (error) {
                console.error('Error loading alert stats:', error);
            }
        }

        async function loadThreatIntelligence() {
            try {
                const response = await fetch('/api/security/threats');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('suspicious-ips').textContent = `${result.threats.suspiciousIPs || 0} detected`;
                    document.getElementById('debugger-attempts').textContent = `${result.threats.debuggerAttempts || 0} blocked`;
                    document.getElementById('false-flags').textContent = `${result.threats.falseFlags || 0} identified`;
                    document.getElementById('blocked-requests').textContent = `${result.threats.blockedRequests || 0} today`;
                }
            } catch (error) {
                console.error('Error loading threat intelligence:', error);
            }
        }

        function getEventIcon(type) {
            const icons = {
                'LOGIN_ATTEMPT': 'fa-sign-in-alt',
                'FAILED_AUTH': 'fa-times-circle',
                'SUSPICIOUS_ACTIVITY': 'fa-user-secret',
                'SYSTEM_ERROR': 'fa-exclamation-triangle',
                'LICENSE_CREATED': 'fa-key',
                'DEBUGGER_DETECTED': 'fa-bug'
            };
            return icons[type] || 'fa-info-circle';
        }

        function toggleAutoRefresh() {
            const button = document.getElementById('auto-refresh');
            
            if (autoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshEnabled = false;
                button.innerHTML = '<i class="fas fa-sync-alt"></i> Auto Refresh: OFF';
                button.className = 'btn btn-sm btn-secondary';
            } else {
                autoRefreshInterval = setInterval(() => {
                    loadSecurityEvents();
                    loadAlertStats();
                    loadThreatIntelligence();
                }, 10000); // Refresh every 10 seconds
                autoRefreshEnabled = true;
                button.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Auto Refresh: ON';
                button.className = 'btn btn-sm btn-success';
            }
        }

        function refreshAlerts() {
            loadSecurityEvents();
            loadAlertStats();
            loadThreatIntelligence();
            showNotification('Security alerts refreshed', 'success');
        }

        async function clearAllAlerts() {
            if (!confirm('Are you sure you want to clear all security alerts?')) return;
            
            try {
                const response = await fetch('/api/security/events', { method: 'DELETE' });
                const result = await response.json();
                
                if (result.success) {
                    showNotification('All alerts cleared successfully', 'success');
                    loadSecurityEvents();
                    loadAlertStats();
                } else {
                    showNotification('Failed to clear alerts: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error clearing alerts: ' + error.message, 'error');
            }
        }

        function markAllRead() {
            showNotification('All alerts marked as read', 'success');
        }

        function exportAlerts() {
            showNotification('Exporting security alerts...', 'info');
            // Implementation for exporting alerts
        }

        function viewEventDetails(eventId) {
            showNotification(`Viewing details for event ${eventId}`, 'info');
            // Implementation for viewing event details
        }

        async function deleteEvent(eventId) {
            if (!confirm('Are you sure you want to delete this event?')) return;
            
            try {
                const response = await fetch(`/api/security/events/${eventId}`, { method: 'DELETE' });
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Event deleted successfully', 'success');
                    loadSecurityEvents();
                } else {
                    showNotification('Failed to delete event: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error deleting event: ' + error.message, 'error');
            }
        }

    </script>

    <script src="js/shared-utils.js"></script>
    <script>
        // Initialize shared components
        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
        });

        // Replace showNotification with AdminUtils.showNotification
        function showNotification(message, type) {
            AdminUtils.showNotification(message, type);
        }
    </script>
</body>
</html>
