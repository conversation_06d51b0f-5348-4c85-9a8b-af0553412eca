using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace RecoilController.Security
{
    /// <summary>
    /// Military-grade security system with real-time threat detection and Discord logging
    /// </summary>
    public static class AdvancedSecurityManager
    {
        private static readonly HttpClient _httpClient = new HttpClient();
        private static readonly Timer _securityTimer;
        private static readonly string _discordWebhook = "https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy";
        private static readonly string _originalHash;
        private static readonly List<string> _securityLogs = new List<string>();
        
        private static bool _isSecurityActive = false;
        private static int _threatLevel = 0;
        private static int _tamperAttempts = 0;
        private static DateTime _lastThreatDetection = DateTime.MinValue;
        
        // Security constants
        private const int MAX_TAMPER_ATTEMPTS = 3;
        private const int CRITICAL_THREAT_LEVEL = 5;
        private const int SECURITY_CHECK_INTERVAL = 15000; // 15 seconds
        private const int THREAT_COOLDOWN_MINUTES = 5;

        // Windows API for advanced protection
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("kernel32.dll")]
        private static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll")]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        [DllImport("ntdll.dll")]
        private static extern int NtSetInformationProcess(IntPtr processHandle, int processInformationClass, ref int processInformation, int processInformationLength);

        [DllImport("kernel32.dll")]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("psapi.dll")]
        private static extern bool EnumProcessModules(IntPtr hProcess, [Out] IntPtr[] lphModule, uint cb, out uint lpcbNeeded);

        [DllImport("psapi.dll")]
        private static extern uint GetModuleBaseName(IntPtr hProcess, IntPtr hModule, StringBuilder lpBaseName, uint nSize);

        static AdvancedSecurityManager()
        {
            _originalHash = CalculateAssemblyHash();
            _securityTimer = new Timer(PerformSecurityScan, null, Timeout.Infinite, Timeout.Infinite);
        }

        /// <summary>
        /// Initializes the advanced security system
        /// </summary>
        public static async Task InitializeAsync()
        {
            try
            {
                _isSecurityActive = true;
                
                // Log security initialization
                await LogSecurityEvent("🔒 Advanced Security System Initialized", "INFO");
                
                // Enable all protection layers
                EnableAntiDebugging();
                EnableMemoryProtection();
                EnableProcessProtection();
                
                // Start continuous monitoring
                _securityTimer.Change(SECURITY_CHECK_INTERVAL, SECURITY_CHECK_INTERVAL);
                
                // Perform initial security scan
                await PerformComprehensiveSecurityScan();
                
                Console.WriteLine("🛡️ Advanced Security Manager: All protection layers active");
            }
            catch (Exception ex)
            {
                await LogSecurityEvent($"❌ Security initialization failed: {ex.Message}", "CRITICAL");
                Environment.FailFast("Security system compromised during initialization");
            }
        }

        /// <summary>
        /// Performs comprehensive security scan
        /// </summary>
        private static async Task PerformComprehensiveSecurityScan()
        {
            var threats = new List<string>();

            // Check for debuggers
            if (IsDebuggerAttached())
                threats.Add("Debugger detected");

            // Check for analysis tools
            var analysisTools = DetectAnalysisTools();
            if (analysisTools.Count > 0)
                threats.Add($"Analysis tools detected: {string.Join(", ", analysisTools)}");

            // Check assembly integrity
            if (!VerifyAssemblyIntegrity())
                threats.Add("Assembly integrity compromised");

            // Check for memory manipulation
            if (DetectMemoryManipulation())
                threats.Add("Memory manipulation detected");

            // Check for suspicious processes
            var suspiciousProcesses = DetectSuspiciousProcesses();
            if (suspiciousProcesses.Count > 0)
                threats.Add($"Suspicious processes: {string.Join(", ", suspiciousProcesses)}");

            // Check for VM/sandbox environment
            if (DetectVirtualEnvironment())
                threats.Add("Virtual environment detected");

            // Process threats
            if (threats.Count > 0)
            {
                _threatLevel += threats.Count;
                await HandleSecurityThreats(threats);
            }
            else
            {
                // Gradually reduce threat level if no threats detected
                _threatLevel = Math.Max(0, _threatLevel - 1);
            }
        }

        /// <summary>
        /// Handles detected security threats
        /// </summary>
        private static async Task HandleSecurityThreats(List<string> threats)
        {
            _tamperAttempts++;
            _lastThreatDetection = DateTime.Now;

            var threatMessage = $"🚨 SECURITY THREAT DETECTED (Level {_threatLevel})\n" +
                               $"Attempt #{_tamperAttempts}/{MAX_TAMPER_ATTEMPTS}\n" +
                               $"Threats: {string.Join(", ", threats)}\n" +
                               $"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            await LogSecurityEvent(threatMessage, "WARNING");

            // Escalate response based on threat level
            if (_threatLevel >= CRITICAL_THREAT_LEVEL || _tamperAttempts >= MAX_TAMPER_ATTEMPTS)
            {
                await HandleCriticalThreat(threats);
            }
            else
            {
                // Log warning silently (no popup to avoid alerting user)
                Console.WriteLine($"⚠️ Security Warning: {string.Join(", ", threats)} (Attempt {_tamperAttempts}/{MAX_TAMPER_ATTEMPTS})");
            }
        }

        /// <summary>
        /// Handles critical security threats
        /// </summary>
        private static async Task HandleCriticalThreat(List<string> threats)
        {
            var criticalMessage = $"🔥 CRITICAL SECURITY BREACH DETECTED\n" +
                                 $"Multiple threats: {string.Join(", ", threats)}\n" +
                                 $"Application terminating immediately\n" +
                                 $"Hardware ID: {GetHardwareId()}\n" +
                                 $"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

            await LogSecurityEvent(criticalMessage, "CRITICAL");

            // Log critical breach silently (no popup to avoid alerting user)
            Console.WriteLine("🔥 CRITICAL SECURITY BREACH - Terminating silently");

            // Secure shutdown
            Environment.FailFast("Critical security violation - application terminated");
        }

        /// <summary>
        /// Logs security events to Discord webhook
        /// </summary>
        private static async Task LogSecurityEvent(string message, string severity)
        {
            try
            {
                _securityLogs.Add($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{severity}] {message}");

                var embed = new
                {
                    embeds = new[]
                    {
                        new
                        {
                            title = $"🛡️ Octane Security Alert - {severity}",
                            description = message,
                            color = severity switch
                            {
                                "INFO" => 0x00FF00,      // Green
                                "WARNING" => 0xFFFF00,   // Yellow
                                "CRITICAL" => 0xFF0000,  // Red
                                _ => 0x808080             // Gray
                            },
                            fields = new[]
                            {
                                new { name = "Hardware ID", value = GetHardwareId().Substring(0, 16) + "...", inline = true },
                                new { name = "Threat Level", value = _threatLevel.ToString(), inline = true },
                                new { name = "Tamper Attempts", value = $"{_tamperAttempts}/{MAX_TAMPER_ATTEMPTS}", inline = true }
                            },
                            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                            footer = new { text = "Octane Security System" }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(embed);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_discordWebhook, content);
                
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to send Discord notification: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending security log: {ex.Message}");
            }
        }

        /// <summary>
        /// Detects if debugger is attached using multiple methods
        /// </summary>
        private static bool IsDebuggerAttached()
        {
            try
            {
                // Method 1: Standard .NET check
                if (Debugger.IsAttached) return true;

                // Method 2: Windows API check
                if (IsDebuggerPresent()) return true;

                // Method 3: Remote debugger check
                bool remoteDebugger = false;
                CheckRemoteDebuggerPresent(GetCurrentProcess(), ref remoteDebugger);
                if (remoteDebugger) return true;

                // Method 4: Timing-based detection
                var sw = Stopwatch.StartNew();
                Thread.Sleep(1);
                sw.Stop();
                if (sw.ElapsedMilliseconds > 10) return true; // Debugger slows execution

                return false;
            }
            catch
            {
                return true; // Assume compromised if check fails
            }
        }

        /// <summary>
        /// Detects known analysis and cracking tools
        /// </summary>
        private static List<string> DetectAnalysisTools()
        {
            var detectedTools = new List<string>();
            var suspiciousProcesses = new[]
            {
                // Debuggers
                "ollydbg", "x64dbg", "x32dbg", "windbg", "ida", "ida64", "idaw", "idaw64",
                "ghidra", "radare2", "r2", "cutter", "binaryninja", "hopper",
                
                // .NET Tools
                "dnspy", "reflexil", "de4dot", "confuserex", "dotpeek", "ilspy",
                "telerik", "redgate", "jetbrains",
                
                // Memory Tools
                "cheatengine", "artmoney", "tsearch", "scanmem", "memoryview",
                "processhacker", "procexp", "procmon", "vmmap", "rammap",
                
                // Network Analysis
                "wireshark", "fiddler", "burpsuite", "owasp", "postman",
                
                // Reverse Engineering
                "pestudio", "peview", "lordpe", "importrec", "scylla", "megadumper",
                "upx", "vmprotect", "themida", "enigma", "aspack", "pecompact"
            };

            try
            {
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        foreach (var suspicious in suspiciousProcesses)
                        {
                            if (processName.Contains(suspicious))
                            {
                                detectedTools.Add(process.ProcessName);
                                break;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }
            }
            catch
            {
                // If we can't enumerate processes, assume compromise
                detectedTools.Add("Process enumeration blocked");
            }

            return detectedTools;
        }

        /// <summary>
        /// Verifies assembly integrity
        /// </summary>
        private static bool VerifyAssemblyIntegrity()
        {
            try
            {
                var currentHash = CalculateAssemblyHash();
                return currentHash.Equals(_originalHash, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculates assembly hash for integrity verification
        /// </summary>
        private static string CalculateAssemblyHash()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var assemblyPath = assembly.Location;
                
                if (string.IsNullOrEmpty(assemblyPath))
                {
                    // Handle single-file deployment
                    assemblyPath = Process.GetCurrentProcess().MainModule?.FileName ?? "";
                }

                using var sha256 = SHA256.Create();
                var fileBytes = File.ReadAllBytes(assemblyPath);
                var hashBytes = sha256.ComputeHash(fileBytes);
                return Convert.ToBase64String(hashBytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Detects memory manipulation attempts
        /// </summary>
        private static bool DetectMemoryManipulation()
        {
            try
            {
                // Check for common DLL injection techniques
                var suspiciousDlls = new[] { "inject", "hook", "detour", "patch", "mod" };
                var currentProcess = Process.GetCurrentProcess();
                
                foreach (ProcessModule module in currentProcess.Modules)
                {
                    var moduleName = module.ModuleName.ToLower();
                    foreach (var suspicious in suspiciousDlls)
                    {
                        if (moduleName.Contains(suspicious))
                            return true;
                    }
                }

                return false;
            }
            catch
            {
                return true; // Assume compromised if check fails
            }
        }

        /// <summary>
        /// Detects suspicious processes that might indicate analysis environment
        /// </summary>
        private static List<string> DetectSuspiciousProcesses()
        {
            var suspicious = new List<string>();
            var suspiciousNames = new[]
            {
                "vmware", "virtualbox", "vbox", "qemu", "sandboxie", "wine",
                "analyst", "malware", "virus", "sample", "test", "debug"
            };

            try
            {
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        foreach (var name in suspiciousNames)
                        {
                            if (processName.Contains(name))
                            {
                                suspicious.Add(process.ProcessName);
                                break;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore access denied
                    }
                }
            }
            catch
            {
                suspicious.Add("Process enumeration failed");
            }

            return suspicious;
        }

        /// <summary>
        /// Detects virtual machine or sandbox environment
        /// </summary>
        private static bool DetectVirtualEnvironment()
        {
            try
            {
                // Check for VM-specific registry keys, files, and hardware
                var vmIndicators = new[]
                {
                    @"SYSTEM\CurrentControlSet\Services\VBoxService",
                    @"SYSTEM\CurrentControlSet\Services\VMTools",
                    @"SOFTWARE\VMware, Inc.\VMware Tools"
                };

                // This would need proper registry checking implementation
                // For now, return false to avoid false positives
                return false;
            }
            catch
            {
                return true;
            }
        }

        /// <summary>
        /// Enables anti-debugging protection
        /// </summary>
        private static void EnableAntiDebugging()
        {
            try
            {
                var process = GetCurrentProcess();
                int debugFlag = 1;
                NtSetInformationProcess(process, 0x1e, ref debugFlag, sizeof(int));
            }
            catch
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Enables memory protection
        /// </summary>
        private static void EnableMemoryProtection()
        {
            try
            {
                // Implementation would include memory encryption and protection
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Enables process protection
        /// </summary>
        private static void EnableProcessProtection()
        {
            try
            {
                // Set process priority and affinity for protection
                Process.GetCurrentProcess().PriorityClass = ProcessPriorityClass.High;
            }
            catch
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Gets hardware ID for tracking
        /// </summary>
        private static string GetHardwareId()
        {
            try
            {
                // This would integrate with the existing authentication system
                return Environment.MachineName + "_" + Environment.UserName;
            }
            catch
            {
                return "UNKNOWN";
            }
        }

        /// <summary>
        /// Periodic security scan callback
        /// </summary>
        private static async void PerformSecurityScan(object state)
        {
            if (!_isSecurityActive) return;

            try
            {
                await PerformComprehensiveSecurityScan();
            }
            catch (Exception ex)
            {
                await LogSecurityEvent($"Security scan failed: {ex.Message}", "WARNING");
            }
        }

        /// <summary>
        /// Disables security system (for testing only)
        /// </summary>
        public static async Task DisableAsync()
        {
            _isSecurityActive = false;
            _securityTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            await LogSecurityEvent("🔓 Security system disabled", "INFO");
        }

        /// <summary>
        /// Gets current security status
        /// </summary>
        public static SecurityStatus GetStatus()
        {
            return new SecurityStatus
            {
                IsActive = _isSecurityActive,
                ThreatLevel = _threatLevel,
                TamperAttempts = _tamperAttempts,
                LastThreatDetection = _lastThreatDetection,
                SecurityLogs = _securityLogs.ToArray()
            };
        }
    }

    /// <summary>
    /// Security status information
    /// </summary>
    public class SecurityStatus
    {
        public bool IsActive { get; set; }
        public int ThreatLevel { get; set; }
        public int TamperAttempts { get; set; }
        public DateTime LastThreatDetection { get; set; }
        public string[] SecurityLogs { get; set; } = Array.Empty<string>();
    }
}
