using System;

namespace RecoilController.Models
{
    /// <summary>
    /// Represents a 2D vector for recoil movement data
    /// </summary>
    public struct Vector2
    {
        public float X { get; set; }
        public float Y { get; set; }

        public Vector2(float x, float y)
        {
            X = x;
            Y = y;
        }

        public static Vector2 Zero => new Vector2(0, 0);

        public static Vector2 operator +(Vector2 a, Vector2 b)
        {
            return new Vector2(a.X + b.X, a.Y + b.Y);
        }

        public static Vector2 operator -(Vector2 a, Vector2 b)
        {
            return new Vector2(a.X - b.X, a.Y - b.Y);
        }

        public static Vector2 operator *(Vector2 a, float scalar)
        {
            return new Vector2(a.X * scalar, a.Y * scalar);
        }

        public static Vector2 operator /(Vector2 a, float scalar)
        {
            return new Vector2(a.X / scalar, a.Y / scalar);
        }

        public float Magnitude => (float)Math.Sqrt(X * X + Y * Y);

        public Vector2 Normalized
        {
            get
            {
                float mag = Magnitude;
                return mag > 0 ? this / mag : Zero;
            }
        }

        public override string ToString()
        {
            return $"({X:F6}, {Y:F6})";
        }

        public override bool Equals(object obj)
        {
            return obj is Vector2 other && Math.Abs(X - other.X) < 0.0001f && Math.Abs(Y - other.Y) < 0.0001f;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(X, Y);
        }
    }
}
