<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-users"></i> User Management</h1>
                <p>Manage user accounts, permissions, and activity monitoring</p>
            </div>

            <div class="dashboard-grid">
                <!-- User Statistics -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> User Statistics</h3>
                    </div>
                    <div class="card-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="total-users">0</div>
                                <div class="stat-label">Total Users</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="active-users">0</div>
                                <div class="stat-label">Active Users</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="banned-users">0</div>
                                <div class="stat-label">Banned Users</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="online-users">0</div>
                                <div class="stat-label">Online Now</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Search and Filters -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-search"></i> Search & Filter</h3>
                    </div>
                    <div class="card-content">
                        <div class="search-controls">
                            <div class="form-group">
                                <input type="text" id="user-search" placeholder="Search by license key, HWID, or IP...">
                                <button id="search-btn" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                            <div class="filter-group">
                                <select id="status-filter">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="banned">Banned</option>
                                    <option value="expired">Expired</option>
                                </select>
                                <select id="duration-filter">
                                    <option value="">All Durations</option>
                                    <option value="1day">1 Day</option>
                                    <option value="1week">1 Week</option>
                                    <option value="1month">1 Month</option>
                                    <option value="lifetime">Lifetime</option>
                                </select>
                                <button id="clear-filters" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="quick-actions">
                            <button class="btn btn-warning" onclick="banAllExpired()">
                                <i class="fas fa-ban"></i>
                                Ban Expired Users
                            </button>
                            <button class="btn btn-info" onclick="resetAllHWID()">
                                <i class="fas fa-redo"></i>
                                Reset All HWID
                            </button>
                            <button class="btn btn-success" onclick="exportUserData()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                            <button class="btn btn-primary" onclick="refreshUserList()">
                                <i class="fas fa-sync-alt"></i>
                                Refresh List
                            </button>
                        </div>
                    </div>
                </div>

                <!-- User List -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> User List</h3>
                        <div class="card-actions">
                            <span id="user-count">Loading...</span>
                            <button id="refresh-users" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="table-container">
                            <table id="users-table">
                                <thead>
                                    <tr>
                                        <th>License Key</th>
                                        <th>Status</th>
                                        <th>Duration</th>
                                        <th>HWID</th>
                                        <th>Last IP</th>
                                        <th>Last Seen</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="users-tbody">
                                    <tr>
                                        <td colspan="8" class="loading">Loading users...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span id="pagination-info">Showing 0 of 0 users</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="prev-page" class="btn btn-secondary" disabled>
                                    <i class="fas fa-chevron-left"></i> Previous
                                </button>
                                <span id="page-info">Page 1 of 1</span>
                                <button id="next-page" class="btn btn-secondary" disabled>
                                    Next <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script>
        // User Management specific functionality
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {};

        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
            loadUserStats();
            loadUserList();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Search functionality
            document.getElementById('search-btn').addEventListener('click', performSearch);
            document.getElementById('user-search').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') performSearch();
            });

            // Filter functionality
            document.getElementById('status-filter').addEventListener('change', applyFilters);
            document.getElementById('duration-filter').addEventListener('change', applyFilters);
            document.getElementById('clear-filters').addEventListener('click', clearFilters);

            // Pagination
            document.getElementById('prev-page').addEventListener('click', () => changePage(currentPage - 1));
            document.getElementById('next-page').addEventListener('click', () => changePage(currentPage + 1));

            // Refresh button
            document.getElementById('refresh-users').addEventListener('click', refreshUserList);
        }

        async function loadUserStats() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/user-stats');
                if (response.success) {
                    document.getElementById('total-users').textContent = response.stats.total || 0;
                    document.getElementById('active-users').textContent = response.stats.active || 0;
                    document.getElementById('banned-users').textContent = response.stats.banned || 0;
                    document.getElementById('online-users').textContent = response.stats.online || 0;
                }
            } catch (error) {
                console.error('Failed to load user stats:', error);
                AdminUtils.showNotification('Failed to load user statistics', 'error');
            }
        }

        async function loadUserList(page = 1, filters = {}) {
            try {
                AdminUtils.showLoading('users-tbody', 'Loading users...');
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 20,
                    ...filters
                });

                const response = await AdminUtils.apiCall(`/api/admin/users?${params}`);
                
                if (response.success) {
                    displayUserList(response.users);
                    updatePagination(response.pagination);
                } else {
                    throw new Error(response.message || 'Failed to load users');
                }
            } catch (error) {
                console.error('Failed to load user list:', error);
                document.getElementById('users-tbody').innerHTML = 
                    '<tr><td colspan="8" class="error">Error loading users</td></tr>';
                AdminUtils.showNotification('Failed to load user list', 'error');
            }
        }

        function displayUserList(users) {
            const tbody = document.getElementById('users-tbody');
            
            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="empty">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td><code>${user.license_key}</code></td>
                    <td><span class="status ${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
                    <td>${AdminUtils.formatDuration(user.duration)}</td>
                    <td><code>${user.hwid || 'Not Set'}</code></td>
                    <td>${user.last_ip || 'Unknown'}</td>
                    <td>${user.last_used ? AdminUtils.formatTime(user.last_used) : 'Never'}</td>
                    <td>${AdminUtils.formatTime(user.created_at)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="viewUserDetails('${user.license_key}')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="resetHWID('${user.license_key}')" title="Reset HWID">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="btn btn-sm ${user.is_banned ? 'btn-success' : 'btn-danger'}" 
                                    onclick="${user.is_banned ? 'unbanUser' : 'banUser'}('${user.license_key}')" 
                                    title="${user.is_banned ? 'Unban User' : 'Ban User'}">
                                <i class="fas ${user.is_banned ? 'fa-check' : 'fa-ban'}"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function updatePagination(pagination) {
            currentPage = pagination.page;
            totalPages = pagination.totalPages;
            
            document.getElementById('pagination-info').textContent = 
                `Showing ${pagination.start}-${pagination.end} of ${pagination.total} users`;
            document.getElementById('page-info').textContent = 
                `Page ${currentPage} of ${totalPages}`;
            
            document.getElementById('prev-page').disabled = currentPage <= 1;
            document.getElementById('next-page').disabled = currentPage >= totalPages;
            
            document.getElementById('user-count').textContent = `${pagination.total} users`;
        }

        function performSearch() {
            const query = document.getElementById('user-search').value.trim();
            currentFilters.search = query;
            loadUserList(1, currentFilters);
        }

        function applyFilters() {
            const status = document.getElementById('status-filter').value;
            const duration = document.getElementById('duration-filter').value;
            
            currentFilters = {};
            if (status) currentFilters.status = status;
            if (duration) currentFilters.duration = duration;
            
            loadUserList(1, currentFilters);
        }

        function clearFilters() {
            document.getElementById('user-search').value = '';
            document.getElementById('status-filter').value = '';
            document.getElementById('duration-filter').value = '';
            currentFilters = {};
            loadUserList(1);
        }

        function changePage(page) {
            if (page >= 1 && page <= totalPages) {
                loadUserList(page, currentFilters);
            }
        }

        function refreshUserList() {
            loadUserStats();
            loadUserList(currentPage, currentFilters);
            AdminUtils.showNotification('User list refreshed', 'success');
        }

        // User action functions
        async function viewUserDetails(licenseKey) {
            try {
                const response = await AdminUtils.apiCall(`/api/admin/users/${licenseKey}`);
                if (response.success) {
                    const user = response.user;
                    const content = `
                        <div class="user-details">
                            <p><strong>License Key:</strong> <code>${user.license_key}</code></p>
                            <p><strong>Status:</strong> <span class="status ${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'Active' : 'Inactive'}</span></p>
                            <p><strong>Duration:</strong> ${AdminUtils.formatDuration(user.duration)}</p>
                            <p><strong>HWID:</strong> <code>${user.hwid || 'Not Set'}</code></p>
                            <p><strong>Last IP:</strong> ${user.last_ip || 'Unknown'}</p>
                            <p><strong>Last Used:</strong> ${user.last_used ? AdminUtils.formatTime(user.last_used) : 'Never'}</p>
                            <p><strong>Created:</strong> ${AdminUtils.formatTime(user.created_at)}</p>
                            <p><strong>Expires:</strong> ${user.expires_at ? AdminUtils.formatTime(user.expires_at) : 'Never'}</p>
                            <p><strong>Notes:</strong> ${user.notes || 'None'}</p>
                        </div>
                    `;
                    
                    AdminUtils.showModal('User Details', content, [
                        { text: 'Close', class: 'btn-secondary', onclick: 'this.closest(".modal").remove()' }
                    ]);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to load user details', 'error');
            }
        }

        async function resetHWID(licenseKey) {
            if (!confirm('Are you sure you want to reset the HWID for this user?')) return;
            
            try {
                const response = await AdminUtils.apiCall(`/api/admin/users/${licenseKey}/reset-hwid`, {
                    method: 'POST'
                });
                
                if (response.success) {
                    AdminUtils.showNotification('HWID reset successfully', 'success');
                    refreshUserList();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to reset HWID: ' + error.message, 'error');
            }
        }

        async function banUser(licenseKey) {
            if (!confirm('Are you sure you want to ban this user?')) return;
            
            try {
                const response = await AdminUtils.apiCall(`/api/admin/users/${licenseKey}/ban`, {
                    method: 'POST'
                });
                
                if (response.success) {
                    AdminUtils.showNotification('User banned successfully', 'success');
                    refreshUserList();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to ban user: ' + error.message, 'error');
            }
        }

        async function unbanUser(licenseKey) {
            if (!confirm('Are you sure you want to unban this user?')) return;
            
            try {
                const response = await AdminUtils.apiCall(`/api/admin/users/${licenseKey}/unban`, {
                    method: 'POST'
                });
                
                if (response.success) {
                    AdminUtils.showNotification('User unbanned successfully', 'success');
                    refreshUserList();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to unban user: ' + error.message, 'error');
            }
        }

        // Quick action functions
        async function banAllExpired() {
            if (!confirm('Are you sure you want to ban all expired users? This action cannot be undone.')) return;
            
            try {
                const response = await AdminUtils.apiCall('/api/admin/users/ban-expired', {
                    method: 'POST'
                });
                
                if (response.success) {
                    AdminUtils.showNotification(`Banned ${response.count} expired users`, 'success');
                    refreshUserList();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to ban expired users: ' + error.message, 'error');
            }
        }

        async function resetAllHWID() {
            if (!confirm('Are you sure you want to reset HWID for all users? This action cannot be undone.')) return;
            
            try {
                const response = await AdminUtils.apiCall('/api/admin/users/reset-all-hwid', {
                    method: 'POST'
                });
                
                if (response.success) {
                    AdminUtils.showNotification(`Reset HWID for ${response.count} users`, 'success');
                    refreshUserList();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to reset all HWID: ' + error.message, 'error');
            }
        }

        function exportUserData() {
            const params = new URLSearchParams(currentFilters);
            window.open(`/api/admin/users/export?${params}`, '_blank');
            AdminUtils.showNotification('Export started', 'info');
        }
    </script>
</body>
</html>
