#!/bin/bash

# Octane System Test Script
# Comprehensive testing of all system components

echo "🔍 OCTANE SYSTEM TEST STARTING..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run test
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    
    if eval "$test_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS: $test_name${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAIL: $test_name${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Function to run test with output
run_test_with_output() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "${BLUE}Testing: $test_name${NC}"
    
    local output=$(eval "$test_command" 2>&1)
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ PASS: $test_name${NC}"
        echo "   Output: $output"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}❌ FAIL: $test_name${NC}"
        echo "   Error: $output"
        ((TESTS_FAILED++))
        return 1
    fi
}

echo -e "${YELLOW}1. SYSTEM HEALTH CHECKS${NC}"
echo "------------------------"

# Test 1: System Resources
run_test_with_output "CPU Usage" "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1"
run_test_with_output "Memory Usage" "free -m | awk 'NR==2{printf \"%.1f%%\", \$3*100/\$2 }'"
run_test_with_output "Disk Usage" "df -h / | awk 'NR==2{print \$5}'"
run_test_with_output "Load Average" "uptime | awk -F'load average:' '{ print \$2 }'"

echo -e "\n${YELLOW}2. NETWORK CONNECTIVITY${NC}"
echo "-------------------------"

# Test 2: Network
run_test "Internet Connectivity" "ping -c 1 8.8.8.8"
run_test "DNS Resolution" "nslookup google.com"
run_test "Port 3000 Listening" "netstat -tuln | grep :3000"

echo -e "\n${YELLOW}3. APPLICATION SERVICES${NC}"
echo "------------------------"

# Test 3: Services
run_test "PM2 Service Running" "pm2 list | grep octane-auth | grep online"
run_test "MongoDB Service" "systemctl is-active mongod"
run_test "Nginx Service" "systemctl is-active nginx"

echo -e "\n${YELLOW}4. APPLICATION HEALTH${NC}"
echo "-----------------------"

# Test 4: Application Health
run_test_with_output "Backend Health Endpoint" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/api/health | grep 200"
run_test_with_output "Admin Panel Access" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/admin | grep 200"
run_test_with_output "Security Alerts Page" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/security-alerts | grep 200"

echo -e "\n${YELLOW}5. DATABASE CONNECTIVITY${NC}"
echo "--------------------------"

# Test 5: Database
run_test "MongoDB Connection" "mongo --eval 'db.runCommand(\"ismaster\")' --quiet"
run_test "Database Exists" "mongo octane-auth --eval 'db.stats()' --quiet"

echo -e "\n${YELLOW}6. DISCORD INTEGRATION${NC}"
echo "-----------------------"

# Test 6: Discord (basic checks)
run_test "Discord Token Set" "[ ! -z \"\$DISCORD_TOKEN\" ]"
run_test "Discord Config Complete" "[ ! -z \"\$DISCORD_SERVER_ID\" ] && [ ! -z \"\$DISCORD_CHANNEL_ID\" ]"

echo -e "\n${YELLOW}7. FILE PERMISSIONS${NC}"
echo "--------------------"

# Test 7: File Permissions
run_test "Application Directory Readable" "[ -r /opt/octane-auth ]"
run_test "Log Files Writable" "[ -w /opt/octane-auth ]"
run_test "Node Modules Present" "[ -d /opt/octane-auth/node_modules ]"

echo -e "\n${YELLOW}8. SECURITY CHECKS${NC}"
echo "-------------------"

# Test 8: Security
run_test "Firewall Active" "ufw status | grep active"
run_test "SSL Certificate Valid" "[ -f /etc/ssl/certs/octane.crt ] || echo 'SSL not configured'"
run_test "Non-root User Running App" "ps aux | grep node | grep -v root | grep octane"

echo -e "\n${YELLOW}9. PERFORMANCE METRICS${NC}"
echo "-----------------------"

# Test 9: Performance
echo -e "${BLUE}Response Time Test${NC}"
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3000/api/health)
echo "   Backend Response Time: ${RESPONSE_TIME}s"

if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    echo -e "${GREEN}✅ PASS: Response Time < 1s${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}❌ FAIL: Response Time > 1s${NC}"
    ((TESTS_FAILED++))
fi

echo -e "\n${YELLOW}10. LOG ANALYSIS${NC}"
echo "-----------------"

# Test 10: Recent Logs
echo -e "${BLUE}Recent Application Logs:${NC}"
pm2 logs octane-auth --lines 5 --nostream 2>/dev/null || echo "Could not retrieve PM2 logs"

echo -e "\n${BLUE}Recent System Logs:${NC}"
journalctl -u mongod --lines 3 --no-pager 2>/dev/null || echo "Could not retrieve MongoDB logs"

# Final Results
echo -e "\n${YELLOW}TEST SUMMARY${NC}"
echo "============="
echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
echo -e "Total Tests: $((TESTS_PASSED + TESTS_FAILED))"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 ALL TESTS PASSED! System is healthy.${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  Some tests failed. Please check the issues above.${NC}"
    exit 1
fi
