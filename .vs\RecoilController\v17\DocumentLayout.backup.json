{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\viewmodels\\loginviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\viewmodels\\loginviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\viewmodels\\mainviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\views\\loginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\views\\loginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\views\\modernmainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\views\\modernmainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|c:\\users\\<USER>\\desktop\\recoil\\desktop-app\\recoilcontroller\\views\\secureloginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|RecoilController\\RecoilController.csproj|solutionrelative:recoilcontroller\\views\\secureloginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\d1ef5a54a7fe9a5e23cdab3cdd77151e1cbeeaadb627ee9feca9bd135b101326\\-Module-.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0a84cc53936b314ba955b54f50bf4796d8234943f3fd7b93873381b45d404026\\XamlReader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{40ea2e6b-2121-4bb8-a43e-c83c04b51041}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:1:0:{d212f56b-c48a-434c-a121-1c5d80b59b9f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{34e76e81-ee4a-11d0-ae2e-00a0c90fffc3}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "LoginViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\ViewModels\\LoginViewModel.cs", "RelativeDocumentMoniker": "RecoilController\\ViewModels\\LoginViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\ViewModels\\LoginViewModel.cs", "RelativeToolTip": "RecoilController\\ViewModels\\LoginViewModel.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T11:52:17.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\ViewModels\\MainViewModel.cs", "RelativeDocumentMoniker": "RecoilController\\ViewModels\\MainViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\ViewModels\\MainViewModel.cs", "RelativeToolTip": "RecoilController\\ViewModels\\MainViewModel.cs", "ViewState": "AgIAALoBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T11:52:15.404Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\appsettings.json", "RelativeDocumentMoniker": "RecoilController\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\appsettings.json", "RelativeToolTip": "RecoilController\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-26T11:52:12.277Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ModernMainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\ModernMainWindow.xaml", "RelativeDocumentMoniker": "RecoilController\\Views\\ModernMainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\ModernMainWindow.xaml", "RelativeToolTip": "RecoilController\\Views\\ModernMainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-26T11:52:05.048Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "SecureLoginWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\SecureLoginWindow.xaml", "RelativeDocumentMoniker": "RecoilController\\Views\\SecureLoginWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\SecureLoginWindow.xaml", "RelativeToolTip": "RecoilController\\Views\\SecureLoginWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-26T11:51:45.345Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LoginWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\LoginWindow.xaml", "RelativeDocumentMoniker": "RecoilController\\Views\\LoginWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\recoil\\desktop-app\\RecoilController\\Views\\LoginWindow.xaml", "RelativeToolTip": "RecoilController\\Views\\LoginWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-26T09:27:19.236Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "-Module-.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\d1ef5a54a7fe9a5e23cdab3cdd77151e1cbeeaadb627ee9feca9bd135b101326\\-Module-.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\d1ef5a54a7fe9a5e23cdab3cdd77151e1cbeeaadb627ee9feca9bd135b101326\\-Module-.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\d1ef5a54a7fe9a5e23cdab3cdd77151e1cbeeaadb627ee9feca9bd135b101326\\-Module-.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\d1ef5a54a7fe9a5e23cdab3cdd77151e1cbeeaadb627ee9feca9bd135b101326\\-Module-.cs [Read Only]", "ViewState": "AgIAALosAAAAAAAAAAAUwMksAABgAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T07:08:49.839Z", "EditorCaption": " [Read Only]"}, {"$type": "Document", "DocumentIndex": 7, "Title": "XamlReader.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0a84cc53936b314ba955b54f50bf4796d8234943f3fd7b93873381b45d404026\\XamlReader.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0a84cc53936b314ba955b54f50bf4796d8234943f3fd7b93873381b45d404026\\XamlReader.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\.vsdbgsrc\\0a84cc53936b314ba955b54f50bf4796d8234943f3fd7b93873381b45d404026\\XamlReader.cs [Read Only]", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\Temp\\.vsdbgsrc\\0a84cc53936b314ba955b54f50bf4796d8234943f3fd7b93873381b45d404026\\XamlReader.cs [Read Only]", "ViewState": "AgIAAKYBAAAAAAAAAAAmwLUBAAACAAAAAQAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T05:31:48.256Z", "EditorCaption": " [Read Only]"}]}]}]}