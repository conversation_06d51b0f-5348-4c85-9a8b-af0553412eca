// Key Maintenance JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize page
    initializeKeyMaintenance();
});

function initializeKeyMaintenance() {
    // Bind event listeners
    document.getElementById('createLicenseBtn').addEventListener('click', createLicense);
    document.getElementById('searchInput').addEventListener('input', OctaneUtils.debounce(searchLicenses, 300));
    
    // Load initial data
    loadStats();
    loadLicenses();
}

function loadStats() {
    // Load statistics from API
    Promise.all([
        fetch('/api/licenses/count'),
        fetch('/api/users/active')
    ]).then(async ([licensesRes, usersRes]) => {
        const licenses = await licensesRes.json();
        const users = await usersRes.json();
        
        document.getElementById('totalKeys').textContent = licenses.count || 0;
        document.getElementById('activeKeys').textContent = users.count || 0;
        document.getElementById('expiredKeys').textContent = Math.max(0, (licenses.count || 0) - (users.count || 0));
    }).catch(error => {
        console.error('Error loading stats:', error);
        OctaneUtils.showAlert('Error loading statistics', 'danger');
    });
}

async function createLicense() {
    const duration = document.getElementById('duration').value;
    const notes = document.getElementById('notes').value;
    
    // Disable button during creation
    const btn = document.getElementById('createLicenseBtn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
    btn.disabled = true;
    
    try {
        const response = await fetch('/api/admin/licenses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ duration, notes })
        });
        
        const result = await response.json();
        
        if (result.success) {
            OctaneUtils.showAlert(`✅ License created successfully: ${result.license.key}`, 'success');
            document.getElementById('notes').value = '';
            loadStats();
            loadLicenses();
            
            // Copy license key to clipboard
            OctaneUtils.copyToClipboard(result.license.key, '📋 License key copied to clipboard!');
        } else {
            OctaneUtils.showAlert('❌ Failed to create license: ' + result.error, 'danger');
        }
    } catch (error) {
        OctaneUtils.showAlert('❌ Error creating license: ' + error.message, 'danger');
    } finally {
        // Re-enable button
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

function loadLicenses() {
    const tbody = document.getElementById('licensesTableBody');
    tbody.innerHTML = '<tr><td colspan="8" class="loading"><i class="fas fa-spinner fa-spin"></i> Loading licenses...</td></tr>';
    
    fetch('/api/admin/licenses?limit=50')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.licenses.length > 0) {
                tbody.innerHTML = result.licenses.map(license => `
                    <tr class="license-row" data-key="${license.key}">
                        <td>
                            <code>${license.key}</code>
                            <button class="copy-key-btn" onclick="OctaneUtils.copyToClipboard('${license.key}')" title="Copy license key">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                        <td>${formatDuration(license.duration)}</td>
                        <td>
                            <span class="license-status ${license.is_active ? 'active' : 'expired'}">
                                ${license.is_active ? '✅ Active' : '❌ Expired'}
                            </span>
                        </td>
                        <td>
                            <span class="hwid-display ${license.hwid ? 'bound' : ''}" title="${license.hwid || 'Not bound to any device'}">
                                ${license.hwid || 'Not Bound'}
                            </span>
                        </td>
                        <td>${OctaneUtils.formatDate(license.created_at)}</td>
                        <td>${license.expires_at ? OctaneUtils.formatDate(license.expires_at) : 'Never'}</td>
                        <td>${license.notes || '-'}</td>
                        <td>
                            <div class="license-actions">
                                <button class="btn btn-sm btn-secondary" onclick="copyKey('${license.key}')" title="Copy Key">
                                    📋
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewLicenseDetails('${license.key}')" title="View Details">
                                    👁️
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="resetHWID('${license.key}')" title="Reset HWID">
                                    🔄
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteLicense('${license.key}')" title="Delete">
                                    🗑️
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            } else {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 40px; color: var(--text-muted);">No licenses found</td></tr>';
            }
        })
        .catch(error => {
            console.error('Error loading licenses:', error);
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading licenses</td></tr>';
        });
}

function formatDuration(duration) {
    const durations = {
        '1day': '1 Day',
        '1week': '1 Week',
        '1month': '1 Month',
        '3months': '3 Months',
        '6months': '6 Months',
        '1year': '1 Year',
        'lifetime': 'Lifetime'
    };
    return durations[duration] || duration;
}

function copyKey(key) {
    OctaneUtils.copyToClipboard(key, '📋 License key copied to clipboard!');
}

function viewLicenseDetails(key) {
    // Show detailed information about the license
    fetch(`/api/admin/licenses/${key}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const license = result.license;
                const details = `
                    <div style="text-align: left;">
                        <h4>License Details</h4>
                        <p><strong>Key:</strong> ${license.key}</p>
                        <p><strong>Duration:</strong> ${formatDuration(license.duration)}</p>
                        <p><strong>Status:</strong> ${license.is_active ? 'Active' : 'Expired'}</p>
                        <p><strong>HWID:</strong> ${license.hwid || 'Not bound'}</p>
                        <p><strong>Created:</strong> ${OctaneUtils.formatDate(license.created_at)}</p>
                        <p><strong>Expires:</strong> ${license.expires_at ? OctaneUtils.formatDate(license.expires_at) : 'Never'}</p>
                        <p><strong>Last Used:</strong> ${license.last_used ? OctaneUtils.formatDate(license.last_used) : 'Never'}</p>
                        <p><strong>Notes:</strong> ${license.notes || 'None'}</p>
                    </div>
                `;
                
                // Create a simple modal
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                `;
                
                modal.innerHTML = `
                    <div style="background: var(--bg-card); padding: 30px; border-radius: var(--border-radius); max-width: 500px; width: 90%;">
                        ${details}
                        <div style="margin-top: 20px; text-align: center;">
                            <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                        </div>
                    </div>
                `;
                
                modal.className = 'modal';
                modal.onclick = (e) => {
                    if (e.target === modal) modal.remove();
                };
                
                document.body.appendChild(modal);
            } else {
                OctaneUtils.showAlert('Error loading license details', 'danger');
            }
        })
        .catch(error => {
            OctaneUtils.showAlert('Error loading license details: ' + error.message, 'danger');
        });
}

function resetHWID(key) {
    if (!confirm('Are you sure you want to reset the HWID for this license? This will allow it to be used on a different device.')) return;
    
    fetch(`/api/admin/licenses/${key}/reset-hwid`, { method: 'POST' })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                OctaneUtils.showAlert('✅ HWID reset successfully', 'success');
                loadLicenses();
            } else {
                OctaneUtils.showAlert('❌ Failed to reset HWID: ' + result.error, 'danger');
            }
        })
        .catch(error => {
            OctaneUtils.showAlert('❌ Error resetting HWID: ' + error.message, 'danger');
        });
}

function deleteLicense(key) {
    if (!confirm('Are you sure you want to delete this license? This action cannot be undone.')) return;
    
    fetch(`/api/admin/licenses/${key}`, { method: 'DELETE' })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                OctaneUtils.showAlert('✅ License deleted successfully', 'success');
                loadLicenses();
                loadStats();
            } else {
                OctaneUtils.showAlert('❌ Failed to delete license: ' + result.error, 'danger');
            }
        })
        .catch(error => {
            OctaneUtils.showAlert('❌ Error deleting license: ' + error.message, 'danger');
        });
}

function searchLicenses() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const rows = document.querySelectorAll('.license-row');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

function refreshLicenses() {
    loadStats();
    loadLicenses();
    OctaneUtils.showAlert('🔄 Data refreshed', 'info');
}

function deleteExpired() {
    if (!confirm('Are you sure you want to delete all expired licenses? This action cannot be undone.')) return;
    
    fetch('/api/admin/licenses/cleanup-expired', { method: 'DELETE' })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                OctaneUtils.showAlert(`✅ Deleted ${result.deleted} expired licenses`, 'success');
                loadLicenses();
                loadStats();
            } else {
                OctaneUtils.showAlert('❌ Failed to delete expired licenses: ' + result.error, 'danger');
            }
        })
        .catch(error => {
            OctaneUtils.showAlert('❌ Error deleting expired licenses: ' + error.message, 'danger');
        });
}

function exportLicenses() {
    OctaneUtils.showAlert('📊 Export feature coming soon...', 'info');
}

function bulkCreate() {
    const count = prompt('How many licenses would you like to create?', '5');
    if (!count || isNaN(count) || count < 1 || count > 100) {
        OctaneUtils.showAlert('❌ Please enter a valid number between 1 and 100', 'danger');
        return;
    }
    
    const duration = document.getElementById('duration').value;
    
    if (!confirm(`Create ${count} licenses with duration: ${formatDuration(duration)}?`)) return;
    
    fetch('/api/admin/licenses/bulk-create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ count: parseInt(count), duration })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            OctaneUtils.showAlert(`✅ Created ${result.created} licenses successfully`, 'success');
            loadLicenses();
            loadStats();
        } else {
            OctaneUtils.showAlert('❌ Failed to create licenses: ' + result.error, 'danger');
        }
    })
    .catch(error => {
        OctaneUtils.showAlert('❌ Error creating licenses: ' + error.message, 'danger');
    });
}
