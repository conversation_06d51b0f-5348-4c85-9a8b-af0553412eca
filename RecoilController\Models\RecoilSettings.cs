using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace RecoilController.Models
{
    /// <summary>
    /// Main recoil script configuration
    /// </summary>
    public class RecoilSettings : INotifyPropertyChanged
    {
        private bool _recoilEnabled = true;
        private bool _hipfireEnabled = true;
        private bool _humanisationEnabled = true;
        private bool _randomisationEnabled = true;
        private bool _rapidfireEnabled = false;
        private bool _antiAfkEnabled = false;
        private bool _codelockEnabled = false;
        private bool _cursorCheckEnabled = true;

        private double _delayScale = 0.0;
        private double _horizontalPercent = 100.0;
        private double _verticalPercent = 100.0;
        private double _sensitivity = 1.0;
        private double _fov = 90.0;

        private int _afkInterval = 7500;
        private string _crouchKey = "CTRL";
        private string _codelockCode = "0000";

        private string _selectedWeapon = "ak47";
        private bool _isAiming = false;
        private bool _isMoving = false;

        // Combat Features
        public bool RecoilEnabled
        {
            get => _recoilEnabled;
            set => SetProperty(ref _recoilEnabled, value);
        }

        public bool HipfireEnabled
        {
            get => _hipfireEnabled;
            set => SetProperty(ref _hipfireEnabled, value);
        }

        public bool HumanisationEnabled
        {
            get => _humanisationEnabled;
            set => SetProperty(ref _humanisationEnabled, value);
        }

        public bool RandomisationEnabled
        {
            get => _randomisationEnabled;
            set => SetProperty(ref _randomisationEnabled, value);
        }

        // Adjustable Parameters
        public double DelayScale
        {
            get => _delayScale;
            set => SetProperty(ref _delayScale, Math.Max(0, Math.Min(10, value)));
        }

        public double HorizontalPercent
        {
            get => _horizontalPercent;
            set => SetProperty(ref _horizontalPercent, Math.Max(0, Math.Min(200, value)));
        }

        public double VerticalPercent
        {
            get => _verticalPercent;
            set => SetProperty(ref _verticalPercent, Math.Max(0, Math.Min(200, value)));
        }

        public double Sensitivity
        {
            get => _sensitivity;
            set => SetProperty(ref _sensitivity, Math.Max(0.1, Math.Min(10.0, value)));
        }

        public double FOV
        {
            get => _fov;
            set => SetProperty(ref _fov, Math.Max(60, Math.Min(120, value)));
        }

        public bool CursorCheckEnabled
        {
            get => _cursorCheckEnabled;
            set => SetProperty(ref _cursorCheckEnabled, value);
        }

        // Weapon Utility
        public bool RapidfireEnabled
        {
            get => _rapidfireEnabled;
            set => SetProperty(ref _rapidfireEnabled, value);
        }

        // Anti-AFK Utility
        public bool AntiAfkEnabled
        {
            get => _antiAfkEnabled;
            set => SetProperty(ref _antiAfkEnabled, value);
        }

        public int AfkInterval
        {
            get => _afkInterval;
            set => SetProperty(ref _afkInterval, Math.Max(1000, Math.Min(60000, value)));
        }

        public string CrouchKey
        {
            get => _crouchKey;
            set => SetProperty(ref _crouchKey, value ?? "CTRL");
        }

        // Codelock Automation
        public bool CodelockEnabled
        {
            get => _codelockEnabled;
            set => SetProperty(ref _codelockEnabled, value);
        }

        public string CodelockCode
        {
            get => _codelockCode;
            set => SetProperty(ref _codelockCode, value ?? "0000");
        }

        // Current State
        public string SelectedWeapon
        {
            get => _selectedWeapon;
            set => SetProperty(ref _selectedWeapon, value ?? "ak47");
        }

        public bool IsAiming
        {
            get => _isAiming;
            set => SetProperty(ref _isAiming, value);
        }

        public bool IsMoving
        {
            get => _isMoving;
            set => SetProperty(ref _isMoving, value);
        }

        // Calculated Properties
        public double EffectiveDelayMultiplier => 1.0 + (DelayScale / 10.0);
        public double EffectiveHorizontalMultiplier => HorizontalPercent / 100.0;
        public double EffectiveVerticalMultiplier => VerticalPercent / 100.0;

        // Humanisation Settings
        public HumanisationSettings Humanisation { get; set; } = new HumanisationSettings();

        // Randomisation Settings
        public RandomisationSettings Randomisation { get; set; } = new RandomisationSettings();

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// Humanisation settings for natural movement
    /// </summary>
    public class HumanisationSettings : INotifyPropertyChanged
    {
        private bool _enabled = true;
        private double _timingVariation = 5.0;
        private double _movementVariation = 2.0;
        private double _smoothingFactor = 0.8;
        private bool _microAdjustments = true;

        public bool Enabled
        {
            get => _enabled;
            set => SetProperty(ref _enabled, value);
        }

        public double TimingVariation
        {
            get => _timingVariation;
            set => SetProperty(ref _timingVariation, Math.Max(0, Math.Min(20, value)));
        }

        public double MovementVariation
        {
            get => _movementVariation;
            set => SetProperty(ref _movementVariation, Math.Max(0, Math.Min(10, value)));
        }

        public double SmoothingFactor
        {
            get => _smoothingFactor;
            set => SetProperty(ref _smoothingFactor, Math.Max(0.1, Math.Min(1.0, value)));
        }

        public bool MicroAdjustments
        {
            get => _microAdjustments;
            set => SetProperty(ref _microAdjustments, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// Randomisation settings for unpredictable patterns
    /// </summary>
    public class RandomisationSettings : INotifyPropertyChanged
    {
        private bool _enabled = true;
        private double _horizontalDeviation = 3.0;
        private double _verticalDeviation = 2.0;
        private double _timingDeviation = 5.0;
        private bool _adaptiveRandomisation = true;

        public bool Enabled
        {
            get => _enabled;
            set => SetProperty(ref _enabled, value);
        }

        public double HorizontalDeviation
        {
            get => _horizontalDeviation;
            set => SetProperty(ref _horizontalDeviation, Math.Max(0, Math.Min(20, value)));
        }

        public double VerticalDeviation
        {
            get => _verticalDeviation;
            set => SetProperty(ref _verticalDeviation, Math.Max(0, Math.Min(20, value)));
        }

        public double TimingDeviation
        {
            get => _timingDeviation;
            set => SetProperty(ref _timingDeviation, Math.Max(0, Math.Min(50, value)));
        }

        public bool AdaptiveRandomisation
        {
            get => _adaptiveRandomisation;
            set => SetProperty(ref _adaptiveRandomisation, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// Hotkey configuration
    /// </summary>
    public class HotkeySettings
    {
        public string ToggleScript { get; set; } = "F1";
        public string ToggleRecoil { get; set; } = "F2";
        public string ToggleRapidfire { get; set; } = "F3";
        public string ToggleAntiAfk { get; set; } = "F4";
        public string EnterCodelock { get; set; } = "F5";
        public string EmergencyStop { get; set; } = "F12";
    }
}
