using System;
using System.Collections.Generic;

namespace RecoilController.Models
{
    /// <summary>
    /// Security configuration for different build modes
    /// </summary>
    public class SecurityConfig
    {
        public bool IsDebugMode { get; set; }
        public bool EnableSecurityAlerts { get; set; }
        public bool EnableDiscordWebhooks { get; set; }
        public bool EnableAntiDebugger { get; set; }
        public bool EnableProcessMonitoring { get; set; }
        public bool EnableHardwareFingerprinting { get; set; }
        public int AlertCooldownSeconds { get; set; }
        public List<string> MonitoredProcesses { get; set; }
        public List<string> BlockedProcesses { get; set; }

        public SecurityConfig()
        {
            // Default production settings
            IsDebugMode = false;
            EnableSecurityAlerts = true;
            EnableDiscordWebhooks = true;
            EnableAntiDebugger = true;
            EnableProcessMonitoring = true;
            EnableHardwareFingerprinting = true;
            AlertCooldownSeconds = 300; // 5 minutes
            
            MonitoredProcesses = new List<string>
            {
                "x64dbg", "x32dbg", "ollydbg", "windbg", "ida", "ida64",
                "cheatengine", "processhacker", "procmon", "wireshark",
                "fiddler", "burpsuite", "dnspy", "reflexil", "ilspy"
            };
            
            BlockedProcesses = new List<string>
            {
                "x64dbg", "x32dbg", "ollydbg", "cheatengine", "dnspy"
            };
        }

        /// <summary>
        /// Get debug configuration (security disabled)
        /// </summary>
        public static SecurityConfig GetDebugConfig()
        {
            return new SecurityConfig
            {
                IsDebugMode = true,
                EnableSecurityAlerts = false,
                EnableDiscordWebhooks = false,
                EnableAntiDebugger = false,
                EnableProcessMonitoring = false,
                EnableHardwareFingerprinting = false,
                AlertCooldownSeconds = 0,
                MonitoredProcesses = new List<string>(),
                BlockedProcesses = new List<string>()
            };
        }

        /// <summary>
        /// Get production configuration (full security)
        /// </summary>
        public static SecurityConfig GetProductionConfig()
        {
            return new SecurityConfig(); // Uses default constructor values
        }
    }

    /// <summary>
    /// Security alert data for logging and Discord notifications
    /// </summary>
    public class SecurityAlert
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string AlertType { get; set; } = "";
        public string Description { get; set; } = "";
        public string ProcessName { get; set; } = "";
        public string HardwareId { get; set; } = "";
        public string IpAddress { get; set; } = "";
        public string ComputerName { get; set; } = "";
        public string Username { get; set; } = "";
        public string WindowsVersion { get; set; } = "";
        public string CpuInfo { get; set; } = "";
        public string MotherboardSerial { get; set; } = "";
        public string MacAddress { get; set; } = "";
        public string DiskSerial { get; set; } = "";
        public string BiosSerial { get; set; } = "";
        public bool IsBlocked { get; set; } = false;
        public bool IsPermanentBan { get; set; } = false;
        public string LicenseKey { get; set; } = "";
        public Dictionary<string, string> AdditionalData { get; set; } = new();

        public SecurityAlert()
        {
        }

        public SecurityAlert(string alertType, string description)
        {
            AlertType = alertType;
            Description = description;
        }
    }

    /// <summary>
    /// Hardware fingerprint for unique identification
    /// </summary>
    public class HardwareFingerprint
    {
        public string ComputerName { get; set; } = "";
        public string ProcessorId { get; set; } = "";
        public string MotherboardSerial { get; set; } = "";
        public string DiskSerial { get; set; } = "";
        public string BiosSerial { get; set; } = "";
        public string MacAddress { get; set; } = "";
        public string WindowsProductId { get; set; } = "";
        public string MachineGuid { get; set; } = "";
        public DateTime FirstSeen { get; set; } = DateTime.UtcNow;
        public DateTime LastSeen { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Generate a unique fingerprint hash
        /// </summary>
        public string GenerateFingerprint()
        {
            var combined = $"{ComputerName}|{ProcessorId}|{MotherboardSerial}|{DiskSerial}|{BiosSerial}|{MacAddress}|{WindowsProductId}|{MachineGuid}";
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(combined)).Replace("=", "").Replace("+", "").Replace("/", "");
        }
    }

    /// <summary>
    /// Security action types
    /// </summary>
    public enum SecurityActionType
    {
        Warning,
        Block,
        Terminate,
        PermanentBan
    }

    /// <summary>
    /// Security threat levels
    /// </summary>
    public enum ThreatLevel
    {
        Low,
        Medium,
        High,
        Critical
    }
}
