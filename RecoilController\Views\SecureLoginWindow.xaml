<Window x:Class="RecoilController.Views.SecureLoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Octane Authentication" 
        Height="500" 
        Width="400"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Topmost="True">
    
    <Border Background="#1a1a1a" CornerRadius="15" BorderBrush="#8E54E9" BorderThickness="2">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="10" Opacity="0.5" BlurRadius="15"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Grid Grid.Row="0" Background="#8E54E9" MouseLeftButtonDown="Header_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="40"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <Ellipse Width="12" Height="12" Fill="#4776E6" Margin="0,0,8,0"/>
                    <TextBlock Text="OCTANE AUTHENTICATION" FontWeight="Bold" FontSize="14" Foreground="White"/>
                </StackPanel>
                
                <Button Grid.Column="1" Content="×" FontSize="18" FontWeight="Bold" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="CloseButton_Click" Cursor="Hand"
                        Style="{DynamicResource {x:Static ToolBar.ButtonStyleKey}}"/>
            </Grid>
            
            <!-- Content -->
            <StackPanel Grid.Row="1" Margin="40,30" VerticalAlignment="Center">
                <!-- Logo -->
                <Ellipse Width="80" Height="80" Margin="0,0,0,30" HorizontalAlignment="Center">
                    <Ellipse.Fill>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#8E54E9" Offset="0"/>
                            <GradientStop Color="#4776E6" Offset="1"/>
                        </LinearGradientBrush>
                    </Ellipse.Fill>
                </Ellipse>
                
                <!-- Title -->
                <TextBlock Text="Secure Access Required" FontSize="20" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                
                <TextBlock Text="Enter your license key to continue" FontSize="12" 
                          Foreground="#888" HorizontalAlignment="Center" Margin="0,0,0,30"/>
                
                <!-- License Key Input -->
                <TextBlock Text="License Key" FontSize="12" FontWeight="Medium" 
                          Foreground="White" Margin="0,0,0,8"/>
                
                <Border Background="#2a2a2a" CornerRadius="8" BorderBrush="#444" BorderThickness="1">
                    <TextBox x:Name="LicenseKeyTextBox" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderThickness="0" 
                            Padding="15,12"
                            FontSize="14"
                            FontFamily="Consolas"
                            CharacterCasing="Upper"
                            MaxLength="32"
                            TextChanged="LicenseKeyTextBox_TextChanged"/>
                </Border>
                
                <!-- Hardware ID Display -->
                <TextBlock Text="Hardware ID" FontSize="12" FontWeight="Medium" 
                          Foreground="White" Margin="0,20,0,8"/>
                
                <Border Background="#2a2a2a" CornerRadius="8" BorderBrush="#444" BorderThickness="1">
                    <TextBox x:Name="HardwareIdTextBox" 
                            Background="Transparent" 
                            Foreground="#888" 
                            BorderThickness="0" 
                            Padding="15,12"
                            FontSize="12"
                            FontFamily="Consolas"
                            IsReadOnly="True"/>
                </Border>
                
                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox" 
                         Content="Remember this device" 
                         Foreground="White" 
                         Margin="0,20,0,0"
                         FontSize="12"/>
                
                <!-- Status Message -->
                <TextBlock x:Name="StatusTextBlock" 
                          FontSize="12" 
                          Margin="0,15,0,0" 
                          HorizontalAlignment="Center"
                          Visibility="Collapsed"/>
            </StackPanel>
            
            <!-- Footer -->
            <Grid Grid.Row="2" Margin="40,0,40,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <ProgressBar x:Name="AuthProgressBar" 
                            Grid.Column="0" 
                            Height="8" 
                            Background="#2a2a2a" 
                            Foreground="#8E54E9"
                            Visibility="Collapsed"
                            VerticalAlignment="Center"/>
                
                <Button x:Name="AuthenticateButton" 
                       Grid.Column="1" 
                       Content="AUTHENTICATE" 
                       Height="40" 
                       FontWeight="Bold" 
                       FontSize="12"
                       Cursor="Hand"
                       Click="AuthenticateButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                        <GradientStop Color="#8E54E9" Offset="0"/>
                                        <GradientStop Color="#4776E6" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Foreground" Value="White"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                               CornerRadius="8" 
                                               BorderBrush="{TemplateBinding BorderBrush}" 
                                               BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Opacity" Value="0.9"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Opacity" Value="0.8"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Grid>
    </Border>
</Window>
