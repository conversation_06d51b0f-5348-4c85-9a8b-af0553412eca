/* Octane Admin Panel - Main Theme CSS */
:root {
    --primary-color: #FF6600;
    --primary-dark: #e55a00;
    --secondary-color: #FF8C00;
    --bg-dark: #0a0a0a;
    --bg-card: #1a1a1a;
    --bg-secondary: #252525;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: #333333;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.4);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Se<PERSON>e <PERSON>I', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-dark) 0%, #1a1a1a 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Container Styles */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    border-bottom: 2px solid var(--border-color);
}

.header h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    text-shadow: 0 0 20px rgba(255, 102, 0, 0.3);
}

.header .subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 20px;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

/* Card Styles */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h2,
.card-header h3 {
    color: var(--primary-color);
    font-weight: 600;
}

.card-content {
    color: var(--text-secondary);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

select.form-control {
    cursor: pointer;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
}

.table th,
.table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--primary-color);
    position: sticky;
    top: 0;
}

.table tr:hover {
    background: rgba(255, 102, 0, 0.05);
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-badge.inactive {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

/* Alert Styles */
.alert {
    padding: 15px 20px;
    margin: 15px 0;
    border-radius: var(--border-radius);
    font-weight: 600;
    animation: slideIn 0.3s ease;
}

.alert-success {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.alert-info {
    background: rgba(255, 102, 0, 0.2);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

/* Loading States */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 40px;
    color: var(--text-muted);
}

.loading i {
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .nav-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .card {
        padding: 20px;
    }
    
    .table-container {
        font-size: 0.9rem;
    }
}

/* Security severity badges */
.severity-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.severity-badge.critical {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.severity-badge.warning {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.severity-badge.info {
    background: rgba(23, 162, 184, 0.2);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

/* HWID display */
.hwid-display {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.8rem;
    color: var(--text-muted);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hwid-display.bound {
    color: var(--warning-color);
}

/* Enhanced login form styles */
.card {
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.8;
}

.card-header {
    position: relative;
}

.card-header h2 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.form-control {
    transition: all 0.3s ease;
    border: 2px solid var(--border-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
    transform: translateY(-1px);
}

.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* Checkbox styling */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}
