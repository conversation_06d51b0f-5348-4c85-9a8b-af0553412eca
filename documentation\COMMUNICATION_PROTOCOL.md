# ESP32-S2 Communication Protocol

## Overview
This document defines the communication protocol between the Octane desktop application and the ESP32-S2 HID mouse firmware.

## Serial Configuration
- **Baud Rate**: 115200
- **Data Bits**: 8
- **Parity**: None
- **Stop Bits**: 1
- **Flow Control**: None

## Message Format
All messages are JSON-formatted strings terminated with a newline character (`\n`).

### Desktop App → ESP32 Commands

#### 1. Initialization
```json
{"Type": "init", "Data": {"version": "4.2.1", "app": "OctaneRecoilScripts"}}
```
**Response**: `{"status": "ready", "firmware": "2.0.0", "hardware": "ESP32-S2"}`

#### 2. Mouse Movement
```json
{"Type": "mouse_move", "Data": {"x": -5, "y": 12}}
```
- **x**: Horizontal movement (-127 to 127)
- **y**: Vertical movement (-127 to 127)
**Response**: `{"status": "moved"}`

#### 3. Mouse Click
```json
{"Type": "mouse_click", "Data": {"button": "left", "action": "down"}}
```
- **button**: "left", "right", or "middle"
- **action**: "down" or "up"
**Response**: `{"status": "clicked"}`

#### 4. Ping/Health Check
```json
{"Type": "ping", "Data": {}}
```
**Response**: `{"status": "pong", "uptime": 12345}`

#### 5. Status Request
```json
{"Type": "status", "Data": {}}
```
**Response**: 
```json
{
  "status": "connected",
  "uptime": 12345,
  "queue_size": 5,
  "free_memory": 245760,
  "usb_ready": true
}
```

#### 6. Recoil Pattern (Advanced)
```json
{"Type": "recoil", "Data": {"pattern": [{"x": -2, "y": 5, "delay": 133}, {"x": 1, "y": 3, "delay": 133}]}}
```
**Response**: `{"status": "pattern_loaded", "points": 2}`

### ESP32 → Desktop App Responses

#### Standard Response Format
```json
{"status": "success|error", "message": "optional_message", "data": {}}
```

#### Error Response
```json
{"status": "error", "message": "Invalid command format", "code": 400}
```

#### Status Update (Unsolicited)
```json
{"type": "status_update", "connected": true, "led_state": "steady"}
```

## Alternative Simple Protocol (Fallback)

For compatibility with existing implementations, also support simple text commands:

### Text Commands
- `PING` → `PONG`
- `M<x>,<y>` → `MOVED` (e.g., `M-5,12`)
- `CLICK_LEFT_DOWN` → `CLICKED`
- `CLICK_LEFT_UP` → `RELEASED`
- `CLICK_RIGHT_DOWN` → `CLICKED`
- `CLICK_RIGHT_UP` → `RELEASED`
- `STATUS` → `STATUS:CONNECTED,LED:ON,UPTIME:12345`
- `VERSION` → `VERSION:2.0.0`

## Device Auto-Detection

The desktop app scans COM ports and sends `ping` commands to detect ESP32 devices.

### Detection Sequence
1. Desktop app opens each available COM port
2. Sends `ping` command
3. Waits 100ms for response
4. Checks if response contains "ESP32" or "octane"
5. If found, establishes connection

### ESP32 Response for Detection
```
PONG ESP32-S2 Octane HID Mouse v2.0.0
```

## Command Queue Management

### Queue Behavior
- Commands are queued on the ESP32 for smooth execution
- Queue size: 64 commands maximum
- Processing rate: 1 command per millisecond
- Overflow handling: Drop oldest commands

### Queue Status
Desktop app can check queue status:
```json
{"Type": "queue_status", "Data": {}}
```
Response:
```json
{"status": "ok", "queue_size": 12, "queue_max": 64}
```

## Error Handling

### Error Codes
- `400`: Invalid command format
- `401`: Authentication required
- `404`: Command not found
- `500`: Internal firmware error
- `503`: USB HID not ready
- `507`: Queue overflow

### Error Recovery
1. Desktop app detects error response
2. Stops sending commands temporarily
3. Sends status request to check device state
4. Resumes operation when device is ready

## Timing Considerations

### Command Timing
- **Mouse Movement**: Process immediately, queue if HID busy
- **Button Clicks**: Process immediately
- **Status Requests**: Respond within 10ms
- **Ping**: Respond within 5ms

### Recoil Compensation Timing
- Commands sent at weapon RPM rate
- ESP32 queues and smooths execution
- Maintains 1ms intervals between HID reports

## Connection Management

### Connection States
1. **Disconnected**: No serial connection
2. **Connected**: Serial open, not initialized
3. **Ready**: Initialized and responding to commands
4. **Active**: Actively processing recoil commands

### LED Status Indication
- **Flashing**: Waiting for connection
- **Steady**: Connected and ready
- **Rapid Flash**: Error state

### Connection Timeout
- Desktop app expects response within 1 second
- After 3 failed pings, marks device as disconnected
- Automatic reconnection attempts every 5 seconds

## Security Considerations

### Authentication (Future)
```json
{"Type": "auth", "Data": {"license": "xxx.xxx.xxx", "hwid": "ESP32_ABCDEF123456"}}
```

### Command Validation
- Validate JSON format
- Check command types
- Validate parameter ranges
- Rate limiting for security

## Performance Optimization

### Batch Commands
For high-frequency recoil:
```json
{"Type": "batch_move", "Data": {"moves": [{"x": -2, "y": 5}, {"x": 1, "y": 3}]}}
```

### Compression
For large recoil patterns, consider compression:
```json
{"Type": "compressed_pattern", "Data": {"compressed": "base64_encoded_data"}}
```

## Debugging & Diagnostics

### Debug Mode
Enable verbose logging:
```json
{"Type": "debug", "Data": {"level": "verbose"}}
```

### Performance Metrics
```json
{"Type": "metrics", "Data": {}}
```
Response:
```json
{
  "status": "ok",
  "metrics": {
    "commands_processed": 12345,
    "avg_response_time": 2.5,
    "queue_overflows": 0,
    "usb_errors": 0
  }
}
```

## Implementation Notes

### JSON Parsing
- Use ArduinoJson library for ESP32
- Allocate sufficient buffer (1KB recommended)
- Handle malformed JSON gracefully

### Serial Buffer Management
- Use circular buffer for incoming data
- Process complete lines only
- Handle partial messages

### USB HID Integration
- Queue HID reports when USB busy
- Maintain timing precision
- Handle USB disconnection gracefully
