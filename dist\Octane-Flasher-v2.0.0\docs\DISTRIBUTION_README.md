# Octane ESP32 Flasher Distribution Structure

This document outlines the proper distribution structure for the Octane ESP32 HID Mouse Flasher and Firmware.

## 📁 Distribution Folder Structure

```
Octane-Flasher-v2.0/
├── OctaneFlasher.exe              # Main flasher application
├── octane_auth_firmware.bin       # ESP32-S2 HID mouse firmware
├── bootloader.bin                 # ESP32-S2 bootloader
├── partitions.bin                 # Partition table
├── esptool.exe                    # ESP32 flashing tool (optional)
├── README.txt                     # User instructions
├── LICENSE.txt                    # License information
├── CHANGELOG.txt                  # Version history
└── docs/                          # Documentation folder
    ├── USER_GUIDE.md              # Detailed user guide
    ├── TROUBLESHOOTING.md         # Common issues and solutions
    └── TECHNICAL_SPECS.md         # Technical specifications
```

## 🎯 Distribution Types

### 1. End User Distribution (Minimal)
**Target**: End users who just want to flash their ESP32
**Size**: ~2-3 MB

```
Octane-Flasher-Minimal-v2.0/
├── OctaneFlasher.exe
├── octane_auth_firmware.bin
├── bootloader.bin
├── partitions.bin
└── README.txt
```

### 2. Complete Distribution (Full)
**Target**: Power users and developers
**Size**: ~5-10 MB

```
Octane-Flasher-Complete-v2.0/
├── OctaneFlasher.exe
├── octane_auth_firmware.bin
├── bootloader.bin
├── partitions.bin
├── esptool.exe
├── README.txt
├── LICENSE.txt
├── CHANGELOG.txt
└── docs/
    ├── USER_GUIDE.md
    ├── TROUBLESHOOTING.md
    └── TECHNICAL_SPECS.md
```

### 3. Developer Distribution (Source)
**Target**: Developers who want to modify firmware
**Size**: ~50-100 MB

```
Octane-Flasher-Source-v2.0/
├── OctaneFlasher.exe
├── octane_auth_firmware.bin
├── bootloader.bin
├── partitions.bin
├── README.txt
├── LICENSE.txt
├── CHANGELOG.txt
├── docs/
├── source/
│   ├── esp32-firmware/           # Firmware source code
│   │   ├── main/
│   │   ├── CMakeLists.txt
│   │   ├── platformio.ini
│   │   ├── build_firmware.bat
│   │   └── BUILD_SYSTEM_README.md
│   └── OctaneFlasher/            # C# flasher source
│       ├── Program.cs
│       ├── OctaneFlasher.csproj
│       └── Properties/
└── tools/
    ├── esptool.exe
    └── build_scripts/
```

## 📦 Packaging Guidelines

### File Naming Convention
- **Version Format**: `v{MAJOR}.{MINOR}.{PATCH}` (e.g., v2.0.0)
- **Archive Names**: 
  - `Octane-Flasher-Minimal-v2.0.0.zip`
  - `Octane-Flasher-Complete-v2.0.0.zip`
  - `Octane-Flasher-Source-v2.0.0.zip`

### File Size Limits
- **Firmware files**: Must be validated for minimum sizes
- **Total package**: Should not exceed 100MB for source distribution
- **Minimal package**: Should be under 5MB

### Required Files Validation
Before distribution, ensure these files exist and meet size requirements:

| File | Min Size | Max Size | Required |
|------|----------|----------|----------|
| `OctaneFlasher.exe` | 500KB | 10MB | ✅ Yes |
| `octane_auth_firmware.bin` | 50KB | 1MB | ✅ Yes |
| `bootloader.bin` | 20KB | 100KB | ✅ Yes |
| `partitions.bin` | 1KB | 10KB | ✅ Yes |
| `esptool.exe` | 5MB | 20MB | ❌ Optional |
| `README.txt` | 1KB | 50KB | ✅ Yes |

## 🔧 Build Process for Distribution

### 1. Prepare Firmware
```bash
cd esp32-firmware
./build.bat                    # Build firmware
# Files automatically copied to OctaneFlasher/
```

### 2. Build C# Flasher
```bash
cd OctaneFlasher
dotnet publish -c Release -r win-x64 --self-contained
# Creates single-file executable
```

### 3. Validate Files
```bash
# Check file sizes and integrity
python validate_distribution.py
```

### 4. Create Distribution Packages
```bash
# Create all distribution types
python create_distribution.py --version 2.0.0
```

## 📋 Quality Checklist

Before releasing any distribution:

### ✅ Firmware Validation
- [ ] Firmware compiles without errors
- [ ] All required files present and correct size
- [ ] Firmware version matches distribution version
- [ ] LED functionality works (flashing/steady states)
- [ ] HID mouse functionality verified
- [ ] Serial communication tested

### ✅ Flasher Validation
- [ ] Flasher compiles without errors
- [ ] License validation works
- [ ] COM port detection works
- [ ] Flashing process completes successfully
- [ ] Error reporting functions properly
- [ ] User interface is intuitive

### ✅ Documentation
- [ ] README.txt is clear and complete
- [ ] User guide covers all scenarios
- [ ] Troubleshooting guide addresses common issues
- [ ] Technical specs are accurate
- [ ] Version information is correct

### ✅ Testing
- [ ] Tested on clean Windows 10/11 systems
- [ ] Tested with multiple ESP32-S2 boards
- [ ] Tested with different USB cables/ports
- [ ] Tested license validation
- [ ] Tested error scenarios

## 🚀 Release Process

### 1. Version Tagging
```bash
git tag -a v2.0.0 -m "Release version 2.0.0"
git push origin v2.0.0
```

### 2. Build All Distributions
```bash
./build_all_distributions.bat v2.0.0
```

### 3. Upload to Distribution Channels
- Internal file server
- Customer download portal
- GitHub releases (if applicable)

### 4. Update Documentation
- Update version numbers
- Update download links
- Notify users of new release

## 🔒 Security Considerations

### File Integrity
- All distributed files should have SHA256 checksums
- Firmware files should be signed (if signing infrastructure exists)
- Flasher executable should be code-signed

### Distribution Channels
- Use HTTPS for all downloads
- Verify download integrity
- Provide checksums for verification

## 📊 Distribution Metrics

Track these metrics for each release:
- Download counts by distribution type
- User feedback and issues
- Success/failure rates
- Geographic distribution
- Platform compatibility

## 🔄 Update Process

For firmware updates:
1. Increment version number
2. Build new firmware
3. Test thoroughly
4. Create new distribution packages
5. Update download links
6. Notify users

For flasher updates:
1. Update C# application
2. Test with existing firmware
3. Create new distribution packages
4. Maintain backward compatibility
5. Update documentation
