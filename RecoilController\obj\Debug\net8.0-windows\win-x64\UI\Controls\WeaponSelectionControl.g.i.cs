﻿#pragma checksum "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7EE9C7172CABE1EDE0F3095454E9220628259D1F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.UI.Controls {
    
    
    /// <summary>
    /// WeaponSelectionControl
    /// </summary>
    public partial class WeaponSelectionControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 78 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AssaultRiflesButton;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SMGsButton;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LMGsButton;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShotgunsButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SniperRiflesButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PistolsButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryTitle;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeaponCount;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WeaponListBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Card WeaponDetailsCard;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WeaponDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeaponNameText;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FireRateText;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MagazineText;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ADSText;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecoilPatternText;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ADSScaleText;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas RecoilPatternCanvas;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoSelectionPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;V1.0.0.0;component/ui/controls/weaponselectioncontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\UI\Controls\WeaponSelectionControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AssaultRiflesButton = ((System.Windows.Controls.Button)(target));
            return;
            case 2:
            this.SMGsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.LMGsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.ShotgunsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.SniperRiflesButton = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.PistolsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.CategoryTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.WeaponCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.WeaponListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 10:
            this.WeaponDetailsCard = ((MaterialDesignThemes.Wpf.Card)(target));
            return;
            case 11:
            this.WeaponDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.WeaponNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.FireRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.MagazineText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ADSText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.RecoilPatternText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ADSScaleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RecoilPatternCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 19:
            this.NoSelectionPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

