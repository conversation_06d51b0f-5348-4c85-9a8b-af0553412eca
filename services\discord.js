const { Client, GatewayIntentBits, Slash<PERSON>ommandBuilder, EmbedBuilder, REST, Routes } = require('discord.js');
const sqlite3 = require('sqlite3').verbose();
require('dotenv').config();

// Discord webhook URLs
const WEBHOOKS = {
    SECURITY: 'https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
    ESP32: 'https://discord.com/api/webhooks/1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
    BACKEND: 'https://discord.com/api/webhooks/1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
};

class OctaneDiscordBot {
    constructor() {
        // Minimal intents for memory optimization
        this.client = new Client({
            intents: [GatewayIntentBits.Guilds],
            presence: {
                status: 'online',
                activities: [{ name: 'Octane Auth System', type: 0 }]
            },
            // Memory optimization options
            allowedMentions: { parse: [] },
            partials: [],
            sweepers: {
                messages: {
                    interval: 300, // 5 minutes
                    lifetime: 1800 // 30 minutes
                },
                users: {
                    interval: 3600, // 1 hour
                    filter: () => user => user.bot && user.id !== user.client.user.id
                }
            }
        });

        this.db = null; // Lazy load database
        this.commands = new Map();
        this.isReady = false;
        this.lastMemoryCleanup = Date.now();

        this.setupCommands();
        this.setupEventHandlers();
        this.startMemoryMonitoring();
    }

    setupCommands() {
        // System Status Command
        this.commands.set('status', {
            data: new SlashCommandBuilder()
                .setName('status')
                .setDescription('Check system status'),
            execute: async (interaction) => {
                const embed = new EmbedBuilder()
                    .setTitle('🟢 Octane System Status')
                    .setColor(0x00ff88)
                    .addFields(
                        { name: 'Backend', value: '✅ Online', inline: true },
                        { name: 'Database', value: '✅ Connected', inline: true },
                        { name: 'Discord Bot', value: '✅ Active', inline: true },
                        { name: 'Uptime', value: this.getUptime(), inline: true }
                    )
                    .setTimestamp();
                
                await interaction.reply({ embeds: [embed] });
            }
        });

        // Create License Command
        this.commands.set('create-license', {
            data: new SlashCommandBuilder()
                .setName('create-license')
                .setDescription('Create a new license key')
                .addStringOption(option =>
                    option.setName('duration')
                        .setDescription('License duration')
                        .setRequired(true)
                        .addChoices(
                            { name: '1 Day', value: '1day' },
                            { name: '1 Week', value: '1week' },
                            { name: '1 Month', value: '1month' },
                            { name: '3 Months', value: '3months' }
                        ))
                .addStringOption(option =>
                    option.setName('username')
                        .setDescription('Username for the license')
                        .setRequired(false)),
            execute: async (interaction) => {
                const duration = interaction.options.getString('duration');
                const username = interaction.options.getString('username') || 'Discord User';
                
                try {
                    const license = await this.createLicense(duration, username, interaction.user.username);
                    
                    const embed = new EmbedBuilder()
                        .setTitle('🔑 License Created Successfully')
                        .setColor(0x00d4ff)
                        .addFields(
                            { name: 'License Key', value: `\`${license.key}\``, inline: false },
                            { name: 'Duration', value: duration, inline: true },
                            { name: 'Created For', value: username, inline: true },
                            { name: 'Created By', value: interaction.user.username, inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed], ephemeral: true });
                } catch (error) {
                    console.error('Error creating license:', error);
                    await interaction.reply({ content: '❌ Failed to create license', ephemeral: true });
                }
            }
        });

        // License Stats Command
        this.commands.set('stats', {
            data: new SlashCommandBuilder()
                .setName('stats')
                .setDescription('Show license statistics'),
            execute: async (interaction) => {
                try {
                    const stats = await this.getLicenseStats();
                    
                    const embed = new EmbedBuilder()
                        .setTitle('📊 License Statistics')
                        .setColor(0x00ff88)
                        .addFields(
                            { name: 'Total Licenses', value: stats.total.toString(), inline: true },
                            { name: 'Active Licenses', value: stats.active.toString(), inline: true },
                            { name: 'Expired Licenses', value: stats.expired.toString(), inline: true }
                        )
                        .setTimestamp();
                    
                    await interaction.reply({ embeds: [embed] });
                } catch (error) {
                    console.error('Error getting stats:', error);
                    await interaction.reply({ content: '❌ Failed to get statistics', ephemeral: true });
                }
            }
        });

        // Emergency Disable Command
        this.commands.set('emergency-disable', {
            data: new SlashCommandBuilder()
                .setName('emergency-disable')
                .setDescription('Emergency disable authentication (Admin only)'),
            execute: async (interaction) => {
                // Check if user has admin permissions
                if (!interaction.member.permissions.has('ADMINISTRATOR')) {
                    await interaction.reply({ content: '❌ Admin permissions required', ephemeral: true });
                    return;
                }
                
                const embed = new EmbedBuilder()
                    .setTitle('🚨 Emergency Disable Activated')
                    .setColor(0xff4757)
                    .setDescription('Authentication has been temporarily disabled')
                    .addFields(
                        { name: 'Activated By', value: interaction.user.username, inline: true },
                        { name: 'Time', value: new Date().toISOString(), inline: true }
                    )
                    .setTimestamp();
                
                await interaction.reply({ embeds: [embed] });
                
                // Send to security webhook
                await this.sendWebhook('SECURITY', {
                    embeds: [embed]
                });
            }
        });
    }

    setupEventHandlers() {
        this.client.once('ready', () => {
            console.log(`✅ Discord bot logged in as ${this.client.user.tag}`);
            this.isReady = true;
            this.registerCommands();
        });

        this.client.on('interactionCreate', async (interaction) => {
            if (!interaction.isChatInputCommand()) return;

            const command = this.commands.get(interaction.commandName);
            if (!command) return;

            try {
                await command.execute(interaction);
            } catch (error) {
                console.error('Error executing command:', error);
                const reply = { content: '❌ There was an error executing this command!', ephemeral: true };
                
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(reply);
                } else {
                    await interaction.reply(reply);
                }
            }
        });

        this.client.on('error', (error) => {
            console.error('Discord client error:', error);
        });
    }

    async registerCommands() {
        try {
            const token = process.env.DISCORD_BOT_TOKEN || process.env.DISCORD_TOKEN;
            const rest = new REST({ version: '10' }).setToken(token);
            const commands = Array.from(this.commands.values()).map(cmd => cmd.data.toJSON());

            console.log('🔄 Registering Discord slash commands...');
            
            await rest.put(
                Routes.applicationCommands(this.client.user.id),
                { body: commands }
            );

            console.log(`✅ Successfully registered ${commands.length} slash commands`);
        } catch (error) {
            console.error('Failed to register commands:', error);
        }
    }

    async createLicense(duration, username, createdBy) {
        return new Promise((resolve, reject) => {
            const key = this.generateLicenseKey();
            const expiresAt = this.calculateExpiry(duration);

            this.getDatabase().run(
                `INSERT INTO licenses (license_key, username, expires_at, status, created_by) VALUES (?, ?, ?, 'active', ?)`,
                [key, username, expiresAt, createdBy],
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        resolve({ key, expiresAt });
                    }
                }
            );
        });
    }

    async getLicenseStats() {
        return new Promise((resolve, reject) => {
            this.getDatabase().all(
                `SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'active' AND expires_at > datetime('now') THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN status = 'expired' OR expires_at <= datetime('now') THEN 1 ELSE 0 END) as expired
                FROM licenses`,
                [],
                (err, rows) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(rows[0]);
                    }
                }
            );
        });
    }

    generateLicenseKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 3; j++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            if (i < 2) result += '.';
        }
        return result;
    }

    calculateExpiry(duration) {
        const now = new Date();
        switch (duration) {
            case '1day': return new Date(now.getTime() + 24 * 60 * 60 * 1000);
            case '1week': return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            case '1month': return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            case '3months': return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
            default: return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        }
    }

    getUptime() {
        const uptime = process.uptime();
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }

    async sendWebhook(type, data) {
        try {
            const webhook = WEBHOOKS[type];
            if (!webhook) return;

            const response = await fetch(webhook, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                console.error(`Webhook ${type} failed:`, response.statusText);
            }
        } catch (error) {
            console.error(`Error sending ${type} webhook:`, error);
        }
    }

    startMemoryMonitoring() {
        // Memory cleanup every 10 minutes
        setInterval(() => {
            this.performMemoryCleanup();
        }, 600000);
    }

    performMemoryCleanup() {
        try {
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
            }

            // Clear Discord.js caches
            if (this.client.users) {
                this.client.users.cache.clear();
            }

            // Close and reopen database connection to prevent memory leaks
            if (this.db) {
                this.db.close();
                this.db = null;
            }

            const memUsage = process.memoryUsage();
            const memMB = Math.round(memUsage.heapUsed / 1024 / 1024);

            if (memMB > 40) { // If using more than 40MB
                console.log(`🧹 Discord bot memory cleanup. Usage: ${memMB}MB`);
            }

            this.lastMemoryCleanup = Date.now();
        } catch (error) {
            console.error('Memory cleanup error:', error);
        }
    }

    getDatabase() {
        if (!this.db) {
            this.db = new sqlite3.Database(process.env.DATABASE_PATH || './octane.db');
        }
        return this.db;
    }

    async start() {
        try {
            const token = process.env.DISCORD_BOT_TOKEN || process.env.DISCORD_TOKEN;
            if (!token) {
                console.error('❌ No Discord bot token found in environment variables');
                process.exit(1);
            }
            await this.client.login(token);
            console.log('🚀 Discord bot started (memory optimized)');
        } catch (error) {
            console.error('Failed to start Discord bot:', error);
            process.exit(1);
        }
    }

    async stop() {
        if (this.client) {
            await this.client.destroy();
        }
        if (this.db) {
            this.db.close();
            this.db = null;
        }
        console.log('🛑 Discord bot stopped');
    }
}

// Start the bot if this file is run directly
if (require.main === module) {
    const bot = new OctaneDiscordBot();
    bot.start();

    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('🛑 Shutting down Discord bot...');
        await bot.stop();
        process.exit(0);
    });
}

module.exports = OctaneDiscordBot;
