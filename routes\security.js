const express = require('express');
const router = express.Router();

// Security logging function
function logSecurityEvent(db, ipAddress, eventType, description, severity = 'info') {
    if (!db) return;
    
    db.run(
        `INSERT INTO security_events (type, severity, description, ip_address, timestamp) 
         VALUES (?, ?, ?, ?, datetime('now'))`,
        [eventType, severity, description, ipAddress],
        (err) => {
            if (err) {
                console.error('Failed to log security event:', err);
            }
        }
    );
}

// Security routes
router.get('/events', (req, res) => {
    const db = req.app.locals.db;
    const { limit = 50, severity, type } = req.query;
    
    let query = 'SELECT * FROM security_events WHERE 1=1';
    const params = [];
    
    if (severity) {
        query += ' AND severity = ?';
        params.push(severity);
    }
    
    if (type) {
        query += ' AND type = ?';
        params.push(type);
    }
    
    query += ' ORDER BY timestamp DESC LIMIT ?';
    params.push(parseInt(limit));
    
    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Get security events error:', err);
            return res.status(500).json({
                success: false,
                error: 'Database error'
            });
        }
        
        res.json({
            success: true,
            events: rows
        });
    });
});

router.get('/stats', (req, res) => {
    const db = req.app.locals.db;
    
    db.all(`
        SELECT 
            severity,
            COUNT(*) as count
        FROM security_events 
        WHERE timestamp >= datetime('now', '-24 hours')
        GROUP BY severity
    `, (err, rows) => {
        if (err) {
            console.error('Security stats error:', err);
            return res.status(500).json({
                success: false,
                error: 'Database error'
            });
        }
        
        const stats = {
            total: 0,
            critical: 0,
            warning: 0,
            info: 0
        };
        
        rows.forEach(row => {
            stats.total += row.count;
            stats[row.severity] = row.count;
        });
        
        res.json({
            success: true,
            stats: stats
        });
    });
});

router.get('/blocked-ips', (req, res) => {
    const db = req.app.locals.db;
    
    db.all(`
        SELECT 
            ip_address,
            COUNT(*) as violation_count,
            MAX(timestamp) as last_violation
        FROM security_events 
        WHERE severity = 'critical' 
        AND timestamp >= datetime('now', '-7 days')
        GROUP BY ip_address
        HAVING violation_count >= 3
        ORDER BY violation_count DESC
    `, (err, rows) => {
        if (err) {
            console.error('Blocked IPs error:', err);
            return res.status(500).json({
                success: false,
                error: 'Database error'
            });
        }
        
        res.json({
            success: true,
            blocked_ips: rows
        });
    });
});

router.post('/ban-ip', (req, res) => {
    const { ip, reason } = req.body;
    const db = req.app.locals.db;
    
    if (!ip) {
        return res.status(400).json({
            success: false,
            error: 'IP address is required'
        });
    }

    // Log the security event
    logSecurityEvent(db, ip, 'IP_BANNED', `IP ${ip} was manually banned. Reason: ${reason || 'No reason provided'}`, 'critical');

    res.json({
        success: true,
        message: 'IP banned successfully'
    });
});

router.post('/unban-ip', (req, res) => {
    const { ip } = req.body;
    const db = req.app.locals.db;
    
    if (!ip) {
        return res.status(400).json({
            success: false,
            error: 'IP address is required'
        });
    }

    // Log the security event
    logSecurityEvent(db, ip, 'IP_UNBANNED', `IP ${ip} was manually unbanned`, 'info');

    res.json({
        success: true,
        message: 'IP unbanned successfully'
    });
});

router.delete('/events', (req, res) => {
    const { eventIds, deleteAll } = req.body;
    const db = req.app.locals.db;
    
    if (deleteAll) {
        // Delete all security events
        db.run('DELETE FROM security_events', (err) => {
            if (err) {
                console.error('Delete all security events error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete security events'
                });
            }
            
            console.log('🗑️ All security events deleted');
            res.json({
                success: true,
                message: 'All security events deleted successfully'
            });
        });
    } else if (eventIds && Array.isArray(eventIds)) {
        // Delete specific events
        const placeholders = eventIds.map(() => '?').join(',');
        db.run(`DELETE FROM security_events WHERE id IN (${placeholders})`, eventIds, function(err) {
            if (err) {
                console.error('Delete security events error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete security events'
                });
            }
            
            console.log(`🗑️ Deleted ${this.changes} security events`);
            res.json({
                success: true,
                message: `Deleted ${this.changes} security events successfully`
            });
        });
    } else {
        res.status(400).json({
            success: false,
            error: 'Either eventIds array or deleteAll flag is required'
        });
    }
});

// Live security logs endpoint
router.get('/live-logs', (req, res) => {
    const db = req.app.locals.db;
    
    db.all(`
        SELECT * FROM security_events 
        WHERE timestamp >= datetime('now', '-1 hour')
        ORDER BY timestamp DESC 
        LIMIT 50
    `, (err, rows) => {
        if (err) {
            console.error('Live logs error:', err);
            return res.status(500).json({
                success: false,
                error: 'Database error'
            });
        }
        
        res.json({
            success: true,
            logs: rows
        });
    });
});

// Security scan endpoint
router.post('/scan', (req, res) => {
    const db = req.app.locals.db;
    
    // Mock security scan
    setTimeout(() => {
        const findings = [
            {
                type: 'info',
                message: 'System security scan completed',
                timestamp: new Date().toISOString()
            },
            {
                type: 'success',
                message: 'No critical vulnerabilities detected',
                timestamp: new Date().toISOString()
            },
            {
                type: 'info',
                message: 'All security policies are enforced',
                timestamp: new Date().toISOString()
            }
        ];
        
        // Log the security scan
        logSecurityEvent(db, req.ip, 'SECURITY_SCAN', 'Manual security scan initiated', 'info');
        
        res.json({
            success: true,
            message: 'Security scan completed',
            findings: findings
        });
    }, 3000);
});

// Reset security settings
router.post('/reset-settings', (req, res) => {
    const db = req.app.locals.db;
    
    // Log the reset action
    logSecurityEvent(db, req.ip, 'SECURITY_RESET', 'Security settings reset to defaults', 'warning');
    
    res.json({
        success: true,
        message: 'Security settings reset to defaults'
    });
});

// Update security rules
router.post('/update-rules', (req, res) => {
    const { rules } = req.body;
    const db = req.app.locals.db;
    
    if (!rules) {
        return res.status(400).json({
            success: false,
            error: 'Security rules are required'
        });
    }
    
    // Log the rule update
    logSecurityEvent(db, req.ip, 'SECURITY_RULES_UPDATE', 'Security rules updated', 'info');
    
    res.json({
        success: true,
        message: 'Security rules updated successfully'
    });
});

// Clear old logs
router.delete('/clear-old-logs', (req, res) => {
    const db = req.app.locals.db;
    const { days = 30 } = req.body;
    
    db.run(`
        DELETE FROM security_events 
        WHERE timestamp < datetime('now', '-${parseInt(days)} days')
    `, function(err) {
        if (err) {
            console.error('Clear old logs error:', err);
            return res.status(500).json({
                success: false,
                error: 'Failed to clear old logs'
            });
        }
        
        console.log(`🧹 Cleared ${this.changes} old security logs`);
        
        // Log the cleanup action
        logSecurityEvent(db, req.ip, 'LOG_CLEANUP', `Cleared ${this.changes} old security logs`, 'info');
        
        res.json({
            success: true,
            message: `Cleared ${this.changes} old security logs`,
            deleted: this.changes
        });
    });
});

module.exports = { router, logSecurityEvent };
