const mongoose = require('mongoose');
const SecurityEvent = require('../models/SecurityEvent');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/octane-auth', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

async function seedSecurityData() {
    try {
        // Clear existing data
        await SecurityEvent.deleteMany({});
        console.log('Cleared existing security events');

        // Sample security events
        const sampleEvents = [
            {
                type: 'failed_auth',
                severity: 'high',
                description: 'Multiple failed authentication attempts detected',
                username: 'user123',
                hardwareId: 'HW-ABC123DEF456',
                ipAddress: '*************',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
            },
            {
                type: 'suspicious_activity',
                severity: 'critical',
                description: 'Unusual pattern detected in user behavior',
                username: 'user456',
                hardwareId: 'HW-XYZ789GHI012',
                ipAddress: '*********',
                timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000) // 1 hour ago
            },
            {
                type: 'successful_auth',
                severity: 'low',
                description: 'User successfully authenticated',
                username: 'user789',
                hardwareId: 'HW-LMN345OPQ678',
                ipAddress: '***********',
                timestamp: new Date(Date.now() - 30 * 60 * 1000) // 30 minutes ago
            },
            {
                type: 'user_blocked',
                severity: 'high',
                description: 'User blocked due to suspicious activity',
                username: 'user123',
                hardwareId: 'HW-ABC123DEF456',
                ipAddress: '*************',
                timestamp: new Date(Date.now() - 15 * 60 * 1000) // 15 minutes ago
            },
            {
                type: 'multiple_attempts',
                severity: 'medium',
                description: 'Multiple login attempts from same IP',
                username: 'unknown',
                hardwareId: 'HW-RST901UVW234',
                ipAddress: '************',
                timestamp: new Date(Date.now() - 10 * 60 * 1000) // 10 minutes ago
            },
            {
                type: 'admin_action',
                severity: 'medium',
                description: 'Admin cleared security log',
                username: 'admin',
                ipAddress: '127.0.0.1',
                timestamp: new Date(Date.now() - 5 * 60 * 1000) // 5 minutes ago
            },
            {
                type: 'system_alert',
                severity: 'critical',
                description: 'Potential security breach detected',
                username: 'system',
                ipAddress: '0.0.0.0',
                timestamp: new Date(Date.now() - 2 * 60 * 1000) // 2 minutes ago
            },
            {
                type: 'failed_auth',
                severity: 'medium',
                description: 'Invalid license key provided',
                username: 'user999',
                hardwareId: 'HW-DEF567GHI890',
                ipAddress: '*************',
                timestamp: new Date(Date.now() - 1 * 60 * 1000) // 1 minute ago
            }
        ];

        // Insert sample events
        await SecurityEvent.insertMany(sampleEvents);
        console.log(`Inserted ${sampleEvents.length} sample security events`);

        // Display statistics
        const stats = {
            total: await SecurityEvent.countDocuments(),
            critical: await SecurityEvent.countDocuments({ severity: 'critical' }),
            high: await SecurityEvent.countDocuments({ severity: 'high' }),
            medium: await SecurityEvent.countDocuments({ severity: 'medium' }),
            low: await SecurityEvent.countDocuments({ severity: 'low' }),
            blocked: await SecurityEvent.countDocuments({ type: 'user_blocked' }),
            active: await SecurityEvent.countDocuments({ 
                timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
            })
        };

        console.log('Security Statistics:');
        console.log(`Total Events: ${stats.total}`);
        console.log(`Critical: ${stats.critical}`);
        console.log(`High: ${stats.high}`);
        console.log(`Medium: ${stats.medium}`);
        console.log(`Low: ${stats.low}`);
        console.log(`Blocked Users: ${stats.blocked}`);
        console.log(`Active (24h): ${stats.active}`);

        console.log('Security data seeded successfully!');
        process.exit(0);
    } catch (error) {
        console.error('Error seeding security data:', error);
        process.exit(1);
    }
}

seedSecurityData();
