using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Collections.Generic;

namespace RecoilController.Services
{
    /// <summary>
    /// Service for fetching Discord webhook URLs from the VPS backend
    /// </summary>
    public static class WebhookService
    {
        private static readonly HttpClient _httpClient = new HttpClient();
        private static readonly string _vpsUrl = "http://217.154.58.14/api/webhooks/public";
        private static Dictionary<string, string> _cachedWebhooks = new Dictionary<string, string>();
        private static DateTime _lastFetch = DateTime.MinValue;
        private static readonly TimeSpan _cacheTimeout = TimeSpan.FromMinutes(5); // Cache for 5 minutes

        /// <summary>
        /// Get Discord webhook URL for security alerts
        /// </summary>
        public static async Task<string> GetSecurityWebhookAsync()
        {
            await RefreshWebhooksIfNeeded();
            return _cachedWebhooks.GetValueOrDefault("security_alerts", "");
        }

        /// <summary>
        /// Get Discord webhook URL for ESP32 errors
        /// </summary>
        public static async Task<string> GetESP32WebhookAsync()
        {
            await RefreshWebhooksIfNeeded();
            return _cachedWebhooks.GetValueOrDefault("esp32_errors", "");
        }

        /// <summary>
        /// Get Discord webhook URL for backend errors
        /// </summary>
        public static async Task<string> GetBackendWebhookAsync()
        {
            await RefreshWebhooksIfNeeded();
            return _cachedWebhooks.GetValueOrDefault("backend_errors", "");
        }

        /// <summary>
        /// Refresh webhook URLs from VPS if cache is expired
        /// </summary>
        private static async Task RefreshWebhooksIfNeeded()
        {
            if (DateTime.Now - _lastFetch < _cacheTimeout && _cachedWebhooks.Count > 0)
            {
                return; // Cache is still valid
            }

            try
            {
                using var response = await _httpClient.GetAsync(_vpsUrl);
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var webhookResponse = JsonSerializer.Deserialize<WebhookResponse>(jsonContent);

                    if (webhookResponse?.success == true && webhookResponse.webhooks != null)
                    {
                        _cachedWebhooks = new Dictionary<string, string>
                        {
                            ["security_alerts"] = webhookResponse.webhooks.security_alerts ?? "",
                            ["esp32_errors"] = webhookResponse.webhooks.esp32_errors ?? "",
                            ["backend_errors"] = webhookResponse.webhooks.backend_errors ?? ""
                        };

                        _lastFetch = DateTime.Now;
                        Console.WriteLine($"✅ Webhook URLs refreshed from VPS at {_lastFetch:HH:mm:ss}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to fetch webhook URLs from VPS: {ex.Message}");
                
                // Use fallback URLs if VPS is unreachable
                if (_cachedWebhooks.Count == 0)
                {
                    _cachedWebhooks = GetFallbackWebhooks();
                    Console.WriteLine("🔄 Using fallback webhook URLs");
                }
            }
        }

        /// <summary>
        /// Get fallback webhook URLs if VPS is unreachable
        /// </summary>
        private static Dictionary<string, string> GetFallbackWebhooks()
        {
            return new Dictionary<string, string>
            {
                ["security_alerts"] = "1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy",
                ["esp32_errors"] = "1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW",
                ["backend_errors"] = "1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i"
            };
        }

        /// <summary>
        /// Force refresh webhook URLs from VPS
        /// </summary>
        public static async Task ForceRefreshAsync()
        {
            _lastFetch = DateTime.MinValue; // Force cache expiry
            await RefreshWebhooksIfNeeded();
        }

        /// <summary>
        /// Send Discord webhook message
        /// </summary>
        public static async Task<bool> SendWebhookAsync(string webhookType, object data)
        {
            try
            {
                string webhookUrl = webhookType.ToLower() switch
                {
                    "security" => await GetSecurityWebhookAsync(),
                    "esp32" => await GetESP32WebhookAsync(),
                    "backend" => await GetBackendWebhookAsync(),
                    _ => ""
                };

                if (string.IsNullOrEmpty(webhookUrl))
                {
                    Console.WriteLine($"❌ No webhook URL found for type: {webhookType}");
                    return false;
                }

                var fullUrl = $"https://discord.com/api/webhooks/{webhookUrl}";
                var jsonContent = JsonSerializer.Serialize(data);
                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");

                using var response = await _httpClient.PostAsync(fullUrl, content);
                
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"✅ Discord webhook sent successfully ({webhookType})");
                    return true;
                }
                else
                {
                    Console.WriteLine($"❌ Discord webhook failed ({webhookType}): {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error sending Discord webhook ({webhookType}): {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Response model for webhook API
    /// </summary>
    public class WebhookResponse
    {
        public bool success { get; set; }
        public WebhookUrls webhooks { get; set; }
        public string timestamp { get; set; }
    }

    /// <summary>
    /// Webhook URLs model
    /// </summary>
    public class WebhookUrls
    {
        public string security_alerts { get; set; }
        public string esp32_errors { get; set; }
        public string backend_errors { get; set; }
    }
}
