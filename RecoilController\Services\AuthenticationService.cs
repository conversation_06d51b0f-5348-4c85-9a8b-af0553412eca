using System;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;

namespace RecoilController.Services
{
    /// <summary>
    /// Service for handling license key validation and authentication
    /// </summary>
    public class AuthenticationService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiBaseUrl = "http://217.154.58.14/api"; // Production VPS endpoint

        public bool IsAuthenticated { get; private set; }
        public string CurrentLicenseKey { get; private set; }
        public string HardwareId { get; private set; }
        public DateTime? LicenseExpiry { get; private set; }
        public TimeSpan RemainingTime { get; private set; }
        public string MaskedLicenseKey => MaskLicenseKey(CurrentLicenseKey);

        public AuthenticationService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);

            // Add headers for better compatibility (matching flasher)
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "OctaneDesktop/4.2.1");

            HardwareId = GenerateHardwareId();
        }

        /// <summary>
        /// Validates a license key with the VPS backend (matching flasher implementation)
        /// </summary>
        public async Task<AuthenticationResult> ValidateLicenseKey(string licenseKey)
        {
            const int maxRetries = 3;
            const int retryDelayMs = 2000; // 2 seconds between retries

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var requestData = new
                    {
                        licenseKey = licenseKey,
                        hardwareId = HardwareId
                    };

                    var json = JsonSerializer.Serialize(requestData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // Add headers for better compatibility
                    content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");

                    var response = await _httpClient.PostAsync($"{_apiBaseUrl}/validate", content);
                    var responseJson = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var result = JsonSerializer.Deserialize<ValidationResponse>(responseJson);

                        if (result.success)
                        {
                            IsAuthenticated = true;
                            CurrentLicenseKey = licenseKey;

                            // Get expiry date using helper method
                            LicenseExpiry = result.GetExpiryDate();
                            RemainingTime = result.remainingTime > 0 ?
                                TimeSpan.FromSeconds(result.remainingTime) :
                                (LicenseExpiry?.Subtract(DateTime.Now) ?? TimeSpan.FromDays(365));

                            return new AuthenticationResult
                            {
                                Success = true,
                                Message = "License validated successfully",
                                RemainingTime = RemainingTime
                            };
                        }
                        else
                        {
                            return new AuthenticationResult { Success = false, Message = result.message ?? "Invalid license key" };
                        }
                    }
                    else
                    {
                        if (attempt == maxRetries)
                        {
                            return new AuthenticationResult { Success = false, Message = $"Server error: {response.StatusCode}" };
                        }
                        // Retry on server errors
                        await Task.Delay(retryDelayMs);
                        continue;
                    }
                }
                catch (HttpRequestException ex)
                {
                    if (attempt == maxRetries)
                    {
                        return new AuthenticationResult { Success = false, Message = "Network error - please check your internet connection" };
                    }
                    await Task.Delay(retryDelayMs);
                    continue;
                }
                catch (TaskCanceledException)
                {
                    if (attempt == maxRetries)
                    {
                        return new AuthenticationResult { Success = false, Message = "Request timeout - please try again" };
                    }
                    await Task.Delay(retryDelayMs);
                    continue;
                }
                catch (Exception ex)
                {
                    return new AuthenticationResult { Success = false, Message = $"Authentication error: {ex.Message}" };
                }
            }

            return new AuthenticationResult { Success = false, Message = "All retry attempts failed" };
        }

        /// <summary>
        /// Requests a hardware ID reset for the current license
        /// </summary>
        public async Task<AuthenticationResult> RequestHardwareIdReset()
        {
            if (string.IsNullOrEmpty(CurrentLicenseKey))
                return new AuthenticationResult { Success = false, Message = "No active license key" };

            try
            {
                var requestData = new
                {
                    licenseKey = CurrentLicenseKey,
                    hardwareId = HardwareId
                };

                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync($"{_apiBaseUrl}/reset-hwid", content);
                var responseJson = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    return new AuthenticationResult { Success = true, Message = "Hardware ID reset successfully" };
                }
                else
                {
                    var errorResult = JsonSerializer.Deserialize<ValidationResponse>(responseJson);
                    return new AuthenticationResult { Success = false, Message = errorResult?.message ?? "Failed to reset hardware ID" };
                }
            }
            catch (Exception ex)
            {
                return new AuthenticationResult { Success = false, Message = $"Reset error: {ex.Message}" };
            }
        }

        /// <summary>
        /// Logs out the current user
        /// </summary>
        public void Logout()
        {
            IsAuthenticated = false;
            CurrentLicenseKey = null;
            LicenseExpiry = null;
        }

        // TODO: REMOVE THIS FOR PRODUCTION - Mock authentication for development
        /// <summary>
        /// Sets mock authentication for development purposes
        /// REMOVE THIS METHOD FOR PRODUCTION!
        /// </summary>
        public void SetMockAuthentication(string licenseKey, DateTime expiry)
        {
            IsAuthenticated = true;
            CurrentLicenseKey = licenseKey;
            LicenseExpiry = expiry;
        }

        /// <summary>
        /// Generates a unique hardware ID based on system characteristics (matching flasher implementation)
        /// </summary>
        private string GenerateHardwareId()
        {
            try
            {
                var sb = new StringBuilder();
                sb.Append(Environment.ProcessorCount);
                sb.Append(Environment.MachineName);
                sb.Append(Environment.UserName);
                sb.Append(Environment.OSVersion.ToString());

                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(sb.ToString()));
                    return Convert.ToBase64String(hash).Substring(0, 16);
                }
            }
            catch
            {
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Environment.MachineName}_{Environment.UserName}")).Substring(0, 16);
            }
        }

        /// <summary>
        /// Masks a license key for display purposes
        /// </summary>
        private string MaskLicenseKey(string licenseKey)
        {
            if (string.IsNullOrEmpty(licenseKey))
                return "Not Set";

            if (licenseKey.Length <= 8)
                return new string('*', licenseKey.Length);

            return $"{licenseKey.Substring(0, 4)}{"".PadLeft(licenseKey.Length - 8, '*')}{licenseKey.Substring(licenseKey.Length - 4)}";
        }
    }

    /// <summary>
    /// Result of an authentication operation
    /// </summary>
    public class AuthenticationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public TimeSpan RemainingTime { get; set; }
    }

    /// <summary>
    /// Response from the validation API (matching flasher implementation)
    /// </summary>
    internal class ValidationResponse
    {
        public bool success { get; set; }
        public string message { get; set; }
        public string expires_at { get; set; }  // For backward compatibility - as string
        public string expiresAt { get; set; }   // New field name - as string
        public int remainingTime { get; set; }

        // Helper method to get expiry date
        public DateTime? GetExpiryDate()
        {
            string dateStr = expiresAt ?? expires_at;
            if (string.IsNullOrEmpty(dateStr))
                return null;

            if (DateTime.TryParse(dateStr, out DateTime result))
                return result;

            return null;
        }
    }
}
