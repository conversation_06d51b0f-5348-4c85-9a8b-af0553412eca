<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Reminders</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link rel="stylesheet" href="/css/reminders.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>


            <!-- Create New Reminder -->
            <div class="card">
                <div class="card-header">
                    <h2>📝 Create New Reminder</h2>
                </div>
                <div class="card-content">
                    <div class="reminder-form">
                        <div class="form-group">
                            <label for="reminderTitle">Title:</label>
                            <input type="text" id="reminderTitle" class="form-control" placeholder="e.g., Compile in Release Mode">
                        </div>
                        <div class="form-group">
                            <label for="reminderDescription">Description:</label>
                            <textarea id="reminderDescription" class="form-control" rows="3" placeholder="Detailed reminder description..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="reminderPriority">Priority:</label>
                            <select id="reminderPriority" class="form-control">
                                <option value="low">🟢 Low</option>
                                <option value="medium" selected>🟡 Medium</option>
                                <option value="high">🔴 High</option>
                                <option value="critical">🚨 Critical</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="reminderCategory">Category:</label>
                            <select id="reminderCategory" class="form-control">
                                <option value="development">💻 Development</option>
                                <option value="deployment">🚀 Deployment</option>
                                <option value="testing">🧪 Testing</option>
                                <option value="security">🛡️ Security</option>
                                <option value="maintenance">🔧 Maintenance</option>
                                <option value="general">📋 General</option>
                            </select>
                        </div>
                        <button id="createReminderBtn" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Reminder
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Reminder Templates -->
            <div class="card">
                <div class="card-header">
                    <h3>⚡ Quick Templates</h3>
                </div>
                <div class="card-content">
                    <div class="template-grid">
                        <div class="template-card" onclick="useTemplate('release')">
                            <i class="fas fa-rocket"></i>
                            <h4>Release Build</h4>
                            <p>Remember to compile in Release mode</p>
                        </div>
                        <div class="template-card" onclick="useTemplate('debug')">
                            <i class="fas fa-bug"></i>
                            <h4>Debug Check</h4>
                            <p>Disable debug features before deployment</p>
                        </div>
                        <div class="template-card" onclick="useTemplate('backup')">
                            <i class="fas fa-save"></i>
                            <h4>Backup</h4>
                            <p>Create backup before major changes</p>
                        </div>
                        <div class="template-card" onclick="useTemplate('test')">
                            <i class="fas fa-vial"></i>
                            <h4>Testing</h4>
                            <p>Run full test suite before release</p>
                        </div>
                        <div class="template-card" onclick="useTemplate('security')">
                            <i class="fas fa-shield-alt"></i>
                            <h4>Security</h4>
                            <p>Review security settings</p>
                        </div>
                        <div class="template-card" onclick="useTemplate('docs')">
                            <i class="fas fa-file-alt"></i>
                            <h4>Documentation</h4>
                            <p>Update documentation</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Reminders -->
            <div class="card">
                <div class="card-header">
                    <h3>📋 Active Reminders</h3>
                    <div class="reminder-filters">
                        <select id="priorityFilter" class="form-control">
                            <option value="all">All Priorities</option>
                            <option value="critical">🚨 Critical</option>
                            <option value="high">🔴 High</option>
                            <option value="medium">🟡 Medium</option>
                            <option value="low">🟢 Low</option>
                        </select>
                        <select id="categoryFilter" class="form-control">
                            <option value="all">All Categories</option>
                            <option value="development">💻 Development</option>
                            <option value="deployment">🚀 Deployment</option>
                            <option value="testing">🧪 Testing</option>
                            <option value="security">🛡️ Security</option>
                            <option value="maintenance">🔧 Maintenance</option>
                            <option value="general">📋 General</option>
                        </select>
                        <button class="btn btn-secondary" onclick="refreshReminders()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div id="remindersList" class="reminders-list">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading reminders...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Reminders -->
            <div class="card">
                <div class="card-header">
                    <h3>✅ Recently Completed</h3>
                    <button class="btn btn-secondary" onclick="clearCompleted()">
                        <i class="fas fa-trash"></i> Clear All
                    </button>
                </div>
                <div class="card-content">
                    <div id="completedRemindersList" class="reminders-list">
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <p>No completed reminders yet</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script src="/js/reminders.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                // User is authenticated, load page content
                loadReminders();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });
    </script>
</body>
</html>
