﻿#pragma checksum "..\..\..\..\..\Views\RustMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F5B0FAB1F39A3745A35E764F4D83FF3CF7929D7D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.Views {
    
    
    /// <summary>
    /// RustMainWindow
    /// </summary>
    public partial class RustMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 121 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MainToggle;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmergencyStop;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WeaponCategoryCombo;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox WeaponList;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeaponInfo;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RecoilToggle;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HipfireToggle;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HumanisationToggle;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RandomisationToggle;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RapidfireToggle;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CursorCheckToggle;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider DelayScaleSlider;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DelayScaleValue;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalSlider;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HorizontalValue;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalSlider;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VerticalValue;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SensitivityValue;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FovSlider;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FovValue;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AntiAfkToggle;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AfkIntervalText;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CrouchKeyText;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CodelockToggle;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodelockCodeText;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnterCodeButton;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider TimingVariationSlider;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimingVariationValue;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider MovementVariationSlider;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovementVariationValue;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SmoothingSlider;
        
        #line default
        #line hidden
        
        
        #line 264 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SmoothingValue;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MicroAdjustmentsToggle;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalDeviationSlider;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HorizontalDeviationValue;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalDeviationSlider;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VerticalDeviationValue;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider TimingDeviationSlider;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimingDeviationValue;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AdaptiveRandomisationToggle;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToggleScriptHotkey;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToggleRecoilHotkey;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToggleRapidfireHotkey;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToggleAntiAfkHotkey;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EnterCodelockHotkey;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmergencyStopHotkey;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShotsCountText;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccuracyText;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTimeText;
        
        #line default
        #line hidden
        
        
        #line 338 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentWeaponText;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecoilMovementText;
        
        #line default
        #line hidden
        
        
        #line 343 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgRecoilText;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 345 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetStatsButton;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LicenseStatusText;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HardwareIdText;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadConfigButton;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AboutButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;V1.0.0.0;component/views/rustmainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\RustMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.MainToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 3:
            this.EmergencyStop = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.WeaponCategoryCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.WeaponList = ((System.Windows.Controls.ListBox)(target));
            return;
            case 6:
            this.WeaponInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RecoilToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 8:
            this.HipfireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 9:
            this.HumanisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 10:
            this.RandomisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 11:
            this.RapidfireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 12:
            this.CursorCheckToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 13:
            this.DelayScaleSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 14:
            this.DelayScaleValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.HorizontalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.HorizontalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.VerticalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 18:
            this.VerticalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.SensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 20:
            this.SensitivityValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.FovSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 22:
            this.FovValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.AntiAfkToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 24:
            this.AfkIntervalText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.CrouchKeyText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.CodelockToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 27:
            this.CodelockCodeText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            this.EnterCodeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 29:
            this.TimingVariationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 30:
            this.TimingVariationValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.MovementVariationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 32:
            this.MovementVariationValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.SmoothingSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 34:
            this.SmoothingValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.MicroAdjustmentsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 36:
            this.HorizontalDeviationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 37:
            this.HorizontalDeviationValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.VerticalDeviationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 39:
            this.VerticalDeviationValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.TimingDeviationSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 41:
            this.TimingDeviationValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.AdaptiveRandomisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 43:
            this.ToggleScriptHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 44:
            this.ToggleRecoilHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 45:
            this.ToggleRapidfireHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.ToggleAntiAfkHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 47:
            this.EnterCodelockHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 48:
            this.EmergencyStopHotkey = ((System.Windows.Controls.TextBox)(target));
            return;
            case 49:
            this.ShotsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.AccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.SessionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.CurrentWeaponText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.RecoilMovementText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.AvgRecoilText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 55:
            this.StatusIndicator = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.ResetStatsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 57:
            this.LicenseStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.HardwareIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 59:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 60:
            this.LoadConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 61:
            this.AboutButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

