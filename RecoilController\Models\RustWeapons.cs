using System;
using System.Collections.Generic;
using System.Linq;

namespace RecoilController.Models
{
    /// <summary>
    /// Represents a weapon's recoil pattern and properties
    /// </summary>
    public class RustWeapon
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public WeaponCategory Category { get; set; }
        public double RepeatDelay { get; set; }
        public bool IsAutomatic { get; set; }
        public bool HasADS { get; set; }
        public int MagCapacity { get; set; }
        public double StancePenalty { get; set; }
        public double FovOffset { get; set; }
        public double ZoomFactor { get; set; }
        public double MovePenalty { get; set; }
        public RecoilData Recoil { get; set; }

        public RustWeapon()
        {
            Recoil = new RecoilData();
        }
    }

    /// <summary>
    /// Weapon categories for organization
    /// </summary>
    public enum WeaponCategory
    {
        AssaultRifles,
        SMGs,
        LMGs,
        Shotguns,
        SniperRifles,
        Pistols,
        Other
    }

    /// <summary>
    /// Recoil pattern data for a weapon
    /// </summary>
    public class RecoilData
    {
        public bool CurvesAsScalar { get; set; }
        public double YawMax { get; set; }
        public double YawMin { get; set; }
        public double PitchMax { get; set; }
        public double PitchMin { get; set; }
        public double TimeMin { get; set; }
        public double TimeMax { get; set; }
        public double AdsScale { get; set; }
        public List<double> CurveX { get; set; }
        public List<double> CurveY { get; set; }

        public RecoilData()
        {
            CurveX = new List<double>();
            CurveY = new List<double>();
        }

        /// <summary>
        /// Gets the recoil point for a specific bullet number
        /// </summary>
        public RecoilPoint GetRecoilPoint(int bulletIndex, bool isADS = false)
        {
            if (bulletIndex >= CurveX.Count || bulletIndex >= CurveY.Count)
                return new RecoilPoint(0, 0);

            double x = CurveX[bulletIndex];
            double y = CurveY[bulletIndex];

            // Apply ADS scaling if aiming down sights
            if (isADS)
            {
                x *= AdsScale;
                y *= AdsScale;
            }

            return new RecoilPoint(x, y);
        }

        /// <summary>
        /// Gets the delta movement needed for a specific bullet
        /// </summary>
        public RecoilPoint GetDeltaMovement(int bulletIndex, bool isADS = false)
        {
            var currentPoint = GetRecoilPoint(bulletIndex, isADS);
            var previousPoint = bulletIndex > 0 ? GetRecoilPoint(bulletIndex - 1, isADS) : new RecoilPoint(0, 0);

            return new RecoilPoint(
                currentPoint.X - previousPoint.X,
                currentPoint.Y - previousPoint.Y
            );
        }
    }

    /// <summary>
    /// Represents a single recoil point
    /// </summary>
    public struct RecoilPoint
    {
        public double X { get; }
        public double Y { get; }

        public RecoilPoint(double x, double y)
        {
            X = x;
            Y = y;
        }

        public RecoilPoint Scale(double factor)
        {
            return new RecoilPoint(X * factor, Y * factor);
        }

        public RecoilPoint Add(RecoilPoint other)
        {
            return new RecoilPoint(X + other.X, Y + other.Y);
        }
    }

    /// <summary>
    /// Weapon attachment that modifies recoil
    /// </summary>
    public class WeaponAttachment
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public AttachmentModifiers Modifiers { get; set; }
        public double FovBias { get; set; }
        public double FovOffset { get; set; }
        public double ZoomFactor { get; set; }

        public WeaponAttachment()
        {
            Modifiers = new AttachmentModifiers();
        }
    }

    /// <summary>
    /// Modifiers applied by attachments
    /// </summary>
    public class AttachmentModifiers
    {
        public ModifierData Recoil { get; set; }
        public ModifierData RepeatDelay { get; set; }

        public AttachmentModifiers()
        {
            Recoil = new ModifierData();
            RepeatDelay = new ModifierData();
        }
    }

    /// <summary>
    /// Individual modifier data
    /// </summary>
    public class ModifierData
    {
        public bool Enabled { get; set; }
        public double Scalar { get; set; } = 1.0;
        public double Offset { get; set; } = 0.0;
    }

    /// <summary>
    /// Static class containing all Rust weapons data
    /// </summary>
    public static class RustWeaponsData
    {
        private static Dictionary<string, RustWeapon> _weapons;
        private static Dictionary<string, WeaponAttachment> _attachments;

        static RustWeaponsData()
        {
            InitializeWeapons();
            InitializeAttachments();
        }

        public static Dictionary<string, RustWeapon> GetAllWeapons() => _weapons;
        public static Dictionary<string, WeaponAttachment> GetAllAttachments() => _attachments;

        public static List<RustWeapon> GetWeaponsByCategory(WeaponCategory category)
        {
            return _weapons.Values.Where(w => w.Category == category).ToList();
        }

        public static RustWeapon GetWeapon(string name)
        {
            return _weapons.TryGetValue(name, out var weapon) ? weapon : null;
        }

        public static WeaponAttachment GetAttachment(string name)
        {
            return _attachments.TryGetValue(name, out var attachment) ? attachment : null;
        }

        private static void InitializeWeapons()
        {
            _weapons = new Dictionary<string, RustWeapon>();

            // Assault Rifles
            AddWeapon(CreateAK47());
            AddWeapon(CreateLR300());
            AddWeapon(CreateM249());
            AddWeapon(CreateAK47U());

            // SMGs
            AddWeapon(CreateMP5());
            AddWeapon(CreateThompson());
            AddWeapon(CreateCustomSMG());

            // LMGs
            AddWeapon(CreateHMLMG());

            // Shotguns
            AddWeapon(CreatePumpShotgun());
            AddWeapon(CreateWaterpipeShotgun());
            AddWeapon(CreateDoubleShotgun());

            // Sniper Rifles
            AddWeapon(CreateBoltRifle());
            AddWeapon(CreateL96());

            // Semi-Auto Rifles
            AddWeapon(CreateSemiAutoRifle());

            // Pistols
            AddWeapon(CreatePython());
            AddWeapon(CreateRevolver());
            AddWeapon(CreateSemiAutoPistol());
            AddWeapon(CreateM92());
        }

        private static void AddWeapon(RustWeapon weapon)
        {
            _weapons[weapon.Name] = weapon;
        }

        // Weapon creation methods with actual recoil data
        private static RustWeapon CreateAK47()
        {
            return new RustWeapon
            {
                Name = "ak47u",
                DisplayName = "AK-47",
                Category = WeaponCategory.AssaultRifles,
                RepeatDelay = 133.30000638961792,
                IsAutomatic = true,
                HasADS = true,
                MagCapacity = 30,
                StancePenalty = 1.0,
                FovOffset = -30.0,
                ZoomFactor = 1.6666666269302368,
                MovePenalty = 0.20000000298023224,
                Recoil = new RecoilData
                {
                    CurvesAsScalar = true,
                    YawMax = 2.5,
                    YawMin = 1.5,
                    PitchMax = -3.5,
                    PitchMin = -2.5,
                    TimeMin = 100.00000149011612,
                    TimeMax = 100.00000149011612,
                    AdsScale = 0.75,
                    CurveX = new List<double> { 0.0, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5, 2.5 },
                    CurveY = new List<double> { -0.0, -0.2666199883647539, -0.5209992038497219, -0.7633411283642054, -0.9938492438175061, -1.2127270321189252, -1.420177975177765, -1.6164055549033263, -1.8016132532049107, -1.9760045519918201, -2.139782933173356, -2.2931518786588203, -2.436314870357513, -2.569475390178738, -2.692836920031795, -2.806602941825986, -2.910976937470613, -3.006162388874977, -3.0923627779483795, -3.1697815866001227, -3.2386222967395075, -3.2990883902758363, -3.351383349118409, -3.3957106551765293, -3.4322737903594964, -3.4612762365766145, -3.4829214757371836, -3.4974129897505044, -3.50495426052588, -3.5057487699726115 }
                }
            };
        }

        private static RustWeapon CreateLR300()
        {
            return new RustWeapon
            {
                Name = "lr300",
                DisplayName = "LR-300 Assault Rifle",
                Category = WeaponCategory.AssaultRifles,
                RepeatDelay = 120.00000476837158,
                IsAutomatic = true,
                HasADS = true,
                MagCapacity = 30,
                StancePenalty = 1.0,
                FovOffset = -30.0,
                ZoomFactor = 1.6666666269302368,
                MovePenalty = 0.20000000298023224,
                Recoil = new RecoilData
                {
                    CurvesAsScalar = true,
                    YawMax = 1.5,
                    YawMin = 1.0,
                    PitchMax = -2.5,
                    PitchMin = -1.5,
                    TimeMin = 100.00000149011612,
                    TimeMax = 100.00000149011612,
                    AdsScale = 0.75,
                    CurveX = new List<double> { 0.0, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5, 1.5 },
                    CurveY = new List<double> { -0.0, -0.16666666666666666, -0.3333333333333333, -0.5, -0.6666666666666666, -0.8333333333333334, -1.0, -1.1666666666666667, -1.3333333333333333, -1.5, -1.6666666666666667, -1.8333333333333333, -2.0, -2.1666666666666665, -2.3333333333333335, -2.5, -2.6666666666666665, -2.8333333333333335, -3.0, -3.1666666666666665, -3.3333333333333335, -3.5, -3.6666666666666665, -3.8333333333333335, -4.0, -4.166666666666667, -4.333333333333333, -4.5, -4.666666666666667, -4.833333333333333 }
                }
            };
        }

        private static RustWeapon CreateM249()
        {
            return new RustWeapon { Name = "m249", DisplayName = "M249", Category = WeaponCategory.LMGs };
        }

        private static RustWeapon CreateAK47U()
        {
            return new RustWeapon { Name = "ak47u", DisplayName = "AK-47U", Category = WeaponCategory.AssaultRifles };
        }

        private static RustWeapon CreateMP5()
        {
            return new RustWeapon
            {
                Name = "mp5",
                DisplayName = "MP5A4",
                Category = WeaponCategory.SMGs,
                RepeatDelay = 100.00000476837158,
                IsAutomatic = true,
                HasADS = true,
                MagCapacity = 30,
                StancePenalty = 1.0,
                FovOffset = -30.0,
                ZoomFactor = 1.6666666269302368,
                MovePenalty = 0.20000000298023224,
                Recoil = new RecoilData
                {
                    CurvesAsScalar = true,
                    YawMax = 1.2,
                    YawMin = 0.8,
                    PitchMax = -2.0,
                    PitchMin = -1.2,
                    TimeMin = 100.00000149011612,
                    TimeMax = 100.00000149011612,
                    AdsScale = 0.75,
                    CurveX = new List<double> { 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0 },
                    CurveY = new List<double> { -0.0, -0.13333333333333333, -0.26666666666666666, -0.4, -0.5333333333333333, -0.6666666666666666, -0.8, -0.9333333333333333, -1.0666666666666667, -1.2, -1.3333333333333333, -1.4666666666666666, -1.6, -1.7333333333333334, -1.8666666666666667, -2.0, -2.1333333333333333, -2.2666666666666666, -2.4, -2.5333333333333332, -2.6666666666666665, -2.8, -2.9333333333333336, -3.066666666666667, -3.2, -3.3333333333333335, -3.466666666666667, -3.6, -3.7333333333333334, -3.8666666666666667 }
                }
            };
        }

        private static RustWeapon CreateThompson()
        {
            return new RustWeapon { Name = "thompson", DisplayName = "Thompson", Category = WeaponCategory.SMGs };
        }

        private static RustWeapon CreateCustomSMG()
        {
            return new RustWeapon { Name = "smg", DisplayName = "Custom SMG", Category = WeaponCategory.SMGs };
        }

        private static RustWeapon CreateHMLMG()
        {
            return new RustWeapon { Name = "hmlmg", DisplayName = "HMLMG", Category = WeaponCategory.LMGs };
        }

        private static RustWeapon CreatePumpShotgun()
        {
            return new RustWeapon { Name = "shotgun_pump", DisplayName = "Pump Shotgun", Category = WeaponCategory.Shotguns };
        }

        private static RustWeapon CreateWaterpipeShotgun()
        {
            return new RustWeapon { Name = "shotgun_waterpipe", DisplayName = "Waterpipe Shotgun", Category = WeaponCategory.Shotguns };
        }

        private static RustWeapon CreateDoubleShotgun()
        {
            return new RustWeapon { Name = "double_shotgun", DisplayName = "Double Barrel Shotgun", Category = WeaponCategory.Shotguns };
        }

        private static RustWeapon CreateBoltRifle()
        {
            return new RustWeapon { Name = "bolt_rifle", DisplayName = "Bolt Action Rifle", Category = WeaponCategory.SniperRifles };
        }

        private static RustWeapon CreateL96()
        {
            return new RustWeapon { Name = "l96", DisplayName = "L96 Rifle", Category = WeaponCategory.SniperRifles };
        }

        private static RustWeapon CreateSemiAutoRifle()
        {
            return new RustWeapon { Name = "semi_auto_rifle", DisplayName = "Semi-Automatic Rifle", Category = WeaponCategory.Other };
        }

        private static RustWeapon CreatePython()
        {
            return new RustWeapon { Name = "python", DisplayName = "Python Revolver", Category = WeaponCategory.Pistols };
        }

        private static RustWeapon CreateRevolver()
        {
            return new RustWeapon { Name = "pistol_revolver", DisplayName = "Revolver", Category = WeaponCategory.Pistols };
        }

        private static RustWeapon CreateSemiAutoPistol()
        {
            return new RustWeapon { Name = "pistol_semiauto", DisplayName = "Semi-Automatic Pistol", Category = WeaponCategory.Pistols };
        }

        private static RustWeapon CreateM92()
        {
            return new RustWeapon { Name = "m92", DisplayName = "M92 Pistol", Category = WeaponCategory.Pistols };
        }

        private static void InitializeAttachments()
        {
            _attachments = new Dictionary<string, WeaponAttachment>();
            // Attachments will be implemented in the next part
        }
    }
}
