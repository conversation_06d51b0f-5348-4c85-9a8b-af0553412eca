using System;
using System.Windows;
using System.Windows.Input;
using System.Threading.Tasks;

namespace RecoilController.Views
{
    public partial class LoginWindow : Window
    {
        public bool IsAuthenticated { get; private set; } = false;
        public string LicenseKey { get; private set; } = "";
        public TimeSpan RemainingTime { get; private set; } = TimeSpan.Zero;

        public LoginWindow()
        {
            InitializeComponent();
            LoadSavedCredentials();
        }

        private void LoadSavedCredentials()
        {
            try
            {
                // TODO: Implement settings storage
                // For now, just skip loading saved credentials
                System.Diagnostics.Debug.WriteLine("Loading saved credentials (not implemented)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading saved credentials: {ex.Message}");
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            var licenseKey = LicenseKeyTextBox.Text.Trim();
            
            if (string.IsNullOrEmpty(licenseKey))
            {
                ShowStatus("Please enter a license key", true);
                return;
            }

            LoginButton.IsEnabled = false;
            LoginButton.Content = "Authenticating...";
            ShowStatus("Validating license...", false);

            try
            {
                // Validate license key with backend
                var result = await ValidateLicenseKey(licenseKey);
                
                if (result.IsValid)
                {
                    IsAuthenticated = true;
                    LicenseKey = licenseKey;
                    RemainingTime = result.RemainingTime;

                    // TODO: Save credentials if remember me is checked
                    if (RememberMeCheckBox.IsChecked == true)
                    {
                        System.Diagnostics.Debug.WriteLine("Saving credentials (not implemented)");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Not saving credentials");
                    }

                    ShowStatus("Authentication successful!", false);
                    await Task.Delay(1000); // Show success message briefly
                    this.DialogResult = true;
                    this.Close();
                }
                else
                {
                    ShowStatus($"Authentication failed: {result.ErrorMessage}", true);
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Connection error: {ex.Message}", true);
            }
            finally
            {
                LoginButton.IsEnabled = true;
                LoginButton.Content = "Authenticate";
            }
        }

        private async Task<LicenseValidationResult> ValidateLicenseKey(string licenseKey)
        {
            try
            {
                using var client = new System.Net.Http.HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                // Generate hardware ID
                var hardwareId = GenerateHardwareId();

                var requestData = new
                {
                    licenseKey = licenseKey,
                    hardwareId = hardwareId,
                    applicationVersion = "4.2.1",
                    timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                var json = System.Text.Json.JsonSerializer.Serialize(requestData);
                var content = new System.Net.Http.StringContent(json, System.Text.Encoding.UTF8, "application/json");

                var response = await client.PostAsync("http://217.154.58.14:3000/api/validate", content);
                var responseText = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var result = System.Text.Json.JsonSerializer.Deserialize<System.Text.Json.JsonElement>(responseText);

                    if (result.GetProperty("success").GetBoolean())
                    {
                        var remainingTime = result.TryGetProperty("remainingTime", out var time) ?
                            TimeSpan.FromSeconds(time.GetInt64()) : TimeSpan.FromDays(30);

                        return new LicenseValidationResult
                        {
                            IsValid = true,
                            RemainingTime = remainingTime,
                            ErrorMessage = ""
                        };
                    }
                    else
                    {
                        var message = result.TryGetProperty("message", out var msg) ?
                            msg.GetString() : "Authentication failed";

                        return new LicenseValidationResult
                        {
                            IsValid = false,
                            RemainingTime = TimeSpan.Zero,
                            ErrorMessage = message
                        };
                    }
                }
                else
                {
                    return new LicenseValidationResult
                    {
                        IsValid = false,
                        RemainingTime = TimeSpan.Zero,
                        ErrorMessage = $"Server error: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LicenseValidationResult
                {
                    IsValid = false,
                    RemainingTime = TimeSpan.Zero,
                    ErrorMessage = $"Connection error: {ex.Message}"
                };
            }
        }

        private string GenerateHardwareId()
        {
            try
            {
                var components = new System.Text.StringBuilder();

                // Get CPU ID
                using (var searcher = new System.Management.ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (System.Management.ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["ProcessorId"]?.ToString() ?? "");
                        break;
                    }
                }

                // Get Motherboard Serial
                using (var searcher = new System.Management.ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (System.Management.ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }

                // Create hash
                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(components.ToString()));
                    return Convert.ToHexString(hash)[..16]; // First 16 characters
                }
            }
            catch
            {
                return "HWID_ERROR_" + DateTime.Now.Ticks.ToString()[..8];
            }
        }

        private void ShowStatus(string message, bool isError)
        {
            StatusTextBlock.Text = message;
            StatusTextBlock.Foreground = isError ? 
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(255, 68, 68)) :
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 255, 136));
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                this.DialogResult = false;
                this.Close();
                e.Handled = true;
            }
        }
    }

    public class LicenseValidationResult
    {
        public bool IsValid { get; set; }
        public TimeSpan RemainingTime { get; set; }
        public string ErrorMessage { get; set; } = "";
    }
}
