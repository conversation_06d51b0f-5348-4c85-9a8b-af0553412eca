/* Reminders Page Specific Styles */

.reminder-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    align-items: end;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.template-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.template-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.template-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.template-card h4 {
    color: var(--text-primary);
    margin-bottom: 5px;
}

.template-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.reminder-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.reminder-filters .form-control {
    max-width: 200px;
}

.reminders-list {
    min-height: 200px;
}

.reminder-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    transition: var(--transition);
}

.reminder-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.reminder-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.reminder-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 5px;
}

.reminder-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-muted);
}

.reminder-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.reminder-priority.low {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.reminder-priority.medium {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.reminder-priority.high {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
}

.reminder-priority.critical {
    background: rgba(220, 53, 69, 0.3);
    color: var(--danger-color);
    animation: pulse 2s infinite;
}

.reminder-category {
    background: var(--bg-dark);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
}

.reminder-description {
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.5;
}

.reminder-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.reminder-item.completed {
    opacity: 0.7;
    border-color: var(--success-color);
}

.reminder-item.completed .reminder-title {
    text-decoration: line-through;
    color: var(--text-muted);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Responsive design */
@media (max-width: 768px) {
    .reminder-form {
        grid-template-columns: 1fr;
    }
    
    .template-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .reminder-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .reminder-filters .form-control {
        max-width: none;
    }
    
    .reminder-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .reminder-actions {
        justify-content: flex-start;
        flex-wrap: wrap;
    }
}
