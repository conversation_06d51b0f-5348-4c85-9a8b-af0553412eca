# ESP32-S2 HID Mouse Firmware Project Summary

## 🎯 Project Goals

### Primary Objective
Develop ESP32-S2 firmware that acts as a **native USB HID mouse** for real-time recoil compensation in the Octane Recoil Scripts gaming application.

### Key Requirements
- **Native USB HID**: Must appear as physical mouse in Windows Device Manager
- **High Performance**: 1000Hz movement capability for competitive gaming
- **Real-time Communication**: JSON protocol with C# desktop application
- **Robust Operation**: Error handling, recovery, and production reliability

## 📊 Analysis Results

### Desktop App Communication Protocol (Confirmed)
From analyzing `desktop-app/RecoilController/Services/ESP32Service.cs`:
```csharp
// Confirmed settings
_serialPort = new SerialPort(portName, 115200, Parity.None, 8, StopBits.One);

// Confirmed command format
await SendCommandInternalAsync(new ESP32Command
{
    Type = "mouse_move",
    Data = new { x = deltaX, y = deltaY }
});
```

### Recoil System Requirements (Confirmed)
From analyzing `desktop-app/RecoilController/Services/RecoilEngine.cs`:
- **Timing**: Weapon RPM-based intervals (e.g., AK47: 133ms between shots)
- **Precision**: Sub-pixel movement accuracy required
- **Frequency**: High-frequency movement sequences for smooth compensation
- **Integration**: Real-time processing with `ExecuteMouseMovement()` calls

### Reference Implementation (Validated)
From `useful post I found/info.txt`:
- **Proven Approach**: ESP32-S3 with TinyUSB successfully implemented
- **Command Queue**: 64-command FIFO prevents HID saturation
- **Protocol**: Simple `M<x>,<y>` commands work effectively
- **Performance**: 921600 baud, 1ms polling achieves smooth operation

## ❌ Why PlatformIO Failed

### Root Cause Analysis
1. **Missing TinyUSB Component**
   ```
   CMake Error: Failed to resolve component 'tinyusb' required by component 'main'
   ```
   - PlatformIO's ESP-IDF lacks proper TinyUSB integration
   - Manual TinyUSB setup is complex and error-prone

2. **Framework Conflicts**
   - ESP-IDF vs Arduino framework USB implementations differ
   - Version mismatches between ESP-IDF and USB libraries
   - Complex CMake configuration requirements

3. **Development Complexity**
   - ESP-IDF requires 50+ lines for USB HID setup
   - Arduino requires 2 lines: `USB.begin()` and `Mouse.begin()`
   - Documentation and community support heavily favor Arduino

### Lesson Learned
**Use Arduino IDE/framework** for ESP32-S2 USB HID projects due to:
- Built-in USB HID support
- Simpler configuration
- Better documentation
- Larger community support

## ✅ Recommended Implementation

### Development Environment
- **Arduino IDE 2.x** with ESP32-S2 board support
- **Board**: ESP32S2 Dev Module
- **USB Mode**: Hardware CDC and JTAG
- **Libraries**: ArduinoJson for command parsing

### Core Architecture
```cpp
// Simple, proven approach
#include "USB.h"
#include "USBHIDMouse.h"
#include <ArduinoJson.h>

USBHIDMouse Mouse;
QueueHandle_t commandQueue;

void setup() {
    Serial.begin(115200);
    Mouse.begin();
    USB.begin();
    commandQueue = xQueueCreate(64, sizeof(HIDCommand));
}

void loop() {
    processSerialCommands();  // Parse JSON from desktop app
    processCommandQueue();    // Execute HID commands smoothly
    updateLEDStatus();        // Visual feedback
}
```

### Communication Protocol
```json
// Desktop → ESP32
{"Type": "mouse_move", "Data": {"x": -5, "y": 12}}

// ESP32 → Desktop  
{"status": "moved", "queue_size": 5}
```

## 📋 Implementation Checklist

### Phase 1: Basic Functionality
- [ ] Arduino IDE setup with ESP32-S2 support
- [ ] Basic USB HID mouse implementation
- [ ] LED status indicators (GPIO15)
- [ ] Serial communication at 115200 baud
- [ ] Simple command parsing (text format)

### Phase 2: Protocol Implementation
- [ ] JSON command parsing with ArduinoJson
- [ ] Command queue system (64 commands)
- [ ] Device auto-detection support
- [ ] Error handling and responses
- [ ] Desktop app integration testing

### Phase 3: Performance Optimization
- [ ] 1ms command processing intervals
- [ ] High-frequency movement support
- [ ] Memory optimization (<50KB usage)
- [ ] Timing precision for recoil compensation
- [ ] Stress testing and validation

### Phase 4: Production Features
- [ ] Comprehensive error recovery
- [ ] Watchdog timer implementation
- [ ] Production build configuration
- [ ] Documentation and user guides
- [ ] Quality assurance testing

## 🎮 Recoil Compensation Details

### How It Works
1. **Desktop App** calculates recoil compensation based on:
   - Weapon type and RPM
   - User sensitivity settings
   - FOV and scope multipliers
   - Bullet number in pattern

2. **ESP32 Receives** movement commands:
   ```json
   {"Type": "mouse_move", "Data": {"x": -2, "y": 5}}
   ```

3. **Command Queue** smooths execution:
   - Queues commands to prevent HID saturation
   - Processes at 1ms intervals
   - Maintains timing precision

4. **HID Mouse** executes movement:
   ```cpp
   Mouse.move(deltaX, deltaY);  // Native USB HID call
   ```

### Performance Requirements
- **Latency**: <1ms from command to execution
- **Frequency**: Up to 1000 commands/second
- **Precision**: ±1 pixel accuracy
- **Smoothness**: No stuttering or lag

## 🔧 Hardware Specifications

### ESP32-S2 Mini
- **MCU**: ESP32-S2FN4R2 (240MHz)
- **Flash**: 4MB
- **PSRAM**: 2MB  
- **USB**: Native USB 1.1 (no external chip needed)
- **GPIO15**: Built-in LED
- **Power**: 5V via USB

### Memory Usage
- **Flash**: ~1.4MB / 4MB (Arduino core + libraries + app)
- **RAM**: ~80KB / 320KB (buffers + variables + stack)
- **PSRAM**: Available for large data structures

### Performance Characteristics
- **USB Polling**: 1000Hz (1ms intervals)
- **Serial Baud**: 115200 (sufficient for command rate)
- **Processing**: Real-time with FreeRTOS
- **Power**: <500mA typical usage

## 🚀 Next Steps

### Immediate Actions
1. **Set up Arduino IDE** with ESP32-S2 board support
2. **Create basic firmware** with USB HID mouse functionality
3. **Test hardware** with simple movement commands
4. **Implement serial protocol** with JSON parsing
5. **Test integration** with desktop application

### Development Timeline
- **Week 1**: Basic HID mouse + LED control
- **Week 2**: Serial protocol + command parsing  
- **Week 3**: Command queue + performance optimization
- **Week 4**: Desktop app integration + testing
- **Week 5**: Error handling + production features

### Success Criteria
- ✅ Native USB HID mouse recognition in Windows
- ✅ Smooth recoil compensation without stuttering
- ✅ Reliable communication with desktop app
- ✅ <1ms command processing latency
- ✅ Production-ready stability and error handling

## 📚 Documentation Structure

All detailed information is organized in the `/documentation` folder:

1. **[README.md](README.md)** - Overview and getting started
2. **[FIRMWARE_REQUIREMENTS.md](FIRMWARE_REQUIREMENTS.md)** - Complete technical specs
3. **[COMMUNICATION_PROTOCOL.md](COMMUNICATION_PROTOCOL.md)** - Protocol details
4. **[DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)** - Step-by-step implementation
5. **[FEATURE_CHECKLIST.md](FEATURE_CHECKLIST.md)** - Implementation tracking
6. **[LIBRARIES_AND_DEPENDENCIES.md](LIBRARIES_AND_DEPENDENCIES.md)** - Setup guide

## 🎯 Key Success Factors

### Technical
- **Use Arduino framework** (not ESP-IDF) for USB HID
- **Implement command queue** for smooth operation
- **Maintain 1ms timing** for competitive performance
- **Handle errors gracefully** for production reliability

### Process
- **Start simple** with basic HID mouse functionality
- **Test incrementally** at each development phase
- **Integrate early** with desktop application
- **Optimize performance** based on real-world testing

### Quality
- **Follow specifications** exactly for compatibility
- **Test thoroughly** with various scenarios
- **Document everything** for maintainability
- **Plan for production** from the beginning

---

**Ready to start development!** Begin with the [Development Guide](DEVELOPMENT_GUIDE.md) for detailed implementation instructions.
