<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="/css/main-theme.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-primary) 100%);
        }
        
        .access-denied-container {
            text-align: center;
            max-width: 500px;
            padding: 40px;
            background: var(--bg-secondary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xl);
            border: 2px solid var(--primary-color);
        }
        
        .access-denied-icon {
            font-size: 4rem;
            color: var(--danger-color);
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .access-denied-title {
            font-size: 2rem;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 15px;
        }
        
        .access-denied-message {
            color: var(--text-secondary);
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .access-denied-details {
            background: var(--bg-dark);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            border-left: 4px solid var(--danger-color);
        }
        
        .access-denied-details h4 {
            color: var(--danger-color);
            margin-bottom: 10px;
        }
        
        .access-denied-details ul {
            text-align: left;
            color: var(--text-muted);
        }
        
        .back-button {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: var(--border-radius);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .security-info {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 102, 0, 0.1);
            border-radius: var(--border-radius);
            border: 1px solid var(--primary-color);
        }
        
        .security-info small {
            color: var(--text-muted);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="access-denied-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        
        <h1 class="access-denied-title">Access Denied</h1>
        
        <p class="access-denied-message">
            You do not have permission to access this administrative panel. 
            This system is restricted to authorized personnel only.
        </p>
        
        <div class="access-denied-details">
            <h4><i class="fas fa-exclamation-triangle"></i> Security Notice</h4>
            <ul>
                <li>This admin panel is IP-restricted</li>
                <li>Only authorized administrators can access this system</li>
                <li>All access attempts are logged and monitored</li>
                <li>Unauthorized access attempts may result in IP blocking</li>
            </ul>
        </div>
        
        <a href="/" class="back-button">
            <i class="fas fa-home"></i> Return to Home
        </a>
        
        <div class="security-info">
            <small>
                <i class="fas fa-info-circle"></i>
                If you believe you should have access to this system, please contact your system administrator.
                <br>
                Incident ID: <span id="incidentId"></span>
            </small>
        </div>
    </div>

    <script>
        // Generate a random incident ID for tracking
        document.getElementById('incidentId').textContent = 
            'AD-' + Math.random().toString(36).substr(2, 9).toUpperCase();
        
        // Log the access attempt
        console.log('Access denied at:', new Date().toISOString());
        
        // Optional: Send access attempt to server for logging
        fetch('/api/security/log-access-denied', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                referrer: document.referrer
            })
        }).catch(() => {
            // Silently fail if logging endpoint doesn't exist
        });
    </script>
</body>
</html>
