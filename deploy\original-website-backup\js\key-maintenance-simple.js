// Simple Key Maintenance JavaScript for Enhanced HTML
class KeyMaintenanceManager {
    constructor() {
        this.licenses = [];
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadLicenses();
    }

    setupEventListeners() {
        // Create license form
        const createForm = document.getElementById('createLicenseForm');
        if (createForm) {
            createForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.createLicense();
            });
        }

        // Copy functionality for license keys
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('copy-button')) {
                const licenseKey = e.target.getAttribute('data-key');
                this.copyToClipboard(licenseKey, e.target);
            }
        });
    }

    async loadLicenses() {
        try {
            const response = await fetch('/api/admin/licenses');
            const data = await response.json();
            
            if (data.success) {
                this.licenses = data.licenses;
                this.renderLicenses();
            } else {
                console.error('Failed to load licenses:', data.message);
                this.showError('Failed to load licenses');
            }
        } catch (error) {
            console.error('Error loading licenses:', error);
            this.showError('Error loading licenses');
        }
    }

    async createLicense() {
        const duration = document.getElementById('duration').value;
        const notes = document.getElementById('notes').value;

        try {
            const response = await fetch('/api/admin/create-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    duration: duration,
                    notes: notes
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('License created successfully!');
                document.getElementById('notes').value = '';
                await this.loadLicenses();
            } else {
                this.showError(data.message || 'Failed to create license');
            }
        } catch (error) {
            console.error('Error creating license:', error);
            this.showError('Error creating license');
        }
    }

    renderLicenses() {
        const container = document.getElementById('licensesContainer');
        if (!container) return;

        if (this.licenses.length === 0) {
            container.innerHTML = '<div class="no-licenses">No licenses found. Create your first license above!</div>';
            return;
        }

        container.innerHTML = this.licenses.map(license => this.renderLicenseCard(license)).join('');
    }

    renderLicenseCard(license) {
        const isExpired = license.expires_at && new Date(license.expires_at) <= new Date();
        const statusClass = isExpired ? 'status-expired' : 'status-active';
        const statusText = isExpired ? 'Expired' : 'Active';

        const expiryDate = license.expires_at ? 
            new Date(license.expires_at).toLocaleDateString() : 
            'Never';

        const createdDate = license.created_at ? 
            new Date(license.created_at).toLocaleDateString() : 
            'Unknown';

        return `
            <div class="license-card">
                <div class="license-key" onclick="this.select()">
                    ${license.key}
                    <button class="copy-button" data-key="${license.key}">Copy</button>
                </div>
                
                <div class="license-info">
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value ${statusClass}">${statusText}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Duration</div>
                        <div class="info-value">${license.duration}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Created</div>
                        <div class="info-value">${createdDate}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Expires</div>
                        <div class="info-value">${expiryDate}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Hardware ID</div>
                        <div class="info-value">${license.hardware_id || 'Not bound'}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Notes</div>
                        <div class="info-value">${license.notes || 'No notes'}</div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="btn-small btn-reset" onclick="keyManager.resetHardwareId('${license.key}')">
                        Reset HWID
                    </button>
                    <button class="btn-small btn-delete" onclick="keyManager.deleteLicense('${license.key}')">
                        Delete
                    </button>
                </div>
            </div>
        `;
    }

    async resetHardwareId(licenseKey) {
        if (!confirm('Are you sure you want to reset the hardware ID for this license?')) {
            return;
        }

        try {
            const response = await fetch('/api/admin/reset-hwid', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: licenseKey
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Hardware ID reset successfully!');
                await this.loadLicenses();
            } else {
                this.showError(data.message || 'Failed to reset hardware ID');
            }
        } catch (error) {
            console.error('Error resetting hardware ID:', error);
            this.showError('Error resetting hardware ID');
        }
    }

    async deleteLicense(licenseKey) {
        if (!confirm('Are you sure you want to delete this license? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch('/api/admin/delete-license', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    licenseKey: licenseKey
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('License deleted successfully!');
                await this.loadLicenses();
            } else {
                this.showError(data.message || 'Failed to delete license');
            }
        } catch (error) {
            console.error('Error deleting license:', error);
            this.showError('Error deleting license');
        }
    }

    copyToClipboard(text, button) {
        if (!navigator.clipboard || !navigator.clipboard.writeText) {
            // Fallback for older browsers
            this.fallbackCopyToClipboard(text, button);
            return;
        }

        navigator.clipboard.writeText(text).then(() => {
            const originalText = button.textContent;
            button.textContent = '✓ Copied!';
            button.classList.add('copied');

            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('copied');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy: ', err);
            this.fallbackCopyToClipboard(text, button);
        });
    }

    fallbackCopyToClipboard(text, button) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                const originalText = button.textContent;
                button.textContent = '✓ Copied!';
                button.classList.add('copied');
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            } else {
                this.showError('Failed to copy to clipboard');
            }
        } catch (err) {
            console.error('Fallback copy failed: ', err);
            this.showError('Copy failed - please copy manually: ' + text);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 10000;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.keyManager = new KeyMaintenanceManager();
});

console.log('✅ Key Maintenance Manager loaded');
