# ESP32-S2 HID Mouse Firmware Requirements

## Overview
This document outlines the complete requirements for developing ESP32-S2 firmware that acts as a native USB HID mouse for the Octane Recoil Scripts project.

## Hardware Specifications
- **Board**: ESP32-S2 Mini
- **Flash**: 4MB
- **PSRAM**: 2MB
- **USB**: Native USB support (no external USB-to-Serial chip needed)
- **LED**: GPIO15 (built-in LED for status indication)

## Core Functionality Requirements

### 1. USB HID Mouse Emulation
- **Must** act as a native USB HID mouse device (not virtual)
- **Must** appear in Windows Device Manager as "HID-compliant mouse"
- **Must** support standard mouse operations:
  - Relative movement (X/Y coordinates)
  - Left/Right/Middle mouse button clicks
  - Button press and release events
- **Must** use proper USB HID descriptors for mouse functionality
- **Must** support high-frequency mouse movements (1000Hz polling rate preferred)

### 2. Serial Communication Protocol
- **Baud Rate**: 115200 (confirmed from desktop app analysis)
- **Format**: JSON commands over serial (confirmed from ESP32Service.cs)
- **Commands to Support**:
  ```json
  {"Type": "init", "Data": {"version": "4.2.1", "app": "OctaneRecoilScripts"}}
  {"Type": "mouse_move", "Data": {"x": deltaX, "y": deltaY}}
  {"Type": "mouse_click", "Data": {"button": "left|right|middle", "action": "down|up"}}
  {"Type": "ping", "Data": {}}
  {"Type": "status", "Data": {}}
  ```

### 3. Command Queue System
- **Must** implement command queuing to prevent HID saturation
- **Queue Size**: 64 commands minimum (based on reference implementation)
- **Must** process commands at 1ms intervals maximum
- **Must** handle high-frequency recoil movements smoothly

### 4. LED Status Indicators
- **GPIO15 LED Behavior**:
  - **Flashing (500ms interval)**: Waiting for desktop app connection
  - **Steady ON**: Connected to desktop app and ready
  - **Boot sequence**: 3 quick flashes on startup
  - **Error indication**: Rapid flashing (6 times) for errors

### 5. Device Detection & Auto-Connection
- **Must** respond to "ping" commands with device identification
- **Must** include "ESP32" or "octane" in ping responses for auto-detection
- **Must** support connection status tracking
- **Must** handle connection timeouts gracefully

## Technical Implementation Details

### USB HID Report Descriptor
Based on the reference implementation, use this HID descriptor:
```c
uint8_t const desc_hid_report[] = {
  0x05, 0x01, 0x09, 0x02, 0xA1, 0x01, 0x09, 0x01, 0xA1, 0x00,
  0x05, 0x09, 0x19, 0x01, 0x29, 0x03, 0x15, 0x00, 0x25, 0x01,
  0x95, 0x03, 0x75, 0x01, 0x81, 0x02, 0x95, 0x01, 0x75, 0x05,
  0x81, 0x03, 0x05, 0x01, 0x09, 0x30, 0x09, 0x31, 0x15, 0x81,
  0x25, 0x7F, 0x75, 0x08, 0x95, 0x02, 0x81, 0x06, 0xC0, 0xC0
};
```

### HID Report Structure
```c
struct HidReport {
    uint8_t buttons;   // Bit 0: Left, Bit 1: Right, Bit 2: Middle
    int8_t x;          // X movement (-127 to 127)
    int8_t y;          // Y movement (-127 to 127)
};
```

### Command Processing
- Parse JSON commands from serial input
- Extract movement deltas and button states
- Queue HID reports for smooth execution
- Maintain 1ms minimum interval between HID reports

## Recoil Compensation Requirements

### Movement Precision
- **Must** support sub-pixel precision movements
- **Must** handle rapid movement sequences (recoil patterns)
- **Must** maintain smooth movement without stuttering
- **Movement Range**: -127 to +127 pixels per command
- **Frequency**: Up to 1000 commands per second

### Pattern Execution
- **Must** execute recoil compensation patterns accurately
- **Must** support weapon-specific timing (RPM-based)
- **Must** handle simultaneous movement and clicking
- **Must** maintain timing precision for different weapons

## Error Handling & Reliability

### Error Conditions to Handle
- USB disconnection/reconnection
- Serial communication errors
- Command queue overflow
- Invalid command formats
- HID device not ready

### Recovery Mechanisms
- Automatic USB re-initialization
- Command queue flushing on errors
- LED error indication
- Serial error reporting to desktop app

## Performance Requirements

### Timing Constraints
- **HID Report Interval**: 1ms maximum
- **Serial Response Time**: <10ms for status commands
- **Command Queue Processing**: Real-time, no blocking
- **USB Polling Rate**: 1000Hz (1ms) preferred

### Memory Usage
- **Command Queue**: ~2KB (64 commands × 32 bytes)
- **Serial Buffer**: 1KB minimum
- **Total RAM Usage**: <50KB (plenty available on ESP32-S2)

## Development Environment

### Recommended Approach
1. **Arduino IDE with ESP32-S2 support** (easiest for HID)
2. **ESP-IDF** (more control, but complex USB setup)
3. **PlatformIO with Arduino framework** (good balance)

### Required Libraries
- **Arduino**: `USB.h`, `Mouse.h` (built-in ESP32-S2 support)
- **ESP-IDF**: TinyUSB component for HID functionality
- **JSON**: ArduinoJson library for command parsing

### Build Configuration
- **Target**: ESP32-S2
- **USB Mode**: Native USB (not CDC)
- **Partition Scheme**: Default (sufficient for firmware)
- **Flash Size**: 4MB
- **PSRAM**: Enabled

## Testing & Validation

### Functional Tests
- [ ] USB HID mouse recognition in Windows
- [ ] Mouse movement accuracy and smoothness
- [ ] Button click functionality
- [ ] Serial command processing
- [ ] LED status indication
- [ ] Auto-detection by desktop app

### Performance Tests
- [ ] High-frequency movement handling (1000Hz)
- [ ] Command queue stress testing
- [ ] Extended operation stability
- [ ] Memory usage validation
- [ ] USB reconnection handling

### Integration Tests
- [ ] Desktop app communication
- [ ] Recoil pattern execution
- [ ] Multi-weapon switching
- [ ] Error recovery scenarios

## Known Issues & Limitations

### PlatformIO Issues (Previous Attempts)
- **TinyUSB Component Missing**: ESP-IDF framework in PlatformIO may lack TinyUSB
- **Version Conflicts**: ESP-IDF versions may have incompatible TinyUSB
- **Build Complexity**: ESP-IDF requires complex CMake configuration

### Recommended Solution
Use **Arduino IDE** or **PlatformIO with Arduino framework** instead of ESP-IDF for simpler USB HID implementation.

### ESP32-S2 Specific Considerations
- Native USB support eliminates need for external USB chips
- GPIO15 LED is built-in on ESP32-S2 Mini
- USB and Serial can conflict - use proper USB mode
- Requires proper USB descriptors for HID functionality

## Next Steps

1. **Set up Arduino IDE** with ESP32-S2 board support
2. **Create basic HID mouse firmware** with USB descriptors
3. **Implement serial command parsing** with JSON support
4. **Add command queue system** for smooth operation
5. **Test with desktop app** for integration
6. **Optimize performance** for recoil compensation
7. **Add error handling** and recovery mechanisms
8. **Create production build** with proper versioning

## References
- Desktop app ESP32Service.cs for communication protocol
- Reference firmware implementations in BAK folder
- Useful post info.txt for HID implementation details
- ESP32-S2 datasheet for hardware specifications
