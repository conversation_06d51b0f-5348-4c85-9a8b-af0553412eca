<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Key Maintenance - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-key"></i> Key Maintenance</h1>
                <p>Create, manage, and monitor license keys</p>
            </div>

            <div class="dashboard-grid">
                <!-- Create New License -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus-circle"></i> Create New License</h3>
                    </div>
                    <div class="card-content">
                        <form id="create-license-form">
                            <div class="form-group">
                                <label for="license-duration">Duration:</label>
                                <select id="license-duration" required>
                                    <option value="1day">1 Day</option>
                                    <option value="1week">1 Week</option>
                                    <option value="1month" selected>1 Month</option>
                                    <option value="3months">3 Months</option>
                                    <option value="6months">6 Months</option>
                                    <option value="1year">1 Year</option>
                                    <option value="lifetime">Lifetime</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="license-notes">Notes (Optional):</label>
                                <input type="text" id="license-notes" placeholder="Customer info, purpose, etc.">
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> Generate License
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Generated License Display -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clipboard"></i> Generated License</h3>
                    </div>
                    <div class="card-content">
                        <div id="generated-license" class="license-display" style="display: none;">
                            <div class="license-key-container">
                                <input type="text" id="license-key-display" readonly>
                                <button id="copy-license" class="btn btn-secondary">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                            <div class="license-info">
                                <p><strong>Duration:</strong> <span id="license-duration-display"></span></p>
                                <p><strong>Expires:</strong> <span id="license-expires-display"></span></p>
                                <p><strong>Created:</strong> <span id="license-created-display"></span></p>
                            </div>
                        </div>
                        <div id="no-license" class="empty-state">
                            <i class="fas fa-key"></i>
                            <p>No license generated yet</p>
                            <p>Create a new license to see it here</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-chart-bar"></i> License Statistics</h3>
                    </div>
                    <div class="card-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value" id="total-licenses">0</div>
                                <div class="stat-label">Total Licenses</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="active-licenses">0</div>
                                <div class="stat-label">Active</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="expired-licenses">0</div>
                                <div class="stat-label">Expired</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value" id="today-created">0</div>
                                <div class="stat-label">Created Today</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Licenses -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Recent Licenses</h3>
                        <button id="refresh-licenses" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="table-container">
                            <table id="licenses-table">
                                <thead>
                                    <tr>
                                        <th>License Key</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Expires</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="licenses-tbody">
                                    <tr>
                                        <td colspan="7" class="loading">Loading licenses...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script src="js/admin-modern.js"></script>
    <script>
        // Initialize shared components
        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
        });
    </script>
    <script>
        // Key Maintenance specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadLicenseStats();
            loadRecentLicenses();
            
            // Create license form
            document.getElementById('create-license-form').addEventListener('submit', createLicense);
            
            // Copy license button
            document.getElementById('copy-license').addEventListener('click', copyLicense);
            
            // Refresh button
            document.getElementById('refresh-licenses').addEventListener('click', loadRecentLicenses);
        });

        async function createLicense(event) {
            event.preventDefault();
            
            const duration = document.getElementById('license-duration').value;
            const notes = document.getElementById('license-notes').value;
            
            try {
                const response = await fetch('/api/admin/licenses', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ duration, notes })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayGeneratedLicense(result.license);
                    loadLicenseStats();
                    loadRecentLicenses();
                    document.getElementById('license-notes').value = '';
                    showNotification('License created successfully', 'success');
                } else {
                    showNotification('Failed to create license: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error creating license: ' + error.message, 'error');
            }
        }

        function displayGeneratedLicense(license) {
            document.getElementById('no-license').style.display = 'none';
            document.getElementById('generated-license').style.display = 'block';
            
            document.getElementById('license-key-display').value = license.key;
            document.getElementById('license-duration-display').textContent = license.duration;
            document.getElementById('license-expires-display').textContent = new Date(license.expires_at).toLocaleString();
            document.getElementById('license-created-display').textContent = new Date(license.created_at).toLocaleString();
        }

        function copyLicense() {
            const licenseKey = document.getElementById('license-key-display');
            licenseKey.select();
            document.execCommand('copy');
            showNotification('License key copied to clipboard', 'success');
        }

        async function loadLicenseStats() {
            try {
                const [totalRes, activeRes] = await Promise.all([
                    fetch('/api/licenses/count'),
                    fetch('/api/users/active')
                ]);
                
                const total = await totalRes.json();
                const active = await activeRes.json();
                
                document.getElementById('total-licenses').textContent = total.count || 0;
                document.getElementById('active-licenses').textContent = active.count || 0;
                document.getElementById('expired-licenses').textContent = Math.max(0, (total.count || 0) - (active.count || 0));
                
                // Mock today's created count
                document.getElementById('today-created').textContent = Math.floor(Math.random() * 5);
            } catch (error) {
                console.error('Error loading license stats:', error);
            }
        }

        async function loadRecentLicenses() {
            try {
                const response = await fetch('/api/admin/licenses?limit=10');
                const result = await response.json();
                
                const tbody = document.getElementById('licenses-tbody');
                
                if (result.success && result.licenses.length > 0) {
                    tbody.innerHTML = result.licenses.map(license => `
                        <tr>
                            <td><code>${license.key}</code></td>
                            <td>${license.duration}</td>
                            <td><span class="status ${license.is_active ? 'active' : 'inactive'}">${license.is_active ? 'Active' : 'Inactive'}</span></td>
                            <td>${new Date(license.created_at).toLocaleDateString()}</td>
                            <td>${license.expires_at ? new Date(license.expires_at).toLocaleDateString() : 'Never'}</td>
                            <td>${license.notes || '-'}</td>
                            <td>
                                <button class="btn btn-sm btn-danger" onclick="deleteLicense('${license.key}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" class="empty">No licenses found</td></tr>';
                }
            } catch (error) {
                console.error('Error loading licenses:', error);
                document.getElementById('licenses-tbody').innerHTML = '<tr><td colspan="7" class="error">Error loading licenses</td></tr>';
            }
        }

        async function deleteLicense(key) {
            if (!confirm('Are you sure you want to delete this license?')) return;
            
            try {
                const response = await fetch(`/api/admin/licenses/${key}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('License deleted successfully', 'success');
                    loadRecentLicenses();
                    loadLicenseStats();
                } else {
                    showNotification('Failed to delete license: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error deleting license: ' + error.message, 'error');
            }
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4757' : '#4a90e2'};
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
