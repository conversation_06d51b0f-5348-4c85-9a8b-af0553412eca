﻿#pragma checksum "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A6ADDAEDAC11223787BDDAC4117791B303865A51"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.UI.Controls {
    
    
    /// <summary>
    /// RecoilSettingsControl
    /// </summary>
    public partial class RecoilSettingsControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 66 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RecoilToggle;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HipfireToggle;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HumanisationToggle;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RandomisationToggle;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RapidfireToggle;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip SensitivityValue;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip FovValue;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FovSlider;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip HorizontalValue;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalSlider;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip VerticalValue;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalSlider;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip DelayValue;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider DelaySlider;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AntiAfkToggle;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AfkSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AfkIntervalText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CrouchKeyText;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CodelockToggle;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CodelockSettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodelockCodeText;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnterCodeButton;
        
        #line default
        #line hidden
        
        
        #line 339 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CursorCheckToggle;
        
        #line default
        #line hidden
        
        
        #line 358 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetToDefaultsButton;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadConfigButton;
        
        #line default
        #line hidden
        
        
        #line 367 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportConfigButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;V1.0.0.0;component/ui/controls/recoilsettingscontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\UI\Controls\RecoilSettingsControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RecoilToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 2:
            this.HipfireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 3:
            this.HumanisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 4:
            this.RandomisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 5:
            this.RapidfireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 6:
            this.SensitivityValue = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 7:
            this.SensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 8:
            this.FovValue = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 9:
            this.FovSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 10:
            this.HorizontalValue = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 11:
            this.HorizontalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.VerticalValue = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 13:
            this.VerticalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 14:
            this.DelayValue = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 15:
            this.DelaySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.AntiAfkToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 17:
            this.AfkSettingsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 18:
            this.AfkIntervalText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.CrouchKeyText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.CodelockToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 21:
            this.CodelockSettingsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.CodelockCodeText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.EnterCodeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 24:
            this.CursorCheckToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 25:
            this.ResetToDefaultsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 26:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 27:
            this.LoadConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 28:
            this.ExportConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

