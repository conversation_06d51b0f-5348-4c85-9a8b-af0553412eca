module.exports = {
  apps: [{
    name: 'octane-auth',
    script: 'server.js',
    cwd: '/opt/octane-auth',
    instances: 1, // Single instance for 1GB RAM
    exec_mode: 'fork', // Fork mode uses less memory than cluster

    // Memory optimization
    max_memory_restart: '400M', // Restart if memory exceeds 400MB
    node_args: [
      '--max-old-space-size=384', // Limit Node.js heap to 384MB
      '--optimize-for-size', // Optimize for memory usage over speed
      '--gc-interval=100', // More frequent garbage collection
      '--max-semi-space-size=64' // Limit semi-space to 64MB
    ],

    // Environment
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      UV_THREADPOOL_SIZE: 2 // Reduce thread pool size
    },

    // Logging (optimized for low disk usage)
    log_file: '/opt/octane-auth/.pm2/logs/octane-auth.log',
    out_file: '/opt/octane-auth/.pm2/logs/octane-auth-out.log',
    error_file: '/opt/octane-auth/.pm2/logs/octane-auth-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    max_log_size: '10M', // Limit log files to 10MB
    max_log_files: 3, // Keep only 3 log files

    // Process management
    autorestart: true,
    watch: false, // Disable watching to save memory
    ignore_watch: ['node_modules', '.pm2', 'logs'],

    // Restart conditions
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,

    // Health monitoring
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true,

    // Performance monitoring
    pmx: false, // Disable PMX to save memory

    // Advanced PM2 features (disabled for memory saving)
    vizion: false,
    automation: false,

    // Kill timeout
    kill_timeout: 5000,

    // Merge logs
    merge_logs: true,

    // Time zone
    time: true
  }, {
    name: 'discord-bot',
    script: 'services/discord.js',
    cwd: '/opt/octane-auth',
    instances: 1,
    exec_mode: 'fork',

    // Memory optimization for Discord bot
    max_memory_restart: '80M', // Restart if memory exceeds 80MB
    node_args: [
      '--max-old-space-size=64', // Limit Node.js heap to 64MB
      '--optimize-for-size',
      '--gc-interval=50',
      '--max-semi-space-size=16',
      '--expose-gc' // Enable manual garbage collection
    ],

    // Environment
    env: {
      NODE_ENV: 'production',
      UV_THREADPOOL_SIZE: 1
    },

    // Logging
    log_file: '/opt/octane-auth/.pm2/logs/discord-bot.log',
    out_file: '/opt/octane-auth/.pm2/logs/discord-bot-out.log',
    error_file: '/opt/octane-auth/.pm2/logs/discord-bot-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    max_log_size: '5M',
    max_log_files: 2,

    // Process management
    autorestart: true,
    watch: false,
    ignore_watch: ['node_modules', '.pm2', 'logs'],

    // Restart conditions
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 5000,

    // Health monitoring
    health_check_grace_period: 5000,
    health_check_fatal_exceptions: true,

    // Performance monitoring
    pmx: false,

    // Advanced PM2 features (disabled for memory saving)
    vizion: false,
    automation: false,

    // Kill timeout
    kill_timeout: 5000,

    // Merge logs
    merge_logs: true,

    // Time zone
    time: true
  }]
};
