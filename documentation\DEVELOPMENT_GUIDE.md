# ESP32-S2 HID Mouse Firmware Development Guide

## Quick Start

### Prerequisites
1. **Arduino IDE 2.x** or **PlatformIO**
2. **ESP32-S2 board support** installed
3. **ESP32-S2 Mini** hardware
4. **USB cable** (data capable)

### Recommended Development Environment
**Arduino IDE** is recommended for this project due to:
- Built-in ESP32-S2 USB HID support
- Simpler USB configuration
- Better library compatibility
- Easier debugging

## Arduino IDE Setup

### 1. Install ESP32 Board Support
1. Open Arduino IDE
2. Go to **File → Preferences**
3. Add to **Additional Board Manager URLs**:
   ```
   https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
   ```
4. Go to **Tools → Board → Boards Manager**
5. Search for "ESP32" and install **esp32 by Espressif Systems**

### 2. Board Configuration
- **Board**: "ESP32S2 Dev Module"
- **Upload Speed**: 921600
- **CPU Frequency**: 240MHz
- **Flash Size**: 4MB
- **Partition Scheme**: "Default 4MB with spiffs"
- **PSRAM**: "Enabled"
- **USB Mode**: "Hardware CDC and JTAG"

### 3. Required Libraries
Install via **Library Manager**:
- **ArduinoJson** (for command parsing)
- **ESP32-USB-Soft-Host** (if needed for advanced USB features)

## Project Structure

```
esp32-firmware/
├── octane_hid_mouse.ino          # Main firmware file
├── config.h                      # Configuration constants
├── hid_mouse.h                   # HID mouse implementation
├── serial_protocol.h             # Serial communication
├── command_queue.h               # Command queue system
├── led_controller.h              # LED status management
└── README.md                     # Firmware documentation
```

## Core Implementation

### 1. Main Firmware Structure
```cpp
#include "USB.h"
#include "USBHIDMouse.h"
#include <ArduinoJson.h>

// Configuration
#define SERIAL_BAUD 115200
#define LED_PIN 15
#define QUEUE_SIZE 64

// Global objects
USBHIDMouse Mouse;
QueueHandle_t commandQueue;

void setup() {
    // Initialize serial
    Serial.begin(SERIAL_BAUD);
    
    // Initialize USB HID
    Mouse.begin();
    USB.begin();
    
    // Initialize LED
    pinMode(LED_PIN, OUTPUT);
    
    // Create command queue
    commandQueue = xQueueCreate(QUEUE_SIZE, sizeof(HIDCommand));
    
    // Boot sequence
    bootSequence();
    
    Serial.println("Octane ESP32-S2 HID Mouse v2.0.0 Ready");
}

void loop() {
    processSerialCommands();
    processCommandQueue();
    updateLEDStatus();
    delay(1);
}
```

### 2. HID Command Structure
```cpp
struct HIDCommand {
    uint8_t type;      // 0=move, 1=click_down, 2=click_up
    int8_t x;          // X movement
    int8_t y;          // Y movement
    uint8_t buttons;   // Button state
    uint32_t timestamp; // For timing
};
```

### 3. Command Processing
```cpp
void processSerialCommands() {
    if (Serial.available()) {
        String command = Serial.readStringUntil('\n');
        command.trim();
        
        // Try JSON first
        if (command.startsWith("{")) {
            processJSONCommand(command);
        } else {
            // Fallback to simple commands
            processSimpleCommand(command);
        }
    }
}

void processJSONCommand(String jsonStr) {
    StaticJsonDocument<512> doc;
    DeserializationError error = deserializeJson(doc, jsonStr);
    
    if (error) {
        sendError("Invalid JSON format");
        return;
    }
    
    String type = doc["Type"];
    JsonObject data = doc["Data"];
    
    if (type == "mouse_move") {
        int x = data["x"];
        int y = data["y"];
        queueMouseMove(x, y);
        sendResponse("moved");
    }
    // ... handle other commands
}
```

### 4. Command Queue Implementation
```cpp
bool queueMouseMove(int8_t x, int8_t y) {
    HIDCommand cmd = {0, x, y, 0, millis()};
    return xQueueSend(commandQueue, &cmd, 0) == pdTRUE;
}

void processCommandQueue() {
    static uint32_t lastProcessTime = 0;
    
    // Process at 1ms intervals
    if (millis() - lastProcessTime >= 1) {
        HIDCommand cmd;
        if (xQueueReceive(commandQueue, &cmd, 0) == pdTRUE) {
            executeHIDCommand(cmd);
            lastProcessTime = millis();
        }
    }
}

void executeHIDCommand(HIDCommand cmd) {
    switch (cmd.type) {
        case 0: // Mouse move
            Mouse.move(cmd.x, cmd.y);
            break;
        case 1: // Click down
            Mouse.press(cmd.buttons);
            break;
        case 2: // Click up
            Mouse.release(cmd.buttons);
            break;
    }
}
```

## Why PlatformIO Failed

### Issues Encountered
1. **TinyUSB Component Missing**: ESP-IDF in PlatformIO lacks proper TinyUSB integration
2. **CMake Configuration Complexity**: ESP-IDF requires complex build configuration
3. **Version Conflicts**: Different ESP-IDF versions have incompatible USB implementations
4. **Missing Dependencies**: Required USB components not automatically included

### Error Analysis
```
CMake Error: Failed to resolve component 'tinyusb' required by component 'main'
```
This error occurred because:
- PlatformIO's ESP-IDF doesn't include TinyUSB by default
- Manual TinyUSB integration is complex and error-prone
- ESP32-S2 USB support varies between ESP-IDF versions

### Arduino vs ESP-IDF Comparison
| Feature | Arduino | ESP-IDF |
|---------|---------|---------|
| USB HID Setup | Simple (`Mouse.begin()`) | Complex (TinyUSB config) |
| Library Support | Excellent | Limited |
| Development Speed | Fast | Slow |
| Debugging | Easy | Complex |
| Documentation | Good | Technical |

## Testing Strategy

### 1. Hardware Testing
```cpp
void testHardware() {
    // Test LED
    digitalWrite(LED_PIN, HIGH);
    delay(500);
    digitalWrite(LED_PIN, LOW);
    
    // Test USB HID
    Mouse.move(10, 0);
    delay(100);
    Mouse.move(-10, 0);
    
    // Test Serial
    Serial.println("Hardware test complete");
}
```

### 2. Communication Testing
```cpp
void testCommunication() {
    // Test JSON parsing
    String testJSON = "{\"Type\":\"ping\",\"Data\":{}}";
    processJSONCommand(testJSON);
    
    // Test simple commands
    processSimpleCommand("PING");
    processSimpleCommand("M5,10");
}
```

### 3. Performance Testing
```cpp
void testPerformance() {
    uint32_t startTime = millis();
    
    // Queue 100 commands
    for (int i = 0; i < 100; i++) {
        queueMouseMove(1, 1);
    }
    
    // Measure processing time
    while (uxQueueMessagesWaiting(commandQueue) > 0) {
        processCommandQueue();
        delay(1);
    }
    
    uint32_t endTime = millis();
    Serial.printf("Processed 100 commands in %dms\n", endTime - startTime);
}
```

## Debugging Tips

### 1. Serial Debugging
```cpp
#define DEBUG_ENABLED 1

void debugPrint(String message) {
    #if DEBUG_ENABLED
    Serial.println("[DEBUG] " + message);
    #endif
}
```

### 2. LED Debug Codes
```cpp
void debugLED(int flashes) {
    for (int i = 0; i < flashes; i++) {
        digitalWrite(LED_PIN, HIGH);
        delay(200);
        digitalWrite(LED_PIN, LOW);
        delay(200);
    }
}
```

### 3. Memory Monitoring
```cpp
void printMemoryUsage() {
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("Queue items: %d\n", uxQueueMessagesWaiting(commandQueue));
}
```

## Common Issues & Solutions

### 1. USB Not Recognized
**Problem**: ESP32 not appearing as HID mouse
**Solution**: 
- Check USB cable (must support data)
- Verify board configuration
- Ensure `USB.begin()` is called

### 2. Serial Communication Fails
**Problem**: Desktop app can't connect
**Solution**:
- Check baud rate (115200)
- Verify COM port in Device Manager
- Test with Arduino Serial Monitor

### 3. Mouse Movement Stuttering
**Problem**: Jerky mouse movement
**Solution**:
- Implement command queue
- Maintain 1ms intervals
- Check for USB HID ready state

### 4. Memory Issues
**Problem**: Firmware crashes or freezes
**Solution**:
- Monitor heap usage
- Limit queue size
- Use static allocation where possible

## Performance Optimization

### 1. Timing Optimization
```cpp
// Use hardware timers for precise timing
hw_timer_t * timer = NULL;

void IRAM_ATTR onTimer() {
    processCommandQueue();
}

void setupTimer() {
    timer = timerBegin(0, 80, true);
    timerAttachInterrupt(timer, &onTimer, true);
    timerAlarmWrite(timer, 1000, true); // 1ms
    timerAlarmEnable(timer);
}
```

### 2. Memory Optimization
```cpp
// Use static buffers
static char serialBuffer[256];
static StaticJsonDocument<512> jsonDoc;

// Avoid String class for performance-critical code
void fastStringCopy(char* dest, const char* src, size_t maxLen) {
    strncpy(dest, src, maxLen - 1);
    dest[maxLen - 1] = '\0';
}
```

## Production Considerations

### 1. Error Recovery
- Implement watchdog timer
- Handle USB disconnection
- Automatic queue cleanup

### 2. Security
- Validate all input commands
- Implement rate limiting
- Add authentication support

### 3. Versioning
- Include firmware version in responses
- Support version checking
- Implement update mechanism

## Next Steps

1. **Create basic firmware** with Arduino IDE
2. **Test USB HID functionality** with simple movements
3. **Implement serial protocol** with JSON support
4. **Add command queue system** for smooth operation
5. **Test with desktop app** for full integration
6. **Optimize performance** for recoil compensation
7. **Add production features** (error handling, security)
8. **Create release build** with proper versioning
