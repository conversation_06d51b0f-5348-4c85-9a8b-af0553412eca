{"runtimeOptions": {"tfm": "net6.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "6.0.36"}, {"name": "Microsoft.WindowsDesktop.App", "version": "6.0.36"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"Microsoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmability": true, "System.ComponentModel.TypeConverter.EnableUnsafeBinaryFormatterInDesigntimeLicenseContextSerialization": false, "System.Resources.ResourceManager.AllowCustomResourceTypes": false, "System.Runtime.InteropServices.BuiltInComInterop.IsSupported": false, "System.Runtime.InteropServices.EnableConsumingManagedCodeFromNativeHosting": false, "System.Runtime.InteropServices.EnableCppCLIHostActivation": false, "System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": false, "System.StartupHookProvider.IsSupported": false, "System.Text.Encoding.EnableUnsafeUTF7Encoding": false, "System.Threading.Thread.EnableAutoreleasePool": false, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}