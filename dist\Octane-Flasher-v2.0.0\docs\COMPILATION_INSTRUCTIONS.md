# ESP32-S2 HID Mouse Firmware Compilation Instructions (CMD Only - No Arduino IDE)

## Method 1: ESP-IDF Command Line (Recommended)

### 1. Install ESP-IDF

Download and install ESP-IDF from: https://docs.espressif.com/projects/esp-idf/en/latest/esp32s2/get-started/windows-setup.html

**Quick Install:**
1. Download ESP-IDF Windows Installer: https://dl.espressif.com/dl/esp-idf/
2. Run installer and follow setup wizard
3. This installs Python, Git, CMake, and ESP-IDF tools automatically

### 2. Setup Environment

Open "ESP-IDF Command Prompt" (installed with ESP-IDF) or run:
```cmd
%USERPROFILE%\esp\esp-idf\export.bat
```

### 3. Build Firmware

Navigate to firmware directory and build:
```cmd
cd C:\Users\<USER>\Desktop\recoil\esp32-flasher\esp32-firmware
idf.py set-target esp32s2
idf.py build
```

### 4. Copy Firmware Files

After successful build, copy these files to OctaneFlasher directory:
```cmd
copy build\bootloader\bootloader.bin ..\OctaneFlasher\bootloader.bin
copy build\partition_table\partition-table.bin ..\OctaneFlasher\partitions.bin
copy build\octane_hid_firmware.bin ..\OctaneFlasher\octane_auth_firmware.bin
```

## Method 2: Pre-compiled Binaries (If ESP-IDF fails)

If ESP-IDF installation is problematic, I can provide pre-compiled binaries.

## Method 3: Docker (Alternative)

Use ESP-IDF Docker container:
```cmd
docker run --rm -v %cd%:/project -w /project espressif/idf:latest idf.py build
```

## Testing

After flashing with OctaneFlasher:
1. **LED Status**: GPIO15 LED flashes when disconnected, steady when connected to desktop app
2. **Serial output**: Connect to serial monitor at 921600 baud
3. **Boot confirmation**: 3 quick flashes on startup
4. **HID Mouse**: ESP32 appears as USB HID mouse device in Device Manager
5. **Commands**: Send "PING" via serial, should respond "PONG"
6. **Movement**: Send "M10,5" for mouse movement (x=10, y=5)
7. **Clicks**: Send "CLICK_LEFT_DOWN" and "CLICK_LEFT_UP" for mouse clicks

## Troubleshooting

**If build fails:**
1. Make sure ESP-IDF Command Prompt is used
2. Check that `IDF_PATH` environment variable is set
3. Try: `idf.py clean` then `idf.py build`

**If ESP-IDF installation is too complex:**
- Let me know and I'll provide pre-compiled firmware binaries
- Or we can use a simpler approach with esptool directly

This HID mouse firmware is compiled with ESP-IDF (professional ESP32 development framework) and doesn't require Arduino IDE.

## Firmware Features

- **USB HID Mouse**: Acts as a real USB mouse device
- **Serial Commands**: Receives movement and click commands from desktop app
- **Command Queue**: Prevents HID saturation with efficient queuing system
- **LED Status**: Visual indication of connection status
- **Error Reporting**: Sends errors back to desktop app for Discord webhooks
- **High Performance**: 921600 baud serial, 1ms HID report intervals
