@echo off
setlocal enabledelayedexpansion

echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🧪 OCTANE FLASHER TESTER 🧪                    ║
echo ║                      by Octane Team                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

echo 🔍 Starting comprehensive flasher testing...
echo.

REM Test 1: Flasher Executable Validation
echo ═══════════════════════════════════════════════════════════════
echo TEST 1: Flasher Executable Validation
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

if exist "OctaneFlasher\OctaneFlasher.exe" (
    for %%F in ("OctaneFlasher\OctaneFlasher.exe") do (
        if %%~zF GTR 500000 (
            echo ✅ OctaneFlasher.exe found and valid size: %%~zF bytes
            set /a PASSED_TESTS+=1
            echo ✅ TEST 1 PASSED: Flasher executable valid
        ) else (
            echo ❌ OctaneFlasher.exe too small: %%~zF bytes
            set /a FAILED_TESTS+=1
            echo ❌ TEST 1 FAILED: Flasher executable invalid size
        )
    )
) else (
    echo ❌ OctaneFlasher.exe not found
    echo 🔧 Build the flasher first: cd OctaneFlasher ^&^& dotnet publish -c Release
    set /a FAILED_TESTS+=1
    echo ❌ TEST 1 FAILED: Flasher executable missing
)

echo.

REM Test 2: Required Firmware Files Validation
echo ═══════════════════════════════════════════════════════════════
echo TEST 2: Required Firmware Files Validation
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

set FIRMWARE_FILES_OK=1

REM Check firmware files
set "REQUIRED_FILES=octane_auth_firmware.bin bootloader.bin partitions.bin"

for %%F in (%REQUIRED_FILES%) do (
    if exist "OctaneFlasher\%%F" (
        for %%S in ("OctaneFlasher\%%F") do (
            echo ✅ %%F found: %%~zS bytes
        )
    ) else (
        echo ❌ %%F missing
        set FIRMWARE_FILES_OK=0
    )
)

if %FIRMWARE_FILES_OK% EQU 1 (
    set /a PASSED_TESTS+=1
    echo ✅ TEST 2 PASSED: All firmware files present
) else (
    set /a FAILED_TESTS+=1
    echo ❌ TEST 2 FAILED: Missing firmware files
    echo 🔧 Build firmware first: cd esp32-firmware ^&^& build.bat
)

echo.

REM Test 3: Flasher Help/Version Test
echo ═══════════════════════════════════════════════════════════════
echo TEST 3: Flasher Help/Version Test
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

if exist "OctaneFlasher\OctaneFlasher.exe" (
    echo 🔧 Testing flasher help output...
    
    REM Test help command
    cd OctaneFlasher
    echo help | OctaneFlasher.exe >flasher_help.log 2>&1
    
    REM Check if help output contains expected content
    findstr "OCTANE ESP32 FLASHER" flasher_help.log >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Flasher displays proper header
        
        findstr "Version" flasher_help.log >nul
        if %ERRORLEVEL% EQU 0 (
            echo ✅ Version information displayed
            set /a PASSED_TESTS+=1
            echo ✅ TEST 3 PASSED: Flasher help/version working
        ) else (
            echo ❌ Version information missing
            set /a FAILED_TESTS+=1
            echo ❌ TEST 3 FAILED: Version info missing
        )
    ) else (
        echo ❌ Flasher header not displayed properly
        set /a FAILED_TESTS+=1
        echo ❌ TEST 3 FAILED: Flasher output invalid
    )
    
    cd ..
) else (
    echo ❌ Cannot test - flasher executable missing
    set /a FAILED_TESTS+=1
    echo ❌ TEST 3 FAILED: No flasher to test
)

echo.

REM Test 4: File Validation Logic Test
echo ═══════════════════════════════════════════════════════════════
echo TEST 4: File Validation Logic Test
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

if exist "OctaneFlasher\OctaneFlasher.exe" (
    echo 🔧 Testing file validation logic...
    
    REM Create temporary dummy files to test validation
    cd OctaneFlasher
    
    REM Backup original files if they exist
    if exist "octane_auth_firmware.bin" ren "octane_auth_firmware.bin" "octane_auth_firmware.bin.backup"
    if exist "bootloader.bin" ren "bootloader.bin" "bootloader.bin.backup"
    if exist "partitions.bin" ren "partitions.bin" "partitions.bin.backup"
    
    REM Create dummy files that are too small
    echo dummy > octane_auth_firmware.bin
    echo dummy > bootloader.bin
    echo dummy > partitions.bin
    
    REM Test with dummy files (should fail validation)
    echo exit | OctaneFlasher.exe >validation_test.log 2>&1
    
    findstr "TOO SMALL\|too small\|invalid" validation_test.log >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ File validation correctly detects invalid files
        set VALIDATION_OK=1
    ) else (
        echo ❌ File validation not working properly
        set VALIDATION_OK=0
    )
    
    REM Clean up dummy files
    del octane_auth_firmware.bin bootloader.bin partitions.bin
    
    REM Restore original files
    if exist "octane_auth_firmware.bin.backup" ren "octane_auth_firmware.bin.backup" "octane_auth_firmware.bin"
    if exist "bootloader.bin.backup" ren "bootloader.bin.backup" "bootloader.bin"
    if exist "partitions.bin.backup" ren "partitions.bin.backup" "partitions.bin"
    
    if %VALIDATION_OK% EQU 1 (
        set /a PASSED_TESTS+=1
        echo ✅ TEST 4 PASSED: File validation working correctly
    ) else (
        set /a FAILED_TESTS+=1
        echo ❌ TEST 4 FAILED: File validation not working
    )
    
    cd ..
) else (
    echo ❌ Cannot test - flasher executable missing
    set /a FAILED_TESTS+=1
    echo ❌ TEST 4 FAILED: No flasher to test
)

echo.

REM Test 5: COM Port Detection Test
echo ═══════════════════════════════════════════════════════════════
echo TEST 5: COM Port Detection Test
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

echo 🔧 Testing COM port detection...

REM Check if any COM ports are available
wmic path Win32_SerialPort get DeviceID >com_ports.txt 2>nul

findstr "COM" com_ports.txt >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ COM ports detected on system:
    type com_ports.txt | findstr "COM"
    
    REM Test flasher COM port detection (non-interactive)
    if exist "OctaneFlasher\OctaneFlasher.exe" (
        cd OctaneFlasher
        REM This would require modifying the flasher to have a test mode
        REM For now, we'll just check that it doesn't crash immediately
        timeout 3 /nobreak >nul 2>&1 & echo exit | OctaneFlasher.exe >com_test.log 2>&1
        
        REM Check if flasher ran without immediate crash
        if exist "com_test.log" (
            echo ✅ Flasher runs without immediate crash
            set /a PASSED_TESTS+=1
            echo ✅ TEST 5 PASSED: COM port detection functional
        ) else (
            echo ❌ Flasher crashed immediately
            set /a FAILED_TESTS+=1
            echo ❌ TEST 5 FAILED: Flasher crash on startup
        )
        cd ..
    ) else (
        echo ❌ Cannot test - flasher executable missing
        set /a FAILED_TESTS+=1
        echo ❌ TEST 5 FAILED: No flasher to test
    )
) else (
    echo ⚠️ No COM ports detected on system
    echo ℹ️ This is normal if no serial devices are connected
    set /a PASSED_TESTS+=1
    echo ✅ TEST 5 PASSED: COM port detection test completed
)

del com_ports.txt >nul 2>&1

echo.

REM Test 6: Esptool Availability Test
echo ═══════════════════════════════════════════════════════════════
echo TEST 6: Esptool Availability Test
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

set ESPTOOL_FOUND=0

REM Check for esptool in various locations
if exist "OctaneFlasher\esptool.exe" (
    echo ✅ esptool.exe found in flasher directory
    set ESPTOOL_FOUND=1
)

where esptool >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ esptool found in system PATH
    set ESPTOOL_FOUND=1
)

where esptool.py >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ esptool.py found in system PATH
    set ESPTOOL_FOUND=1
)

if %ESPTOOL_FOUND% EQU 1 (
    set /a PASSED_TESTS+=1
    echo ✅ TEST 6 PASSED: Esptool available for flashing
) else (
    set /a FAILED_TESTS+=1
    echo ❌ TEST 6 FAILED: No esptool found
    echo 🔧 Install ESP-IDF or place esptool.exe in flasher directory
)

echo.

REM Test Summary
echo ═══════════════════════════════════════════════════════════════
echo TEST SUMMARY
echo ═══════════════════════════════════════════════════════════════

echo Total tests: %TOTAL_TESTS%
echo Passed: %PASSED_TESTS%
echo Failed: %FAILED_TESTS%

if %TOTAL_TESTS% GTR 0 (
    set /a SUCCESS_RATE=%PASSED_TESTS%*100/%TOTAL_TESTS%
    echo Success rate: !SUCCESS_RATE!%%
) else (
    echo Success rate: 0%%
)

echo.

if %FAILED_TESTS% EQU 0 (
    echo 🎉 ALL FLASHER TESTS PASSED!
    echo    Flasher application is working correctly
    echo    Ready for ESP32 flashing operations
    echo.
    echo 📋 Manual testing recommendations:
    echo    1. Test with actual ESP32-S2 hardware
    echo    2. Verify license key validation
    echo    3. Test flashing process end-to-end
    echo    4. Verify error reporting functionality
    exit /b 0
) else (
    echo ❌ SOME FLASHER TESTS FAILED
    echo    Please fix the issues before using the flasher
    echo.
    echo 🔧 Common fixes:
    echo    1. Build flasher: cd OctaneFlasher ^&^& dotnet publish -c Release
    echo    2. Build firmware: cd esp32-firmware ^&^& build.bat
    echo    3. Install ESP-IDF or esptool
    echo    4. Check .NET runtime installation
    exit /b 1
)

pause
