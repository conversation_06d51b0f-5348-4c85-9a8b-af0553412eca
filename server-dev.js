const express = require('express');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({ extended: true }));

// CORS middleware
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Request logging
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    console.log(`[${timestamp}] ${req.method} ${req.url} - IP: ${ip}`);
    next();
});

// In-memory storage for development
let licenses = [
    {
        id: 1,
        key: 'TEST-1234-ABCD',
        duration: '1month',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        hardware_id: null,
        notes: 'Test license',
        is_active: 1
    },
    {
        id: 2,
        key: 'DEMO-5678-EFGH',
        duration: '1week',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        hardware_id: 'DEMO-HARDWARE-123',
        notes: 'Demo license',
        is_active: 1
    }
];

let securityEvents = [
    {
        id: 1,
        type: 'license_validation',
        severity: 'info',
        description: 'License validation attempt: TEST-1234...',
        timestamp: new Date().toISOString(),
        ip_address: '127.0.0.1',
        user_agent: 'Test Agent'
    }
];

// Helper functions
function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result.slice(0, 4)}-${result.slice(4, 8)}-${result.slice(8, 12)}`;
}

function calculateExpiryDate(duration) {
    const now = new Date();
    
    switch (duration.toLowerCase()) {
        case '1hour':
            return new Date(now.getTime() + 60 * 60 * 1000);
        case '1day':
            return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        case '1week':
            return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        case '1month':
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        case '3months':
            return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        case '6months':
            return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
        case '1year':
            return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        case 'lifetime':
            return null;
        default:
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1',
        database: 'memory (development)'
    });
});

// License validation endpoint
app.post('/api/validate', (req, res) => {
    try {
        const { licenseKey, hardwareId } = req.body;
        
        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        if (!hardwareId) {
            return res.status(400).json({
                success: false,
                message: 'Hardware ID is required'
            });
        }

        // Find license
        const license = licenses.find(l => l.key === licenseKey && l.is_active);
        
        if (!license) {
            return res.status(401).json({
                success: false,
                message: 'Invalid license key'
            });
        }

        // Check if expired
        if (license.expires_at && new Date(license.expires_at) <= new Date()) {
            return res.status(401).json({
                success: false,
                message: 'License has expired'
            });
        }

        // Check hardware binding
        if (license.hardware_id && license.hardware_id !== hardwareId) {
            return res.status(401).json({
                success: false,
                message: 'License is bound to different hardware'
            });
        }

        // Bind to hardware if not already bound
        if (!license.hardware_id) {
            license.hardware_id = hardwareId;
        }

        const remainingTime = license.expires_at ? 
            Math.max(0, new Date(license.expires_at) - new Date()) : 
            null;

        res.json({
            success: true,
            message: 'License valid',
            remainingTime: remainingTime ? Math.floor(remainingTime / 1000) : null,
            expiresAt: license.expires_at
        });
    } catch (error) {
        console.error('License validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    res.json({
        success: true,
        licenses: licenses
    });
});

app.post('/api/admin/create-license', (req, res) => {
    try {
        const { duration, notes } = req.body;
        
        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);
        
        const newLicense = {
            id: licenses.length + 1,
            key: key,
            duration: duration,
            created_at: new Date().toISOString(),
            expires_at: expiresAt ? expiresAt.toISOString() : null,
            hardware_id: null,
            notes: notes || '',
            is_active: 1
        };
        
        licenses.push(newLicense);

        res.json({
            success: true,
            license: newLicense
        });
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create license'
        });
    }
});

app.post('/api/admin/delete-license', (req, res) => {
    try {
        const { licenseKey } = req.body;
        
        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        const index = licenses.findIndex(l => l.key === licenseKey);
        
        if (index !== -1) {
            licenses.splice(index, 1);
            res.json({
                success: true,
                message: 'License deleted successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License not found'
            });
        }
    } catch (error) {
        console.error('Delete license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete license'
        });
    }
});

app.post('/api/admin/reset-hwid', (req, res) => {
    try {
        const { licenseKey } = req.body;
        
        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        const license = licenses.find(l => l.key === licenseKey);
        
        if (license) {
            license.hardware_id = null;
            res.json({
                success: true,
                message: 'Hardware ID reset successfully'
            });
        } else {
            res.status(404).json({
                success: false,
                message: 'License not found'
            });
        }
    } catch (error) {
        console.error('Reset hardware ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset hardware ID'
        });
    }
});

// Discord management endpoints
app.get('/api/admin/discord-status', (req, res) => {
    res.json({
        success: true,
        status: 'online',
        botName: 'authentication#3048',
        connected: true,
        uptime: process.uptime()
    });
});

app.post('/api/admin/test-discord', (req, res) => {
    console.log('Discord test message sent');
    res.json({
        success: true,
        message: 'Discord test message sent successfully'
    });
});

app.post('/api/admin/send-daily-report', (req, res) => {
    console.log('Daily report generated');
    res.json({
        success: true,
        message: 'Daily report sent to Discord'
    });
});

app.post('/api/admin/restart-discord-bot', (req, res) => {
    res.json({
        success: true,
        message: 'Discord bot restart initiated'
    });
});

app.get('/api/admin/discord-config', (req, res) => {
    res.json({
        success: true,
        token: process.env.DISCORD_TOKEN || 'MTM5ODQ2MDg3NjQwMjU5MzkyNA.GvcFLz.3qJjO1ZPJSaffSweRBde8P4EwS7FIjcCUlbsnM'
    });
});

app.post('/api/admin/update-discord-config', (req, res) => {
    const { token } = req.body;
    
    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    process.env.DISCORD_TOKEN = token;
    
    res.json({
        success: true,
        message: 'Discord config updated successfully'
    });
});

app.post('/api/admin/update-discord-token', (req, res) => {
    const { token } = req.body;
    
    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    process.env.DISCORD_TOKEN = token;
    
    res.json({
        success: true,
        message: 'Discord token updated successfully'
    });
});

// Security endpoints
app.get('/api/security/events', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    const paginatedEvents = securityEvents.slice(offset, offset + limit);

    res.json({
        success: true,
        events: paginatedEvents,
        pagination: {
            page: page,
            limit: limit,
            total: securityEvents.length,
            pages: Math.ceil(securityEvents.length / limit)
        }
    });
});

// Serve static files with proper headers
app.use(express.static(path.join(__dirname, 'public'), {
    setHeaders: (res, path) => {
        if (path.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css');
        }
        if (path.endsWith('.js')) {
            res.setHeader('Content-Type', 'application/javascript');
        }
    }
}));

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'key-maintenance.html'));
});

app.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'reminders.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

app.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-alerts.html'));
});

// Error handling
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: 'Internal server error'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Octane Auth Server (Development) running on port ${PORT}`);
    console.log(`🌐 Admin Panel: http://localhost:${PORT}/admin`);
    console.log(`💾 Database: In-memory (development)`);
    console.log(`🚀 Ready for development`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});
