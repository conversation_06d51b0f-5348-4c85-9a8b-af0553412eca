<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Security Alerts</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>

            <!-- Security Overview -->
            <div class="card">
                <div class="card-header">
                    <h3>🛡️ Security Overview</h3>
                    <button class="btn btn-secondary" onclick="refreshSecurityData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--danger-color);" id="criticalAlerts">0</div>
                            <div style="color: var(--text-secondary);">Critical Alerts</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--warning-color);" id="warningAlerts">0</div>
                            <div style="color: var(--text-secondary);">Warnings</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--info-color);" id="blockedIPs">0</div>
                            <div style="color: var(--text-secondary);">Blocked IPs</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="securityScore">100%</div>
                            <div style="color: var(--text-secondary);">Security Score</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Actions -->
            <div class="card">
                <div class="card-header">
                    <h3>⚡ Quick Actions</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <h4>🚫 Access Control</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-danger" onclick="banIP()">
                                    <i class="fas fa-ban"></i> Ban IP Address
                                </button>
                                <button class="btn btn-warning" onclick="suspendUser()">
                                    <i class="fas fa-user-slash"></i> Suspend User
                                </button>
                                <button class="btn btn-success" onclick="unbanUser()">
                                    <i class="fas fa-user-check"></i> Unban User
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>📊 Monitoring</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-info" onclick="viewLiveLogs()">
                                    <i class="fas fa-eye"></i> Live Security Logs
                                </button>
                                <button class="btn btn-secondary" onclick="exportSecurityReport()">
                                    <i class="fas fa-download"></i> Export Report
                                </button>
                                <button class="btn btn-primary" onclick="runSecurityScan()">
                                    <i class="fas fa-search"></i> Run Security Scan
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>🔧 Maintenance</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-warning" onclick="clearOldLogs()">
                                    <i class="fas fa-trash"></i> Clear Old Logs
                                </button>
                        <button class="btn btn-danger" onclick="deleteAllSecurityEvents()">
                            <i class="fas fa-trash-alt"></i> Delete All Events
                        </button>
                                <button class="btn btn-secondary" onclick="resetSecuritySettings()">
                                    <i class="fas fa-undo"></i> Reset Settings
                                </button>
                                <button class="btn btn-info" onclick="updateSecurityRules()">
                                    <i class="fas fa-shield-alt"></i> Update Rules
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Security Events -->
            <div class="card">
                <div class="card-header">
                    <h3>🚨 Recent Security Events</h3>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <select id="severityFilter" class="form-control" style="max-width: 150px;">
                            <option value="all">All Severity</option>
                            <option value="critical">Critical</option>
                            <option value="warning">Warning</option>
                            <option value="info">Info</option>
                        </select>
                        <select id="typeFilter" class="form-control" style="max-width: 200px;">
                            <option value="all">All Types</option>
                            <option value="invalid_request">Invalid Request</option>
                            <option value="invalid_hwid">Invalid HWID</option>
                            <option value="license_not_found">License Not Found</option>
                            <option value="suspicious_activity">Suspicious Activity</option>
                        </select>
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Severity</th>
                                    <th>Event Type</th>
                                    <th>IP Address</th>
                                    <th>Details</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="securityEventsTable">
                                <tr>
                                    <td colspan="6" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading security events...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Blocked IPs -->
            <div class="card">
                <div class="card-header">
                    <h3>🚫 Blocked IP Addresses</h3>
                    <button class="btn btn-secondary" onclick="refreshBlockedIPs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div id="blockedIPsList" style="max-height: 300px; overflow-y: auto;">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading blocked IPs...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                loadSecurityData();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // Security management functions
        function loadSecurityData() {
            loadSecurityStats();
            loadSecurityEvents();
            loadBlockedIPs();
        }

        function loadSecurityStats() {
            fetch('/api/security/stats')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        document.getElementById('criticalAlerts').textContent = result.critical || 0;
                        document.getElementById('warningAlerts').textContent = result.warnings || 0;
                        document.getElementById('blockedIPs').textContent = result.blockedIPs || 0;
                        document.getElementById('securityScore').textContent = (result.score || 100) + '%';
                    }
                })
                .catch(error => {
                    console.error('Error loading security stats:', error);
                });
        }

        function loadSecurityEvents() {
            fetch('/api/security/events')
                .then(response => response.json())
                .then(result => {
                    const tbody = document.getElementById('securityEventsTable');

                    if (result.success && result.events.length > 0) {
                        tbody.innerHTML = result.events.map(event => `
                            <tr>
                                <td>${OctaneUtils.formatDate(event.timestamp)}</td>
                                <td><span class="severity-badge ${event.severity}">${event.severity.toUpperCase()}</span></td>
                                <td>${event.event_type}</td>
                                <td><code>${event.ip_address}</code></td>
                                <td>${event.details}</td>
                                <td>
                                    <div style="display: flex; gap: 5px;">
                                        <button class="btn btn-sm btn-danger" onclick="banIP('${event.ip_address}')" title="Ban IP">
                                            🚫
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="viewEventDetails('${event.id}')" title="View Details">
                                            👁️
                                        </button>
                                        <button class="btn btn-sm btn-secondary" onclick="dismissEvent('${event.id}')" title="Dismiss">
                                            ✖️
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--text-muted);">No security events found</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading security events:', error);
                    document.getElementById('securityEventsTable').innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading events</td></tr>';
                });
        }

        function loadBlockedIPs() {
            fetch('/api/security/blocked-ips')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('blockedIPsList');

                    if (result.success && result.ips.length > 0) {
                        container.innerHTML = result.ips.map(ip => `
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid var(--border-color);">
                                <div>
                                    <code style="background: var(--bg-secondary); padding: 4px 8px; border-radius: 4px;">${ip.address}</code>
                                    <div style="font-size: 0.8rem; color: var(--text-muted);">Blocked: ${OctaneUtils.formatDate(ip.blocked_at)}</div>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="unbanIP('${ip.address}')" title="Unban IP">
                                    ✅ Unban
                                </button>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-muted);">No blocked IPs</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading blocked IPs:', error);
                    document.getElementById('blockedIPsList').innerHTML = '<div style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading blocked IPs</div>';
                });
        }

        function refreshSecurityData() {
            loadSecurityData();
            OctaneUtils.showAlert('🔄 Security data refreshed', 'info');
        }

        function banIP(ip = null) {
            if (!ip) {
                ip = prompt('Enter IP address to ban:');
                if (!ip) return;
            }

            if (!confirm(`Ban IP address ${ip}?`)) return;

            fetch('/api/security/ban-ip', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ip })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ IP banned successfully', 'success');
                    loadSecurityData();
                } else {
                    OctaneUtils.showAlert('❌ Failed to ban IP: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error banning IP: ' + error.message, 'danger');
            });
        }

        function unbanIP(ip) {
            if (!confirm(`Unban IP address ${ip}?`)) return;

            fetch('/api/security/unban-ip', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ ip })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ IP unbanned successfully', 'success');
                    loadSecurityData();
                } else {
                    OctaneUtils.showAlert('❌ Failed to unban IP: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error unbanning IP: ' + error.message, 'danger');
            });
        }

        function suspendUser() {
            const licenseKey = prompt('Enter license key to suspend:');
            if (!licenseKey) return;

            if (!confirm(`Suspend user with license ${licenseKey}?`)) return;

            fetch('/api/admin/users/' + licenseKey + '/suspend', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ User suspended successfully', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Failed to suspend user: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error suspending user: ' + error.message, 'danger');
                });
        }

        function unbanUser() {
            const licenseKey = prompt('Enter license key to unban:');
            if (!licenseKey) return;

            if (!confirm(`Unban user with license ${licenseKey}?`)) return;

            fetch('/api/admin/users/' + licenseKey + '/unban', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ User unbanned successfully', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Failed to unban user: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error unbanning user: ' + error.message, 'danger');
                });
        }

        function viewLiveLogs() {
            window.open('/api/security/live-logs', '_blank');
        }

        function exportSecurityReport() {
            fetch('/api/security/export-report')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    OctaneUtils.showAlert('📊 Security report exported!', 'success');
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error exporting report: ' + error.message, 'danger');
                });
        }

        function runSecurityScan() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
            btn.disabled = true;

            fetch('/api/security/scan', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ Security scan completed!', 'success');
                        loadSecurityData();
                    } else {
                        OctaneUtils.showAlert('❌ Security scan failed: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error running scan: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function clearOldLogs() {
            if (!confirm('Clear security logs older than 30 days?')) return;

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Clearing...';
            btn.disabled = true;

            fetch('/api/security/clear-old-logs', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ days: 30 })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert(`✅ ${result.message}`, 'success');
                    loadSecurityData();
                } else {
                    OctaneUtils.showAlert('❌ Failed to clear logs: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error clearing logs: ' + error.message, 'danger');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function resetSecuritySettings() {
            if (!confirm('Reset all security settings to defaults? This action cannot be undone.')) return;

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';
            btn.disabled = true;

            fetch('/api/security/reset-settings', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ Security settings reset to defaults', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Failed to reset settings: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error resetting settings: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function updateSecurityRules() {
            const rules = {
                maxFailedAttempts: 5,
                blockDuration: 3600,
                enableIPBlocking: true,
                enableRateLimit: true
            };

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            btn.disabled = true;

            fetch('/api/security/update-rules', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ rules })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ Security rules updated successfully', 'success');
                } else {
                    OctaneUtils.showAlert('❌ Failed to update rules: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error updating rules: ' + error.message, 'danger');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function viewEventDetails(eventId) {
            OctaneUtils.showAlert('👁️ Event details feature coming soon...', 'info');
        }

        function dismissEvent(eventId) {
            if (!confirm('Dismiss this security event?')) return;
            OctaneUtils.showAlert('✖️ Dismiss event feature coming soon...', 'info');
        }

        function refreshBlockedIPs() {
            loadBlockedIPs();
        }

        function deleteAllSecurityEvents() {
            if (!confirm('Delete ALL security events? This action cannot be undone!')) return;
            if (!confirm('Are you absolutely sure? This will delete all security event history!')) return;

            fetch('/api/security/events', {
                method: 'DELETE',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ deleteAll: true })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ All security events deleted successfully', 'success');
                    loadSecurityData();
                } else {
                    OctaneUtils.showAlert('❌ Failed to delete events: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error deleting events: ' + error.message, 'danger');
            });
        }
    </script>
</body>
</html>
