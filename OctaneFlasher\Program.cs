using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Management;
using System.Security.Cryptography;
using System.Reflection;
using System.Runtime.InteropServices;

namespace OctaneFlasher
{
    class Program
    {
        private static readonly HttpClient httpClient = new HttpClient()
        {
            Timeout = TimeSpan.FromSeconds(30),
        };
        private static readonly string API_BASE_URL = "http://217.154.58.14/api";
        private static string FLASHER_ERROR_WEBHOOK = "https://discord.com/api/webhooks/1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW";
        private static string licenseKey = "";
        private static string hardwareId = "";
        private static Dictionary<string, string> webhookUrls = new Dictionary<string, string>();

        // Version information
        private static readonly string FLASHER_VERSION = "2.0.0";
        private static readonly string EXPECTED_FIRMWARE_VERSION = "2.0.0";
        private static readonly int FIRMWARE_VERSION_MAJOR = 2;
        private static readonly int FIRMWARE_VERSION_MINOR = 0;
        private static readonly int FIRMWARE_VERSION_PATCH = 0;

        // Security constants for anti-debugging
        private static readonly string[] BLOCKED_PROCESSES = {
            "ollydbg", "x64dbg", "x32dbg", "windbg", "ida", "ida64", "idaq", "idaq64",
            "immunitydebugger", "cheatengine", "processhacker", "procmon", "procexp",
            "wireshark", "fiddler", "burpsuite", "dnspy", "reflexil", "de4dot",
            "ilspy", "dotpeek", "justdecompile", "telerik", "resharper", "ghidra"
        };

        [DllImport("kernel32.dll")]
        static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll")]
        static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        [DllImport("kernel32.dll")]
        static extern IntPtr GetCurrentProcess();

        static async Task Main(string[] args)
        {
            // Perform security checks first
            if (!PerformSecurityChecks())
            {
                Environment.Exit(1);
                return;
            }

            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                🔥 OCTANE ESP32 FLASHER 🔥                   ║");
            Console.WriteLine("║                      by Octane Team                         ║");
            Console.WriteLine($"║                     Version {FLASHER_VERSION}                        ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();
            Console.WriteLine();

            try
            {
                // Extract embedded files first
                ExtractAllFirmwareFiles();

                // Check for command line arguments
                if (args.Length > 0)
                {
                    licenseKey = args[0];
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"🔑 Using license key from command line: {licenseKey}");
                    Console.ResetColor();
                    Console.WriteLine();
                }

                // Step 1: License Validation
                if (!await ValidateLicense())
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("❌ License validation failed. Access denied.");
                    Console.ResetColor();
                    Console.WriteLine("\nPress any key to exit...");
                    Console.ReadKey();
                    return;
                }

                // Step 1.5: Get webhook URLs from VPS
                await GetWebhookUrls();

                // Step 2: Check Required Files
                if (!CheckRequiredFiles())
                {
                    Console.WriteLine("\nPress any key to exit...");
                    Console.ReadKey();
                    return;
                }

                // Step 3: ESP32 Detection and Flashing
                await FlashESP32();

            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Fatal Error: {ex.Message}");
                Console.ResetColor();

                // Send error to Discord webhook
                _ = SendErrorToWebhook("Fatal Flasher Error", ex.Message, ex.StackTrace);
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static async Task<bool> ValidateLicense()
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("🔐 LICENSE VALIDATION REQUIRED");
            Console.WriteLine("═══════════════════════════════");
            Console.ResetColor();
            Console.WriteLine();

            // Generate hardware ID
            hardwareId = GenerateHardwareId();
            Console.WriteLine($"Hardware ID: {hardwareId}");
            Console.WriteLine();

            // Get license key from user (if not provided via command line)
            if (string.IsNullOrEmpty(licenseKey))
            {
                Console.Write("Enter your Octane license key: ");
                licenseKey = Console.ReadLine()?.Trim();
            }
            else
            {
                Console.WriteLine($"Using provided license key: {licenseKey}");
            }

            if (string.IsNullOrEmpty(licenseKey))
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("❌ License key cannot be empty.");
                Console.ResetColor();
                return false;
            }

            Console.WriteLine();

            // Retry logic for better reliability
            const int maxRetries = 3;
            const int retryDelayMs = 5000; // 5 seconds between retries

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    if (attempt == 1)
                    {
                        Console.WriteLine("🔄 Validating license...");
                    }
                    else
                    {
                        Console.WriteLine($"🔄 Retrying validation... (Attempt {attempt}/{maxRetries})");
                        await Task.Delay(retryDelayMs);
                    }

                    var requestData = new
                    {
                        licenseKey = licenseKey,
                        hardwareId = hardwareId
                    };

                    var json = JsonSerializer.Serialize(requestData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // Add headers for better compatibility
                    content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
                    httpClient.DefaultRequestHeaders.Clear();
                    httpClient.DefaultRequestHeaders.Add("User-Agent", "OctaneFlasher/2.1.0");

                    var response = await httpClient.PostAsync($"{API_BASE_URL}/validate", content);
                    var responseJson = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var result = JsonSerializer.Deserialize<ValidationResponse>(responseJson);

                        if (result.success)
                        {
                            Console.ForegroundColor = ConsoleColor.Green;
                            Console.WriteLine("✅ License validated successfully!");

                            // Get expiry date using helper method
                            DateTime? expiryDate = result.GetExpiryDate();
                            if (expiryDate.HasValue)
                            {
                                string timeRemaining = FormatTimeRemaining(expiryDate.Value);
                                Console.WriteLine($"   License expires: {timeRemaining}");
                            }
                            else
                            {
                                Console.WriteLine("   License expires: Lifetime");
                            }

                            Console.ResetColor();
                            Console.WriteLine();
                            return true;
                        }
                        else
                        {
                            Console.ForegroundColor = ConsoleColor.Red;
                            Console.WriteLine($"❌ License validation failed: {result.message}");
                            Console.ResetColor();
                            return false;
                        }
                    }
                    else
                    {
                        // Don't return false immediately, let it retry
                        if (attempt == maxRetries)
                        {
                            Console.ForegroundColor = ConsoleColor.Red;
                            Console.WriteLine("❌ Failed to connect to license server.");
                            Console.WriteLine("   Please check your internet connection and try again.");
                            Console.ResetColor();
                            return false;
                        }
                        else
                        {
                            Console.WriteLine($"⚠️ Connection failed (HTTP {response.StatusCode}), retrying...");
                            continue; // Try again
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (attempt == maxRetries)
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine("❌ Failed to connect to license server.");
                        Console.WriteLine($"   Error: {ex.Message}");
                        Console.WriteLine("   Please check your internet connection and try again.");
                        Console.ResetColor();
                        return false;
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ Connection error: {ex.Message}, retrying...");
                        continue; // Try again
                    }
                }
            }
            // If we get here, all retries failed
            return false;
        }

        static async Task GetWebhookUrls()
        {
            Console.WriteLine("🔗 Retrieving webhook URLs from VPS...");

            try
            {
                var response = await httpClient.GetAsync($"{API_BASE_URL}/webhooks/public");
                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var webhookResponse = JsonSerializer.Deserialize<WebhookResponse>(responseJson);

                    if (webhookResponse?.success == true && webhookResponse.webhooks != null)
                    {
                        webhookUrls = new Dictionary<string, string>
                        {
                            ["security_alerts"] = webhookResponse.webhooks.security_alerts ?? "",
                            ["esp32_errors"] = webhookResponse.webhooks.esp32_errors ?? "",
                            ["backend_errors"] = webhookResponse.webhooks.backend_errors ?? ""
                        };

                        // Update the ESP32 error webhook URL
                        if (!string.IsNullOrEmpty(webhookResponse.webhooks.esp32_errors))
                        {
                            FLASHER_ERROR_WEBHOOK = $"https://discord.com/api/webhooks/{webhookResponse.webhooks.esp32_errors}";
                        }

                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine("✅ Webhook URLs retrieved successfully");
                        Console.ResetColor();
                    }
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine("⚠️ Could not retrieve webhook URLs, using defaults");
                    Console.ResetColor();
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"⚠️ Webhook retrieval failed: {ex.Message}");
                Console.WriteLine("   Using default webhook URLs");
                Console.ResetColor();
            }

            Console.WriteLine();
        }

        static string GenerateHardwareId()
        {
            try
            {
                var sb = new StringBuilder();
                sb.Append(Environment.ProcessorCount);
                sb.Append(Environment.MachineName);
                sb.Append(Environment.UserName);
                sb.Append(Environment.OSVersion.ToString());

                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(sb.ToString()));
                    return Convert.ToBase64String(hash).Substring(0, 16);
                }
            }
            catch
            {
                return Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Environment.MachineName}_{Environment.UserName}")).Substring(0, 16);
            }
        }

        static bool CheckRequiredFiles()
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("📁 CHECKING REQUIRED FILES");
            Console.WriteLine("═══════════════════════════");
            Console.ResetColor();

            var requiredFiles = new Dictionary<string, (int minSize, string description)>
            {
                { "octane_auth_firmware.bin", (50000, "Main ESP32-S2 HID Mouse Firmware") },
                { "bootloader.bin", (20000, "ESP32-S2 Bootloader") },
                { "partitions.bin", (1000, "Partition Table") }
            };

            bool allFilesPresent = true;
            long totalFirmwareSize = 0;

            // Get the directory where the executable is located
            string exeDirectory = AppContext.BaseDirectory;

            Console.WriteLine($"📂 Checking firmware files in: {exeDirectory}");
            Console.WriteLine();

            foreach (var fileInfo in requiredFiles)
            {
                string fileName = fileInfo.Key;
                int minSize = fileInfo.Value.minSize;
                string description = fileInfo.Value.description;

                // Check in the executable directory
                string filePath = Path.Combine(exeDirectory, fileName);
                bool fileExists = File.Exists(filePath);

                if (fileExists)
                {
                    var fileSize = new FileInfo(filePath).Length;
                    totalFirmwareSize += fileSize;

                    if (fileSize >= minSize)
                    {
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine($"✅ {fileName} ({FormatFileSize(fileSize)}) - {description}");
                        Console.ResetColor();
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ {fileName} ({FormatFileSize(fileSize)}) - TOO SMALL (min: {FormatFileSize(minSize)})");
                        Console.WriteLine($"   This appears to be a dummy/placeholder file");
                        Console.ResetColor();
                        allFilesPresent = false;
                    }
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"❌ {fileName} - MISSING ({description})");
                    Console.ResetColor();
                    allFilesPresent = false;
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 Total firmware size: {FormatFileSize(totalFirmwareSize)}");
            Console.WriteLine();

            // Check for esptool separately with better detection
            string esptoolPath = GetEsptoolExecutable();
            bool esptoolAvailable = false;

            if (File.Exists(esptoolPath))
            {
                var esptoolSize = new FileInfo(esptoolPath).Length;
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"✅ esptool found: {Path.GetFileName(esptoolPath)} ({FormatFileSize(esptoolSize)})");
                Console.ResetColor();
                esptoolAvailable = true;
            }
            else if (IsCommandAvailable("esptool") || IsCommandAvailable("esptool.py"))
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine($"✅ esptool found in system PATH");
                Console.ResetColor();
                esptoolAvailable = true;
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ esptool - NOT FOUND");
                Console.WriteLine("   Tried: esptool.exe, esptool, esptool.py");
                Console.WriteLine("   Please install ESP-IDF or place esptool.exe in this directory");
                Console.ResetColor();
                allFilesPresent = false;
            }

            if (!allFilesPresent)
            {
                Console.WriteLine();
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("❌ MISSING REQUIRED FILES");
                Console.WriteLine("═══════════════════════════");
                Console.ResetColor();
                Console.WriteLine();

                if (!esptoolAvailable)
                {
                    Console.WriteLine("🔧 To install esptool:");
                    Console.WriteLine("   Option 1: Install ESP-IDF (recommended)");
                    Console.WriteLine("   Option 2: pip install esptool");
                    Console.WriteLine("   Option 3: Download esptool.exe and place in flasher directory");
                    Console.WriteLine();
                }

                Console.WriteLine("📁 To get firmware files:");
                Console.WriteLine("   1. Navigate to: esp32-flasher\\esp32-firmware\\");
                Console.WriteLine("   2. Run: build_firmware.bat");
                Console.WriteLine("   3. Firmware files will be automatically copied here");
                Console.WriteLine();
                Console.WriteLine("   Or compile manually with ESP-IDF:");
                Console.WriteLine("   idf.py set-target esp32s2");
                Console.WriteLine("   idf.py build");
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✅ All required files present and valid!");
                Console.ResetColor();
            }

            Console.WriteLine();
            return allFilesPresent;
        }

        // Helper method to format file sizes
        static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024.0 * 1024.0):F1} MB";
            return $"{bytes / (1024.0 * 1024.0 * 1024.0):F1} GB";
        }

        static bool IsCommandAvailable(string command)
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = command,
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit(5000); // 5 second timeout
                    return process.ExitCode == 0;
                }
            }
            catch
            {
                return false;
            }
        }

        static async Task FlashESP32()
        {
            ShowProgressHeader("ESP32 FLASHING PROCESS", 1, 5);

            // Step 1: User preparation with better guidance
            Console.WriteLine("📋 STEP 1: PREPARE ESP32 FOR FLASHING");
            Console.WriteLine("═══════════════════════════════════");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🔌 ESP32-S2 Download Mode Instructions:");
            Console.ResetColor();
            Console.WriteLine();
            Console.WriteLine("   1️⃣  Unplug your ESP32-S2 board from USB");
            Console.WriteLine("   2️⃣  Locate the BOOT button on your ESP32-S2");
            Console.WriteLine("   3️⃣  Hold down the BOOT button firmly");
            Console.WriteLine("   4️⃣  While holding BOOT, plug USB cable back in");
            Console.WriteLine("   5️⃣  Release the BOOT button after USB is connected");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("💡 TIP: The LED should be dim or off when in download mode");
            Console.ResetColor();
            Console.WriteLine();

            Console.Write("Press any key when ESP32 is ready for flashing... ");
            Console.ReadKey();
            Console.WriteLine();
            Console.WriteLine();

            ShowProgressIndicator("Waiting for ESP32 to enter download mode", 2000);
            Console.WriteLine();

            ShowProgressHeader("COM PORT DETECTION", 2, 5);

            // Step 2: Auto-detect COM port with better user guidance
            string comPort = DetectESP32();

            if (string.IsNullOrEmpty(comPort))
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("⚠️ Automatic detection failed - Manual selection required");
                Console.ResetColor();
                Console.WriteLine();

                ShowDeviceManagerInstructions();

                comPort = GetManualComPort();
                if (string.IsNullOrEmpty(comPort))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine("❌ Invalid COM port selection. Flashing aborted.");
                    Console.ResetColor();
                    return;
                }
            }

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"✅ Selected COM port: {comPort}");
            Console.ResetColor();
            Console.WriteLine();

            ShowProgressHeader("ERROR REPORTING SETUP", 3, 5);

            // Step 3: Error Reporting Configuration with better explanation
            Console.WriteLine("🔧 CONFIGURE ERROR REPORTING");
            Console.WriteLine("═══════════════════════════");
            Console.WriteLine();

            Console.WriteLine("📊 Error reporting helps us:");
            Console.WriteLine("   • Monitor ESP32 performance and issues");
            Console.WriteLine("   • Fix problems quickly for all users");
            Console.WriteLine("   • Improve firmware stability");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🔒 Privacy: Only error messages and basic hardware info are sent");
            Console.ResetColor();
            Console.WriteLine();

            Console.WriteLine("Choose an option:");
            Console.WriteLine("   [Y] Yes - Enable error reporting (Recommended)");
            Console.WriteLine("   [N] No - Disable error reporting");
            Console.WriteLine();
            Console.Write("Your choice (Y/N): ");

            string errorChoice = Console.ReadLine()?.Trim().ToUpper();
            bool enableErrorReporting = errorChoice == "Y" || errorChoice == "YES" || string.IsNullOrEmpty(errorChoice);

            Console.WriteLine();
            if (enableErrorReporting)
            {
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✅ Error reporting enabled - Thank you for helping us improve!");
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("⚠️ Error reporting disabled");
            }
            Console.ResetColor();
            Console.WriteLine();

            ShowProgressHeader("FIRMWARE FLASHING", 4, 5);

            // Step 4: Flash firmware with progress indication
            Console.WriteLine("🚀 FLASHING ESP32-S2 FIRMWARE");
            Console.WriteLine("═════════════════════════════");
            Console.WriteLine();

            Console.WriteLine("⏳ This process typically takes 2-3 minutes");
            Console.WriteLine("💡 ESP32-S2 warnings about 'manual reset' are normal");
            Console.WriteLine();

            bool flashSuccess = await FlashFirmware(comPort);

            ShowProgressHeader("COMPLETION", 5, 5);

            if (flashSuccess)
            {
                ShowSuccessMessage();
            }
            else
            {
                ShowFailureMessage();
            }
        }

        static void ShowSuccessMessage()
        {
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("🎉 FIRMWARE FLASHING COMPLETED SUCCESSFULLY!");
            Console.WriteLine("═══════════════════════════════════════════");
            Console.ResetColor();
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("🔄 IMPORTANT: Press the RESET button on your ESP32-S2 now!");
            Console.ResetColor();
            Console.WriteLine();

            Console.WriteLine("📋 WHAT HAPPENS NEXT:");
            Console.WriteLine("   1️⃣  Press the RESET button on your ESP32-S2");
            Console.WriteLine("   2️⃣  LED on GPIO15 will start flashing (firmware is running)");
            Console.WriteLine("   3️⃣  ESP32 appears as USB HID mouse in Device Manager");
            Console.WriteLine("   4️⃣  Open your Octane desktop application");
            Console.WriteLine("   5️⃣  LED becomes steady when connected to desktop app");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("🔍 VERIFICATION STEPS:");
            Console.ResetColor();
            Console.WriteLine("   • Check Device Manager > Human Interface Devices");
            Console.WriteLine("   • Look for 'HID-compliant mouse' or 'Octane HID Mouse'");
            Console.WriteLine("   • LED should flash every 500ms when disconnected");
            Console.WriteLine("   • LED should be steady when desktop app connects");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("⚠️ TROUBLESHOOTING:");
            Console.ResetColor();
            Console.WriteLine("   • If no LED activity: Press RESET button");
            Console.WriteLine("   • If not in Device Manager: Try different USB port");
            Console.WriteLine("   • If desktop app can't connect: Check COM port in app");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine("✅ Your ESP32-S2 HID Mouse is ready for use!");
            Console.ResetColor();
        }

        static void ShowFailureMessage()
        {
            Console.ForegroundColor = ConsoleColor.Red;
            Console.WriteLine("❌ FIRMWARE FLASHING FAILED");
            Console.WriteLine("═══════════════════════════");
            Console.ResetColor();
            Console.WriteLine();

            Console.WriteLine("🔧 TROUBLESHOOTING STEPS:");
            Console.WriteLine("   1️⃣  Ensure ESP32-S2 is in download mode:");
            Console.WriteLine("      • Hold BOOT button");
            Console.WriteLine("      • Plug in USB while holding BOOT");
            Console.WriteLine("      • Release BOOT button");
            Console.WriteLine();
            Console.WriteLine("   2️⃣  Check connections:");
            Console.WriteLine("      • USB cable is working (try different cable)");
            Console.WriteLine("      • USB port is functional (try different port)");
            Console.WriteLine("      • ESP32-S2 is properly seated");
            Console.WriteLine();
            Console.WriteLine("   3️⃣  Verify COM port:");
            Console.WriteLine("      • Open Device Manager");
            Console.WriteLine("      • Check Ports (COM & LPT) section");
            Console.WriteLine("      • ESP32 should appear when in download mode");
            Console.WriteLine();
            Console.WriteLine("   4️⃣  Try again:");
            Console.WriteLine("      • Run this flasher again");
            Console.WriteLine("      • Follow download mode steps carefully");
            Console.WriteLine();

            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("💡 Need help? Check the documentation or contact support");
            Console.ResetColor();
        }

        static string DetectESP32()
        {
            Console.WriteLine("🔍 Scanning for available ports...");
            Console.WriteLine();

            string[] ports = SerialPort.GetPortNames();

            if (ports.Length == 0)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("❌ No COM ports found!");
                Console.ResetColor();
                return null;
            }

            // Display available ports like the working example
            Console.WriteLine("Available ports:");
            var availablePorts = new List<string>();

            for (int i = 0; i < ports.Length; i++)
            {
                string portDescription = GetPortDescription(ports[i]);
                Console.WriteLine($"[{i}] {ports[i]} {portDescription}");
                availablePorts.Add(ports[i]);
            }

            Console.WriteLine();
            Console.Write("Select port index: ");

            string input = Console.ReadLine();
            if (int.TryParse(input, out int selectedIndex) && selectedIndex >= 0 && selectedIndex < availablePorts.Count)
            {
                string selectedPort = availablePorts[selectedIndex];
                Console.WriteLine($"🔌 Selected port: {selectedPort}");
                Console.WriteLine();

                // Try to detect ESP32 on selected port
                if (TestESP32Connection(selectedPort))
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine($"✅ ESP32 detected on {selectedPort}");
                    Console.ResetColor();
                }
                else
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine($"⚠️ ESP32 not detected on {selectedPort}, but proceeding anyway...");
                    Console.ResetColor();
                }

                return selectedPort;
            }
            else
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("❌ Invalid port selection!");
                Console.ResetColor();
                return null;
            }
        }

        static string GetPortDescription(string portName)
        {
            try
            {
                // Try to get a more descriptive name for the port
                using (var searcher = new System.Management.ManagementObjectSearcher(
                    $"SELECT * FROM Win32_PnPEntity WHERE Caption LIKE '%{portName}%'"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        string caption = obj["Caption"]?.ToString();
                        if (!string.IsNullOrEmpty(caption))
                        {
                            return caption.Replace($"({portName})", "").Trim();
                        }
                    }
                }
            }
            catch
            {
                // Fallback to generic description
            }

            return "Serial Port";
        }

        static bool TestESP32Connection(string portName)
        {
            try
            {
                // Use esptool to test connection
                var startInfo = new ProcessStartInfo
                {
                    FileName = GetEsptoolExecutable(),
                    Arguments = $"--port {portName} chip_id",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    process.WaitForExit(5000); // 5 second timeout

                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();

                    // Check for ESP32 detection in output or error stream
                    bool hasESP32 = output.Contains("ESP32") || error.Contains("ESP32") ||
                                   output.Contains("Chip is") || error.Contains("Chip is");

                    // ESP32-S2 may have warnings but still be detected successfully
                    bool hasESP32S2 = output.Contains("ESP32-S2") || error.Contains("ESP32-S2");

                    return hasESP32 || hasESP32S2;
                }
            }
            catch
            {
                // Connection test failed
            }

            return false;
        }

        static string GetManualComPort()
        {
            Console.WriteLine("📋 AVAILABLE COM PORTS:");
            string[] ports = SerialPort.GetPortNames();

            if (ports.Length == 0)
            {
                Console.WriteLine("❌ No COM ports found!");
                return null;
            }

            for (int i = 0; i < ports.Length; i++)
            {
                Console.WriteLine($"[{i + 1}] {ports[i]}");
            }
            Console.WriteLine();

            Console.Write($"Select COM port (1-{ports.Length}): ");
            string input = Console.ReadLine()?.Trim();

            if (int.TryParse(input, out int choice))
            {
                if (choice >= 1 && choice <= ports.Length)
                {
                    return ports[choice - 1];
                }
            }

            return null;
        }



        static string GetEsptoolExecutable()
        {
            // Get the directory where the executable is located
            string exeDirectory = AppContext.BaseDirectory;

            // Check for different esptool versions based on platform
            string[] possibleEsptoolNames = {
                "esptool.exe",      // Windows
                "esptool",          // Linux/Mac
                "esptool.py"        // Python version
            };

            foreach (string toolName in possibleEsptoolNames)
            {
                string toolPath = Path.Combine(exeDirectory, toolName);
                if (File.Exists(toolPath))
                {
                    return toolPath;
                }
            }

            // If no local esptool found, try system PATH
            return "esptool";
        }

        static string GetFirmwareFilePath(string fileName, string tempPath)
        {
            // Get the directory where the executable is located
            string exeDirectory = AppContext.BaseDirectory;

            // Check multiple locations for firmware files in priority order
            string[] possiblePaths = {
                Path.Combine(exeDirectory, fileName),                    // Executable directory (primary)
                Path.Combine(exeDirectory, "firmware", fileName),       // Firmware subdirectory
                Path.Combine(tempPath, fileName),                       // Temp directory (extracted)
                fileName,                                               // Current directory (fallback)
                Path.Combine(Environment.CurrentDirectory, fileName),   // Working directory
                Path.Combine(Environment.CurrentDirectory, "firmware", fileName) // Working dir firmware subdir
            };

            foreach (string path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    // Validate file size to ensure it's not a dummy file
                    var fileInfo = new FileInfo(path);
                    if (ValidateFirmwareFile(fileName, fileInfo.Length))
                    {
                        ESP_LOGI($"Found valid firmware file: {path} ({FormatFileSize(fileInfo.Length)})");
                        return path;
                    }
                    else
                    {
                        ESP_LOGW($"Found firmware file but invalid size: {path} ({FormatFileSize(fileInfo.Length)})");
                    }
                }
            }

            // If no valid file found, return the primary path for error reporting
            return Path.Combine(exeDirectory, fileName);
        }

        static bool ValidateFirmwareFile(string fileName, long fileSize)
        {
            var minimumSizes = new Dictionary<string, long>
            {
                { "octane_auth_firmware.bin", 50000 },  // Main firmware should be at least 50KB
                { "bootloader.bin", 20000 },            // Bootloader should be at least 20KB
                { "partitions.bin", 1000 }              // Partition table should be at least 1KB
            };

            if (minimumSizes.TryGetValue(fileName, out long minSize))
            {
                return fileSize >= minSize;
            }

            // For unknown files, just check they're not empty
            return fileSize > 0;
        }

        // UI Helper Methods
        static void ShowProgressHeader(string title, int currentStep, int totalSteps)
        {
            Console.Clear();
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
            Console.WriteLine("║                🔥 OCTANE ESP32 FLASHER 🔥                   ║");
            Console.WriteLine("║                      by Octane Team                         ║");
            Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
            Console.ResetColor();
            Console.WriteLine();

            // Progress bar
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"📊 PROGRESS: STEP {currentStep} OF {totalSteps}");
            Console.ResetColor();

            // Visual progress bar
            int progressWidth = 50;
            int filledWidth = (int)((double)currentStep / totalSteps * progressWidth);

            Console.Write("   [");
            Console.ForegroundColor = ConsoleColor.Green;
            Console.Write(new string('█', filledWidth));
            Console.ResetColor();
            Console.Write(new string('░', progressWidth - filledWidth));
            Console.WriteLine($"] {currentStep}/{totalSteps}");
            Console.WriteLine();

            // Current step title
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"🔄 {title}");
            Console.WriteLine(new string('═', title.Length + 3));
            Console.ResetColor();
            Console.WriteLine();
        }

        static async Task ShowProgressIndicator(string message, int durationMs)
        {
            Console.Write($"⏳ {message}");

            var spinner = new[] { "⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏" };
            int spinnerIndex = 0;

            var startTime = DateTime.Now;
            while ((DateTime.Now - startTime).TotalMilliseconds < durationMs)
            {
                Console.Write($"\r⏳ {message} {spinner[spinnerIndex]}");
                spinnerIndex = (spinnerIndex + 1) % spinner.Length;
                await Task.Delay(100);
            }

            Console.WriteLine($"\r✅ {message} - Complete");
        }

        static void ShowDeviceManagerInstructions()
        {
            Console.ForegroundColor = ConsoleColor.Cyan;
            Console.WriteLine("📋 HOW TO FIND YOUR ESP32 COM PORT:");
            Console.ResetColor();
            Console.WriteLine();
            Console.WriteLine("   1️⃣  Press Windows + X, then select 'Device Manager'");
            Console.WriteLine("   2️⃣  Expand 'Ports (COM & LPT)' section");
            Console.WriteLine("   3️⃣  Look for one of these devices:");
            Console.WriteLine("      • 'USB Serial Device (COM#)'");
            Console.WriteLine("      • 'Silicon Labs CP210x USB to UART Bridge (COM#)'");
            Console.WriteLine("      • 'USB-SERIAL CH340 (COM#)'");
            Console.WriteLine("      • 'ESP32-S2 USB JTAG/serial debug unit (COM#)'");
            Console.WriteLine("   4️⃣  Note the COM port number (e.g., COM3, COM10)");
            Console.WriteLine();
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("💡 TIP: If no device appears, ESP32 may not be in download mode");
            Console.ResetColor();
            Console.WriteLine();
        }

        // Logging helpers for consistency
        static void ESP_LOGI(string message)
        {
            Console.WriteLine($"ℹ️ {message}");
        }

        static void ESP_LOGW(string message)
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"⚠️ {message}");
            Console.ResetColor();
        }

        // Firmware version validation
        static bool ValidateFirmwareVersion(string versionResponse)
        {
            try
            {
                // Parse version response: VERSION:2.0.0,BUILD:...,GIT:...,BOARD:...,MAJOR:2,MINOR:0,PATCH:0
                var parts = versionResponse.Split(',');

                string firmwareVersion = "";
                int major = 0, minor = 0, patch = 0;

                foreach (var part in parts)
                {
                    var keyValue = part.Split(':');
                    if (keyValue.Length == 2)
                    {
                        switch (keyValue[0].Trim())
                        {
                            case "VERSION":
                                firmwareVersion = keyValue[1].Trim();
                                break;
                            case "MAJOR":
                                int.TryParse(keyValue[1].Trim(), out major);
                                break;
                            case "MINOR":
                                int.TryParse(keyValue[1].Trim(), out minor);
                                break;
                            case "PATCH":
                                int.TryParse(keyValue[1].Trim(), out patch);
                                break;
                        }
                    }
                }

                Console.WriteLine($"   Firmware version: {firmwareVersion}");
                Console.WriteLine($"   Expected version: {EXPECTED_FIRMWARE_VERSION}");

                // Check if versions match exactly
                if (firmwareVersion == EXPECTED_FIRMWARE_VERSION)
                {
                    return true;
                }

                // Check if major version matches (backward compatibility)
                if (major == FIRMWARE_VERSION_MAJOR)
                {
                    if (minor >= FIRMWARE_VERSION_MINOR)
                    {
                        Console.WriteLine("   Version is compatible (same or newer minor version)");
                        return true;
                    }
                    else
                    {
                        Console.WriteLine("   Version is older but may be compatible");
                        return false;
                    }
                }
                else
                {
                    Console.WriteLine("   Version has different major version - may not be compatible");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   Could not parse version response: {ex.Message}");
                return false;
            }
        }

        static string ExtractEmbeddedFile(string resourceName, string fileName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using (Stream stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream == null)
                    {
                        Console.WriteLine($"⚠️ Embedded resource not found: {resourceName}");
                        return null;
                    }

                    string tempPath = Path.Combine(Path.GetTempPath(), "OctaneFlasher");
                    Directory.CreateDirectory(tempPath);

                    string filePath = Path.Combine(tempPath, fileName);

                    // Only extract if file doesn't exist or is different
                    if (!File.Exists(filePath) || new FileInfo(filePath).Length != stream.Length)
                    {
                        using (FileStream fileStream = File.Create(filePath))
                        {
                            stream.CopyTo(fileStream);
                        }

                        // Make executable if it's esptool
                        if (fileName.EndsWith(".exe"))
                        {
                            File.SetAttributes(filePath, FileAttributes.Normal);
                        }
                    }

                    return filePath;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Failed to extract {fileName}: {ex.Message}");
                return null;
            }
        }

        static void ExtractAllFirmwareFiles()
        {
            Console.WriteLine("📦 Checking for required files...");

            // Check if we have real firmware files
            CheckFirmwareFiles();
        }

        static void CheckFirmwareFiles()
        {
            var firmwareFiles = new Dictionary<string, (long minSize, string description)>
            {
                { "octane_auth_firmware.bin", (50000, "ESP32-S2 HID Mouse Firmware") },
                { "bootloader.bin", (20000, "ESP32-S2 Bootloader") },
                { "partitions.bin", (1000, "Partition Table") }
            };

            bool hasRealFirmware = true;
            var foundFiles = new List<(string name, long size, bool valid)>();

            // Get the directory where the executable is located
            string exeDirectory = AppContext.BaseDirectory;

            Console.WriteLine("🔍 Scanning for firmware files...");
            Console.WriteLine();

            foreach (var fileInfo in firmwareFiles)
            {
                string fileName = fileInfo.Key;
                long minSize = fileInfo.Value.minSize;
                string description = fileInfo.Value.description;

                string filePath = Path.Combine(exeDirectory, fileName);
                if (File.Exists(filePath))
                {
                    var fileSize = new FileInfo(filePath).Length;
                    bool isValid = fileSize >= minSize;
                    foundFiles.Add((fileName, fileSize, isValid));

                    if (isValid)
                    {
                        Console.WriteLine($"✅ {fileName} - {description} ({FormatFileSize(fileSize)})");
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ {fileName} - {description} ({FormatFileSize(fileSize)}) - TOO SMALL");
                        hasRealFirmware = false;
                    }
                }
                else
                {
                    Console.WriteLine($"❌ {fileName} - {description} - NOT FOUND");
                    foundFiles.Add((fileName, 0, false));
                    hasRealFirmware = false;
                }
            }

            if (!hasRealFirmware)
            {
                Console.WriteLine();
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("⚠️ FIRMWARE FILES ISSUE DETECTED");
                Console.WriteLine("═══════════════════════════════");
                Console.ResetColor();
                Console.WriteLine();

                Console.WriteLine("📋 Required firmware files:");
                foreach (var fileInfo in firmwareFiles)
                {
                    string fileName = fileInfo.Key;
                    string description = fileInfo.Value.description;
                    long minSize = fileInfo.Value.minSize;

                    var found = foundFiles.FirstOrDefault(f => f.name == fileName);
                    if (found.name != null)
                    {
                        if (found.valid)
                        {
                            Console.WriteLine($"   ✅ {fileName} - {description}");
                        }
                        else if (found.size > 0)
                        {
                            Console.WriteLine($"   ⚠️ {fileName} - {description} (too small: {FormatFileSize(found.size)} < {FormatFileSize(minSize)})");
                        }
                        else
                        {
                            Console.WriteLine($"   ❌ {fileName} - {description} (missing)");
                        }
                    }
                }

                Console.WriteLine();
                Console.WriteLine("🔧 How to fix this:");
                Console.WriteLine("   1. Navigate to: esp32-flasher\\esp32-firmware\\");
                Console.WriteLine("   2. Run: build_firmware.bat");
                Console.WriteLine("   3. Files will be automatically copied to flasher directory");
                Console.WriteLine();
                Console.WriteLine("   Or manually compile with ESP-IDF:");
                Console.WriteLine("   • idf.py set-target esp32s2");
                Console.WriteLine("   • idf.py build");
                Console.WriteLine("   • Copy files from build\\ directory");
                Console.WriteLine();
                return;
            }
            else
            {
                Console.WriteLine();
                Console.ForegroundColor = ConsoleColor.Green;
                Console.WriteLine("✅ All firmware files are present and valid!");
                Console.ResetColor();
                Console.WriteLine();
            }
        }

        static async Task<bool> FlashFirmware(string comPort)
        {
            try
            {
                // Pre-flash validation
                if (!await PreFlashValidation(comPort))
                {
                    return false;
                }

                string esptoolPath = GetEsptoolExecutable();

                // Get paths to firmware files (check multiple locations)
                string tempPath = Path.Combine(Path.GetTempPath(), "OctaneFlasher");

                string bootloaderPath = GetFirmwareFilePath("bootloader.bin", tempPath);
                string firmwarePath = GetFirmwareFilePath("octane_auth_firmware.bin", tempPath);
                string partitionsPath = GetFirmwareFilePath("partitions.bin", tempPath);

                // Validate all firmware files exist and are valid
                var firmwareFiles = new Dictionary<string, string>
                {
                    { "bootloader", bootloaderPath },
                    { "firmware", firmwarePath },
                    { "partitions", partitionsPath }
                };

                foreach (var file in firmwareFiles)
                {
                    if (!File.Exists(file.Value))
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ {file.Key} file not found: {file.Value}");
                        Console.ResetColor();
                        return false;
                    }

                    var fileInfo = new FileInfo(file.Value);
                    if (!ValidateFirmwareFile(Path.GetFileName(file.Value), fileInfo.Length))
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"❌ {file.Key} file invalid: {file.Value} ({FormatFileSize(fileInfo.Length)})");
                        Console.ResetColor();
                        return false;
                    }
                }

                Console.WriteLine("✅ All firmware files validated successfully");
                Console.WriteLine();

                // Optimized ESP32-S2 flash parameters
                ProcessStartInfo startInfo = new ProcessStartInfo
                {
                    FileName = esptoolPath,
                    Arguments = $"--chip esp32s2 --port {comPort} --baud 460800 " +
                               $"--before default_reset --after hard_reset " +
                               $"write_flash --flash_mode dio --flash_freq 40m --flash_size 4MB " +
                               $"0x1000 \"{bootloaderPath}\" 0x8000 \"{partitionsPath}\" 0x10000 \"{firmwarePath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,  // Hide window for cleaner output
                    WorkingDirectory = Path.GetDirectoryName(esptoolPath) ?? Environment.CurrentDirectory
                };

                Console.WriteLine($"🔧 Flashing ESP32-S2 on {comPort}...");
                Console.WriteLine($"📁 Bootloader: {Path.GetFileName(bootloaderPath)}");
                Console.WriteLine($"📁 Partitions: {Path.GetFileName(partitionsPath)}");
                Console.WriteLine($"📁 Firmware: {Path.GetFileName(firmwarePath)}");
                Console.WriteLine();
                Console.WriteLine($"⚙️ Command: {Path.GetFileName(esptoolPath)} {startInfo.Arguments}");
                Console.WriteLine();

                using (Process process = Process.Start(startInfo))
                {
                    var flashResult = await ProcessEsptoolOutput(process);

                    if (flashResult.success)
                    {
                        Console.WriteLine();
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine("✅ FIRMWARE FLASHED SUCCESSFULLY!");

                        if (flashResult.requiresManualReset)
                        {
                            Console.ForegroundColor = ConsoleColor.Yellow;
                            Console.WriteLine("🔄 ESP32-S2 requires manual reset - this is normal!");
                            Console.WriteLine("   Press the RESET button on your ESP32 to start the firmware.");
                        }

                        Console.ResetColor();
                        Console.WriteLine();

                        // Post-flash verification
                        await PostFlashVerification(comPort);

                        return true;
                    }
                    else
                    {
                        Console.WriteLine();
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine("❌ FIRMWARE FLASHING FAILED!");

                        if (!string.IsNullOrEmpty(flashResult.errorDetails))
                        {
                            Console.WriteLine($"   Error: {flashResult.errorDetails}");
                        }

                        Console.WriteLine();
                        Console.WriteLine("🔧 Troubleshooting steps:");
                        Console.WriteLine("   1. Ensure ESP32 is in download mode (hold BOOT, plug USB, release BOOT)");
                        Console.WriteLine("   2. Check COM port is correct");
                        Console.WriteLine("   3. Try a different USB cable or port");
                        Console.WriteLine("   4. Verify ESP32-S2 is properly connected");

                        Console.ResetColor();
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Flash error: {ex.Message}");
                Console.ResetColor();

                // Send error to webhook for debugging
                _ = SendErrorToWebhook("ESP32 Flash Error", ex.Message, ex.StackTrace);

                return false;
            }
        }

        // Pre-flash validation
        static async Task<bool> PreFlashValidation(string comPort)
        {
            Console.WriteLine("🔍 Pre-flash validation...");

            // Test esptool connectivity
            try
            {
                string esptoolPath = GetEsptoolExecutable();
                var startInfo = new ProcessStartInfo
                {
                    FileName = esptoolPath,
                    Arguments = $"--chip esp32s2 --port {comPort} chip_id",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    await process.WaitForExitAsync();
                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();

                    bool hasESP32S2 = output.Contains("ESP32-S2") || error.Contains("ESP32-S2") ||
                                     output.Contains("Chip is ESP32-S2") || error.Contains("Chip is ESP32-S2");

                    if (hasESP32S2)
                    {
                        Console.WriteLine("✅ ESP32-S2 detected and ready for flashing");
                        return true;
                    }
                    else
                    {
                        Console.ForegroundColor = ConsoleColor.Yellow;
                        Console.WriteLine("⚠️ ESP32-S2 not detected, but proceeding anyway...");
                        Console.WriteLine("   Make sure ESP32 is in download mode");
                        Console.ResetColor();
                        return true; // Continue anyway, might still work
                    }
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"⚠️ Pre-flash validation failed: {ex.Message}");
                Console.WriteLine("   Proceeding with flash attempt anyway...");
                Console.ResetColor();
                return true; // Continue anyway
            }
        }

        // Flash result structure
        struct FlashResult
        {
            public bool success;
            public bool requiresManualReset;
            public string errorDetails;
            public List<string> warnings;
        }

        // Process esptool output with better parsing
        static async Task<FlashResult> ProcessEsptoolOutput(Process process)
        {
            var result = new FlashResult
            {
                success = false,
                requiresManualReset = false,
                errorDetails = "",
                warnings = new List<string>()
            };

            string fullOutput = "";
            string fullError = "";
            var progressIndicators = new[] { "Writing at 0x", "Hash of data verified", "Wrote", "compressed" };
            var errorIndicators = new[] { "Failed to", "Error:", "Timeout", "No serial data received" };

            // Read output in real-time with better formatting
            var outputTask = Task.Run(async () =>
            {
                string line;
                while ((line = await process.StandardOutput.ReadLineAsync()) != null)
                {
                    fullOutput += line + "\n";

                    // Show progress indicators
                    if (progressIndicators.Any(indicator => line.Contains(indicator)))
                    {
                        Console.WriteLine($"📡 {line}");
                    }
                    else if (line.Contains("Connecting"))
                    {
                        Console.WriteLine($"🔗 {line}");
                    }
                    else if (line.Contains("Chip is"))
                    {
                        Console.WriteLine($"🔍 {line}");
                    }
                }
            });

            var errorTask = Task.Run(async () =>
            {
                string line;
                while ((line = await process.StandardError.ReadLineAsync()) != null)
                {
                    fullError += line + "\n";

                    // Categorize error messages
                    if (line.Contains("can not exit the download mode over USB") ||
                        line.Contains("reset the chip manually"))
                    {
                        result.requiresManualReset = true;
                        Console.WriteLine($"ℹ️ {line}");
                    }
                    else if (errorIndicators.Any(indicator => line.Contains(indicator)))
                    {
                        Console.WriteLine($"❌ {line}");
                        if (string.IsNullOrEmpty(result.errorDetails))
                        {
                            result.errorDetails = line;
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ {line}");
                        result.warnings.Add(line);
                    }
                }
            });

            await process.WaitForExitAsync();
            await Task.WhenAll(outputTask, errorTask);

            // Determine success based on multiple criteria
            bool hasSuccessIndicators = fullOutput.Contains("Hash of data verified") ||
                                      fullOutput.Contains("Wrote") ||
                                      (fullOutput.Contains("compressed") && fullOutput.Contains("Writing at"));

            bool hasESP32S2Detection = fullOutput.Contains("ESP32-S2") || fullError.Contains("ESP32-S2");

            bool hasCriticalErrors = errorIndicators.Any(indicator =>
                fullError.Contains(indicator) && !fullError.Contains("can not exit the download mode"));

            result.success = (process.ExitCode == 0 || hasSuccessIndicators) &&
                           !hasCriticalErrors &&
                           hasESP32S2Detection;

            return result;
        }

        // Post-flash verification
        static async Task PostFlashVerification(string comPort)
        {
            Console.WriteLine("🔍 Post-flash verification...");

            try
            {
                // Wait for ESP32 to boot
                await Task.Delay(3000);

                // Try to connect via serial to verify firmware is running
                using (var serialPort = new SerialPort(comPort, 921600))
                {
                    serialPort.ReadTimeout = 5000;
                    serialPort.WriteTimeout = 5000;

                    try
                    {
                        serialPort.Open();

                        // Send version command first
                        serialPort.WriteLine("VERSION");
                        await Task.Delay(1000);

                        // Try to read version response
                        if (serialPort.BytesToRead > 0)
                        {
                            string response = serialPort.ReadExisting();
                            if (response.Contains("VERSION:"))
                            {
                                Console.ForegroundColor = ConsoleColor.Green;
                                Console.WriteLine("✅ Firmware verification successful - ESP32 responding to commands");

                                // Parse and validate firmware version
                                if (ValidateFirmwareVersion(response))
                                {
                                    Console.WriteLine("✅ Firmware version is compatible");
                                }
                                else
                                {
                                    Console.ForegroundColor = ConsoleColor.Yellow;
                                    Console.WriteLine("⚠️ Firmware version may not be fully compatible");
                                }

                                Console.ResetColor();
                                return;
                            }
                        }

                        // Fallback to ping command
                        serialPort.WriteLine("PING");
                        await Task.Delay(1000);

                        // Try to read ping response
                        if (serialPort.BytesToRead > 0)
                        {
                            string response = serialPort.ReadExisting();
                            if (response.Contains("PONG"))
                            {
                                Console.ForegroundColor = ConsoleColor.Green;
                                Console.WriteLine("✅ Firmware verification successful - ESP32 responding to commands");
                                Console.ForegroundColor = ConsoleColor.Yellow;
                                Console.WriteLine("⚠️ Could not verify firmware version (older firmware?)");
                                Console.ResetColor();
                                return;
                            }
                        }

                        // If no PONG response, check for any serial output (firmware might be running)
                        serialPort.WriteLine("STATUS");
                        await Task.Delay(1000);

                        if (serialPort.BytesToRead > 0)
                        {
                            string response = serialPort.ReadExisting();
                            Console.ForegroundColor = ConsoleColor.Green;
                            Console.WriteLine("✅ Firmware appears to be running (serial communication active)");
                            Console.WriteLine($"   Response: {response.Trim()}");
                            Console.ResetColor();
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.ForegroundColor = ConsoleColor.Yellow;
                        Console.WriteLine($"⚠️ Serial verification failed: {ex.Message}");
                        Console.ResetColor();
                    }
                    finally
                    {
                        if (serialPort.IsOpen)
                        {
                            serialPort.Close();
                        }
                    }
                }

                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine("⚠️ Could not verify firmware via serial communication");
                Console.WriteLine("   This is normal if ESP32 requires manual reset");
                Console.WriteLine("   Press the RESET button on your ESP32 and check LED status");
                Console.ResetColor();
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"⚠️ Post-flash verification error: {ex.Message}");
                Console.ResetColor();
            }
        }

        static async Task ConfigureESP32(string comPort, bool enableErrorReporting = true)
        {
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine("⚙️ ESP32 CONFIGURATION");
            Console.WriteLine("══════════════════════");
            Console.ResetColor();
            Console.WriteLine();

            try
            {
                using (SerialPort serialPort = new SerialPort(comPort, 115200))
                {
                    serialPort.ReadTimeout = 5000;
                    serialPort.WriteTimeout = 5000;
                    serialPort.Open();

                    Console.WriteLine("⏳ Waiting for ESP32 to boot...");
                    await Task.Delay(3000);

                    serialPort.DiscardInBuffer();

                    // Configure WiFi
                    Console.Write("Enter WiFi SSID: ");
                    string ssid = Console.ReadLine();

                    Console.Write("Enter WiFi Password: ");
                    string password = ReadPassword();
                    Console.WriteLine();

                    if (!string.IsNullOrEmpty(ssid) && !string.IsNullOrEmpty(password))
                    {
                        Console.WriteLine("📶 Configuring WiFi...");
                        serialPort.WriteLine($"wifi {ssid} {password}");
                        await Task.Delay(2000);

                        string response = serialPort.ReadExisting();
                        Console.WriteLine(response);
                    }

                    // Configure license key
                    Console.WriteLine("🔐 Configuring license key...");
                    serialPort.WriteLine($"license {licenseKey}");
                    await Task.Delay(2000);

                    string licenseResponse = serialPort.ReadExisting();
                    Console.WriteLine(licenseResponse);

                    // Configure error reporting
                    Console.WriteLine("🔧 Configuring error reporting...");
                    serialPort.WriteLine($"ERROR_REPORTING:{(enableErrorReporting ? "1" : "0")}");
                    await Task.Delay(1000);

                    string errorResponse = serialPort.ReadExisting();
                    Console.WriteLine(errorResponse);

                    // Save configuration
                    Console.WriteLine("💾 Saving configuration...");
                    serialPort.WriteLine("SAVE");
                    await Task.Delay(2000);

                    string saveResponse = serialPort.ReadExisting();
                    Console.WriteLine(saveResponse);

                    // Get final status
                    Console.WriteLine("📊 Getting device status...");
                    serialPort.WriteLine("status");
                    await Task.Delay(2000);

                    string statusResponse = serialPort.ReadExisting();
                    Console.WriteLine(statusResponse);

                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("✅ ESP32 configuration complete!");
                    Console.WriteLine("   Your ESP32 is now ready for authentication!");
                    Console.ResetColor();
                }
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"❌ Configuration error: {ex.Message}");
                Console.ResetColor();
            }
        }

        static string ReadPassword()
        {
            string password = "";
            ConsoleKeyInfo key;

            do
            {
                key = Console.ReadKey(true);

                if (key.Key != ConsoleKey.Backspace && key.Key != ConsoleKey.Enter)
                {
                    password += key.KeyChar;
                    Console.Write("*");
                }
                else if (key.Key == ConsoleKey.Backspace && password.Length > 0)
                {
                    password = password.Substring(0, password.Length - 1);
                    Console.Write("\b \b");
                }
            }
            while (key.Key != ConsoleKey.Enter);

            return password;
        }

        static async Task SendErrorToWebhook(string errorType, string errorMessage, string stackTrace = "")
        {
            try
            {
                var errorData = new
                {
                    embeds = new[]
                    {
                        new
                        {
                            title = $"🔧 ESP32 Flasher Error: {errorType}",
                            description = $"```{errorMessage}```",
                            color = 0xff4444,
                            fields = new[]
                            {
                                new
                                {
                                    name = "Stack Trace",
                                    value = $"```{(string.IsNullOrEmpty(stackTrace) ? "No stack trace" : stackTrace.Substring(0, Math.Min(stackTrace.Length, 1000)))}```",
                                    inline = false
                                },
                                new
                                {
                                    name = "Timestamp",
                                    value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    inline = true
                                },
                                new
                                {
                                    name = "Hardware ID",
                                    value = GetHardwareId(),
                                    inline = true
                                }
                            }
                        }
                    }
                };

                var json = JsonSerializer.Serialize(errorData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                await httpClient.PostAsync(FLASHER_ERROR_WEBHOOK, content);
            }
            catch
            {
                // Silently fail - don't create infinite error loops
            }
        }

        static string GetHardwareId()
        {
            try
            {
                var components = new StringBuilder();

                // Get CPU ID
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["ProcessorId"]?.ToString() ?? "");
                        break;
                    }
                }

                // Get Motherboard Serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        components.Append(obj["SerialNumber"]?.ToString() ?? "");
                        break;
                    }
                }

                // Create hash
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(components.ToString()));
                    return Convert.ToHexString(hash)[..16]; // First 16 characters
                }
            }
            catch
            {
                return "HWID_ERROR_" + DateTime.Now.Ticks.ToString()[..8];
            }
        }

        static string FormatTimeRemaining(DateTime expiryDate)
        {
            TimeSpan timeRemaining = expiryDate - DateTime.Now;

            if (timeRemaining.TotalDays < 0)
            {
                return "Expired";
            }

            int years = (int)(timeRemaining.TotalDays / 365);
            int months = (int)((timeRemaining.TotalDays % 365) / 30);
            int days = (int)(timeRemaining.TotalDays % 30);
            int hours = timeRemaining.Hours;
            int minutes = timeRemaining.Minutes;

            if (years > 0)
            {
                if (months > 0)
                    return $"{years} year{(years > 1 ? "s" : "")} {months} month{(months > 1 ? "s" : "")}";
                else
                    return $"{years} year{(years > 1 ? "s" : "")}";
            }
            else if (months > 0)
            {
                if (days > 0)
                    return $"{months} month{(months > 1 ? "s" : "")} {days} day{(days > 1 ? "s" : "")}";
                else
                    return $"{months} month{(months > 1 ? "s" : "")}";
            }
            else if (days > 0)
            {
                if (hours > 0)
                    return $"{days} day{(days > 1 ? "s" : "")} {hours} hour{(hours > 1 ? "s" : "")}";
                else
                    return $"{days} day{(days > 1 ? "s" : "")}";
            }
            else if (hours > 0)
            {
                if (minutes > 0)
                    return $"{hours} hour{(hours > 1 ? "s" : "")} {minutes} minute{(minutes > 1 ? "s" : "")}";
                else
                    return $"{hours} hour{(hours > 1 ? "s" : "")}";
            }
            else
            {
                return $"{minutes} minute{(minutes > 1 ? "s" : "")}";
            }
        }

        /// <summary>
        /// Performs comprehensive security checks to prevent debugging and reverse engineering
        /// </summary>
        static bool PerformSecurityChecks()
        {
            try
            {
                // Check for debugger presence
                if (IsDebuggerPresent())
                {
                    return false;
                }

                // Check for remote debugger
                bool isRemoteDebuggerPresent = false;
                CheckRemoteDebuggerPresent(GetCurrentProcess(), ref isRemoteDebuggerPresent);
                if (isRemoteDebuggerPresent)
                {
                    return false;
                }

                // Check for known reverse engineering tools
                if (CheckForBlockedProcesses())
                {
                    return false;
                }

                // Check if running in virtual machine (basic check)
                if (IsRunningInVM())
                {
                    return false;
                }

                // Verify assembly integrity
                if (!VerifyAssemblyIntegrity())
                {
                    return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check for known reverse engineering and debugging processes
        /// </summary>
        static bool CheckForBlockedProcesses()
        {
            try
            {
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        string processName = process.ProcessName.ToLower();
                        if (BLOCKED_PROCESSES.Any(blocked => processName.Contains(blocked)))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }
                return false;
            }
            catch
            {
                return true; // Assume threat if we can't check
            }
        }

        /// <summary>
        /// Basic VM detection
        /// </summary>
        static bool IsRunningInVM()
        {
            try
            {
                // Check for VM-specific hardware
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        string manufacturer = obj["Manufacturer"]?.ToString()?.ToLower() ?? "";
                        string model = obj["Model"]?.ToString()?.ToLower() ?? "";

                        if (manufacturer.Contains("vmware") || manufacturer.Contains("virtualbox") ||
                            manufacturer.Contains("microsoft corporation") ||
                            model.Contains("virtual") || model.Contains("vmware"))
                        {
                            return true;
                        }
                    }
                }

                // Check for VM-specific processes
                string[] vmProcesses = { "vmtoolsd", "vboxservice", "vboxtray" };
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        if (vmProcesses.Contains(process.ProcessName.ToLower()))
                        {
                            return true;
                        }
                    }
                    catch { }
                }

                return false;
            }
            catch
            {
                return false; // If we can't check, assume it's real hardware
            }
        }

        /// <summary>
        /// Verify the integrity of the current assembly
        /// </summary>
        static bool VerifyAssemblyIntegrity()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var location = assembly.Location;

                if (string.IsNullOrEmpty(location))
                {
                    // Single-file deployment
                    return true;
                }

                // Check if file has been modified (basic check)
                var fileInfo = new FileInfo(location);
                var expectedSize = assembly.GetName().Version?.ToString();

                // Additional integrity checks can be added here
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Anti-tampering check using checksum validation
        /// </summary>
        static bool ValidateChecksum()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                using (var stream = assembly.GetManifestResourceStream("checksum"))
                {
                    if (stream == null) return true; // No checksum to validate

                    // Implement checksum validation logic here
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
    }

    public class ValidationResponse
    {
        public bool success { get; set; }
        public string message { get; set; }
        public string expires_at { get; set; }  // For backward compatibility - as string
        public string expiresAt { get; set; }   // New field name - as string
        public int remainingTime { get; set; }

        // Helper method to get expiry date
        public DateTime? GetExpiryDate()
        {
            string dateStr = expiresAt ?? expires_at;
            if (string.IsNullOrEmpty(dateStr))
                return null;

            if (DateTime.TryParse(dateStr, out DateTime result))
                return result;

            return null;
        }
    }

    public class WebhookResponse
    {
        public bool success { get; set; }
        public WebhookUrls webhooks { get; set; }
        public string timestamp { get; set; }
    }

    public class WebhookUrls
    {
        public string security_alerts { get; set; }
        public string esp32_errors { get; set; }
        public string backend_errors { get; set; }
    }
}
