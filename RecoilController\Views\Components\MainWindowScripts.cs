using System;

namespace RecoilController.Views.Components
{
    public static class MainWindowScripts
    {
        public static string GetJavaScriptContent()
        {
            return @"
    <script>
        // Window control functions


        // ESP32 communication functions
        function connectESP32() {
            const port = document.getElementById('esp32-port').value;
            window.chrome.webview.postMessage({ 
                action: 'connectESP32', 
                port: port 
            });
        }

        function updateESP32Status(connected) {
            const statusElement = document.getElementById('esp32-status');
            const connectButton = document.getElementById('connect-esp32');
            
            if (connected) {
                statusElement.textContent = 'Connected';
                statusElement.style.color = '#00ff88';
                connectButton.textContent = 'Disconnect ESP32';
                connectButton.classList.add('active');
            } else {
                statusElement.textContent = 'Disconnected';
                statusElement.style.color = '#ff4757';
                connectButton.textContent = 'Connect ESP32';
                connectButton.classList.remove('active');
            }
        }

        // Weapon configuration functions
        function updateWeaponConfig() {
            const config = {
                weapon: document.getElementById('primary-weapon').value,
                sight: document.getElementById('sight').value,
                muzzle: document.getElementById('muzzle').value,
                barrel: document.getElementById('barrel').value,
                recoilComp: document.getElementById('recoil-comp').value,
                humanization: document.getElementById('humanization').value,
                sensitivity: document.getElementById('sensitivity').value,
                adsSensitivity: document.getElementById('ads-sensitivity').value,
                fov: document.getElementById('fov').value,
                horizontal: document.getElementById('horizontal').value,
                vertical: document.getElementById('vertical').value,
                smoothing: document.getElementById('smoothing').value
            };

            window.chrome.webview.postMessage({ 
                action: 'updateWeaponConfig', 
                config: config 
            });
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    this.classList.add('active');
                    document.getElementById(targetTab + '-tab').classList.add('active');
                });
            });

            // Initialize sliders with real-time updates
            initializeSlider('recoil-comp', 'recoil-value', '%');
            initializeSlider('humanization', 'humanization-value', '%');
            initializeSlider('sensitivity', 'sens-value', '', 2);
            initializeSlider('ads-sensitivity', 'ads-value', '', 2);
            initializeSlider('fov', 'fov-value', '°');
            initializeSlider('horizontal', 'horizontal-value', '%');
            initializeSlider('vertical', 'vertical-value', '%');
            initializeSlider('smoothing', 'smoothing-value', '%');
            initializeSlider('afk-interval', 'afk-interval-value', 's');

            // Initialize toggle button
            const toggleBtn = document.getElementById('toggle-script');
            toggleBtn.addEventListener('click', function() {
                if (this.classList.contains('active')) {
                    this.classList.remove('active');
                    this.textContent = 'DISABLED';
                    window.chrome.webview.postMessage({ action: 'disableScript' });
                } else {
                    this.classList.add('active');
                    this.textContent = 'ENABLED';
                    window.chrome.webview.postMessage({ action: 'enableScript' });
                }
            });

            // ESP32 connection button
            document.getElementById('connect-esp32').addEventListener('click', connectESP32);

            // Weapon configuration change listeners
            document.getElementById('primary-weapon').addEventListener('change', updateWeaponConfig);
            document.getElementById('sight').addEventListener('change', updateWeaponConfig);
            document.getElementById('muzzle').addEventListener('change', updateWeaponConfig);
            document.getElementById('barrel').addEventListener('change', updateWeaponConfig);

            // Settings change listeners
            document.querySelectorAll('input[type=""range""]').forEach(slider => {
                slider.addEventListener('input', updateWeaponConfig);
            });

            // Feature toggles
            document.getElementById('cursor-check').addEventListener('change', function() {
                window.chrome.webview.postMessage({ 
                    action: 'toggleFeature', 
                    feature: 'cursorCheck', 
                    enabled: this.checked 
                });
            });

            document.getElementById('rapid-fire').addEventListener('change', function() {
                window.chrome.webview.postMessage({ 
                    action: 'toggleFeature', 
                    feature: 'rapidFire', 
                    enabled: this.checked 
                });
            });

            document.getElementById('anti-afk').addEventListener('change', function() {
                window.chrome.webview.postMessage({ 
                    action: 'toggleFeature', 
                    feature: 'antiAFK', 
                    enabled: this.checked 
                });
            });

            // Initialize with default configuration
            updateWeaponConfig();
        });

        function initializeSlider(sliderId, valueId, suffix, decimals = 0) {
            const slider = document.getElementById(sliderId);
            const valueDisplay = document.getElementById(valueId);
            
            slider.addEventListener('input', function() {
                let value = parseFloat(this.value);
                if (decimals > 0) {
                    value = value.toFixed(decimals);
                }
                valueDisplay.textContent = value + suffix;
            });
        }

        // Listen for messages from C# backend
        window.chrome.webview.addEventListener('message', function(event) {
            const data = event.data;
            
            switch(data.action) {
                case 'esp32StatusUpdate':
                    updateESP32Status(data.connected);
                    break;
                case 'weaponConfigLoaded':
                    loadWeaponConfig(data.config);
                    break;
                case 'showNotification':
                    showNotification(data.message, data.type);
                    break;
            }
        });

        function loadWeaponConfig(config) {
            if (config.weapon) document.getElementById('primary-weapon').value = config.weapon;
            if (config.sight) document.getElementById('sight').value = config.sight;
            if (config.muzzle) document.getElementById('muzzle').value = config.muzzle;
            if (config.barrel) document.getElementById('barrel').value = config.barrel;
            if (config.recoilComp) {
                document.getElementById('recoil-comp').value = config.recoilComp;
                document.getElementById('recoil-value').textContent = config.recoilComp + '%';
            }
            // Load other settings...
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? 'linear-gradient(135deg, #00ff88 0%, #00d4ff 100%)' : 
                           type === 'error' ? 'linear-gradient(135deg, #ff4757 0%, #c44569 100%)' : 
                           'linear-gradient(135deg, #4a90e2 0%, #357abd 100%)'};
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Keybind capture functionality
        let capturingElement = null;

        function captureKey(element) {
            if (capturingElement) {
                capturingElement.classList.remove('capturing');
            }

            capturingElement = element;
            element.classList.add('capturing');
            element.value = 'Press any key...';

            document.addEventListener('keydown', handleKeyCapture, { once: true });
        }

        function handleKeyCapture(event) {
            event.preventDefault();

            if (capturingElement) {
                const key = event.key;
                const keyName = getKeyName(key, event);

                capturingElement.value = keyName;
                capturingElement.classList.remove('capturing');

                // Send keybind update to backend
                window.chrome.webview.postMessage({
                    action: 'updateKeybind',
                    key: capturingElement.id,
                    value: keyName
                });

                capturingElement = null;
            }
        }

        function getKeyName(key, event) {
            const keyMap = {
                ' ': 'Space',
                'ArrowUp': 'Up',
                'ArrowDown': 'Down',
                'ArrowLeft': 'Left',
                'ArrowRight': 'Right',
                'Control': 'Ctrl',
                'Alt': 'Alt',
                'Shift': 'Shift',
                'Meta': 'Win',
                'Escape': 'Esc',
                'Enter': 'Enter',
                'Backspace': 'Backspace',
                'Delete': 'Del',
                'Tab': 'Tab'
            };

            if (keyMap[key]) return keyMap[key];
            if (key.startsWith('F') && key.length <= 3) return key;
            return key.toUpperCase();
        }

        // Loadout management
        function saveLoadout() {
            const name = document.getElementById('loadout-name').value;
            const weapon = document.getElementById('loadout-weapon').value;
            const sight = document.getElementById('loadout-sight').value;
            const key = document.getElementById('loadout-key').value;

            if (!name || !key) {
                showNotification('Please enter a loadout name and set a keybind', 'error');
                return;
            }

            const loadout = { name, weapon, sight, key };

            window.chrome.webview.postMessage({
                action: 'saveLoadout',
                loadout: loadout
            });

            addLoadoutToList(loadout);

            // Clear form
            document.getElementById('loadout-name').value = '';
            document.getElementById('loadout-key').value = '';

            showNotification('Loadout saved successfully', 'success');
        }

        function addLoadoutToList(loadout) {
            const list = document.getElementById('loadouts-list');
            const item = document.createElement('div');
            item.className = 'loadout-item';
            item.innerHTML = `
                <span class='loadout-name'>${loadout.name}</span>
                <span class='loadout-key'>${loadout.key}</span>
                <button class='loadout-delete' onclick='deleteLoadout(this)'>×</button>
            `;
            list.appendChild(item);
        }

        function deleteLoadout(button) {
            const item = button.closest('.loadout-item');
            const name = item.querySelector('.loadout-name').textContent;

            window.chrome.webview.postMessage({
                action: 'deleteLoadout',
                name: name
            });

            item.remove();
            showNotification('Loadout deleted', 'success');
        }

        // Initialize loadout functionality when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const saveBtn = document.getElementById('save-loadout');
            if (saveBtn) {
                saveBtn.addEventListener('click', saveLoadout);
            }
        });
    </script>";
        }
    }
}
