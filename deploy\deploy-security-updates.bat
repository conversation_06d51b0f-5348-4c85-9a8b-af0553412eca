@echo off
echo.
echo OCTANE SECURITY UPDATES - DEPLOYMENT
echo ====================================
echo.

set VPS_IP=*************
set VPS_USER=root
set LOCAL_PATH=%~dp0..
set REMOTE_PATH=/opt/octane-auth
set SSH_KEY=%~dp0octane_key

echo VPS Details:
echo    IP: %VPS_IP%
echo    User: %VPS_USER%
echo    Remote Path: %REMOTE_PATH%
echo.

set /p CONTINUE="Deploy security updates? (y/N): "
if /i not "%CONTINUE%"=="y" (
    echo Deployment cancelled.
    pause
    exit /b
)

echo.
echo STEP 1: Backing up current deployment...
echo =======================================

echo Creating backup...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd /opt && tar -czf octane-auth-backup-$(date +%%Y%%m%%d-%%H%%M%%S).tar.gz octane-auth"

if %ERRORLEVEL% NEQ 0 (
    echo Backup failed!
    pause
    exit /b 1
)

echo Backup created successfully!

echo.
echo STEP 2: Uploading updated files...
echo =================================

echo Uploading enhanced security routes...
scp -i "%SSH_KEY%" "%LOCAL_PATH%\routes\security.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/routes/

echo Uploading security models...
scp -i "%SSH_KEY%" "%LOCAL_PATH%\models\SecurityEvent.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/models/

echo Uploading admin routes...
scp -i "%SSH_KEY%" "%LOCAL_PATH%\routes\admin.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/routes/

echo Uploading public HTML files...
scp -i "%SSH_KEY%" "%LOCAL_PATH%\public\security-alerts.html" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/public/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\public\index.html" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/public/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\public\js\security-alerts.js" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/public/js/
scp -i "%SSH_KEY%" "%LOCAL_PATH%\public\css\admin.css" %VPS_USER%@%VPS_IP%:%REMOTE_PATH%/public/css/

if %ERRORLEVEL% NEQ 0 (
    echo Upload failed!
    pause
    exit /b 1
)

echo Files uploaded successfully!

echo.
echo STEP 3: Installing dependencies...
echo =================================

echo Installing any new dependencies...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "cd %REMOTE_PATH% && npm install --production"

if %ERRORLEVEL% NEQ 0 (
    echo Dependency installation failed!
    pause
    exit /b 1
)

echo Dependencies installed successfully!

echo.
echo STEP 4: Restarting application...
echo ================================

echo Stopping application...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 stop octane-auth"

echo Starting application with updates...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 start octane-auth"

echo Saving PM2 configuration...
ssh -i "%SSH_KEY%" %VPS_USER%@%VPS_IP% "sudo -u octane pm2 save"

if %ERRORLEVEL% NEQ 0 (
    echo Application restart failed!
    pause
    exit /b 1
)

echo Application restarted successfully!

echo.
echo STEP 5: Testing security endpoints...
echo ====================================

echo Testing health endpoint...
curl -s http://%VPS_IP%/api/health

echo.
echo Testing security stats endpoint...
curl -s http://%VPS_IP%/api/security/stats

echo.
echo Testing security log endpoint...
curl -X POST -H "Content-Type: application/json" -d "{\"type\":\"test\",\"severity\":\"low\",\"description\":\"Security update deployment test\"}" http://%VPS_IP%/api/security/log-event

echo.
echo.
echo SECURITY UPDATES DEPLOYED SUCCESSFULLY!
echo ======================================
echo.
echo Updated components:
echo    ✓ Enhanced security logging
echo    ✓ Comprehensive security alerts
echo    ✓ Discord webhook integration
echo    ✓ Admin panel security features
echo    ✓ Updated HTML interfaces
echo.
echo Your backend is running at:
echo    API: http://%VPS_IP%/api
echo    Admin: http://%VPS_IP%/admin
echo    Security Stats: http://%VPS_IP%/api/security/stats
echo.

echo Opening admin panel...
start http://%VPS_IP%/admin

pause
