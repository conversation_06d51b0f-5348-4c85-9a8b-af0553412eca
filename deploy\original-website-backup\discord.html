<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Management - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fab fa-discord"></i> Discord Management</h1>
                <p>Manage Discord bot, webhooks, and server integration</p>
            </div>

            <div class="dashboard-grid">
                <!-- Bot Status -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-robot"></i> Bot Status</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-indicator">
                            <div id="bot-status" class="status-dot offline"></div>
                            <span id="bot-status-text">Checking...</span>
                        </div>
                        <div class="bot-info">
                            <p><strong>Bot Name:</strong> Octane Security Bot</p>
                            <p><strong>Server:</strong> Octane Recoil Scripts</p>
                            <p><strong>Uptime:</strong> <span id="bot-uptime">--</span></p>
                        </div>
                        <div class="bot-actions">
                            <button id="restart-bot" class="btn btn-warning">
                                <i class="fas fa-redo"></i> Restart Bot
                            </button>
                            <button id="test-bot" class="btn btn-primary">
                                <i class="fas fa-vial"></i> Test Bot
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Commands -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-terminal"></i> Quick Commands</h3>
                    </div>
                    <div class="card-content">
                        <div class="command-buttons">
                            <button class="btn btn-secondary" onclick="sendDiscordCommand('stats')">
                                <i class="fas fa-chart-bar"></i> Send Stats
                            </button>
                            <button class="btn btn-secondary" onclick="sendDiscordCommand('health')">
                                <i class="fas fa-heartbeat"></i> Health Check
                            </button>
                            <button class="btn btn-secondary" onclick="sendDiscordCommand('users')">
                                <i class="fas fa-users"></i> User Count
                            </button>
                            <button class="btn btn-danger" onclick="sendDiscordCommand('emergency')">
                                <i class="fas fa-exclamation-triangle"></i> Emergency Disable
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Webhook Configuration -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-link"></i> Webhook Configuration</h3>
                    </div>
                    <div class="card-content">
                        <div class="webhook-list">
                            <div class="webhook-item">
                                <div class="webhook-info">
                                    <strong>Security Alerts</strong>
                                    <span class="webhook-status active">Active</span>
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="testWebhook('security')">
                                    <i class="fas fa-paper-plane"></i> Test
                                </button>
                            </div>
                            <div class="webhook-item">
                                <div class="webhook-info">
                                    <strong>ESP32 Errors</strong>
                                    <span class="webhook-status active">Active</span>
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="testWebhook('esp32')">
                                    <i class="fas fa-paper-plane"></i> Test
                                </button>
                            </div>
                            <div class="webhook-item">
                                <div class="webhook-info">
                                    <strong>Backend Errors</strong>
                                    <span class="webhook-status active">Active</span>
                                </div>
                                <button class="btn btn-sm btn-primary" onclick="testWebhook('backend')">
                                    <i class="fas fa-paper-plane"></i> Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Discord Activity -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Recent Discord Activity</h3>
                        <button id="refresh-activity" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="activity-list" id="discord-activity">
                            <div class="activity-item">
                                <div class="activity-icon success">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Security alert sent successfully</div>
                                    <div class="activity-time">2 minutes ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon info">
                                    <i class="fas fa-info"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Bot status check completed</div>
                                    <div class="activity-time">5 minutes ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">ESP32 connection timeout reported</div>
                                    <div class="activity-time">12 minutes ago</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Discord Commands -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-code"></i> Available Discord Commands</h3>
                    </div>
                    <div class="card-content">
                        <div class="commands-grid">
                            <div class="command-card">
                                <h4>/stats</h4>
                                <p>Display server statistics and user counts</p>
                            </div>
                            <div class="command-card">
                                <h4>/health</h4>
                                <p>Check system health and uptime</p>
                            </div>
                            <div class="command-card">
                                <h4>/users</h4>
                                <p>Show active user count and recent activity</p>
                            </div>
                            <div class="command-card">
                                <h4>/emergency</h4>
                                <p>Emergency disable authentication (admin only)</p>
                            </div>
                            <div class="command-card">
                                <h4>/logs</h4>
                                <p>Display recent system logs and errors</p>
                            </div>
                            <div class="command-card">
                                <h4>/restart</h4>
                                <p>Restart backend services (admin only)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/admin-modern.js"></script>
    <script>
        // Discord Management specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            checkBotStatus();
            loadDiscordActivity();
            
            // Bot control buttons
            document.getElementById('restart-bot').addEventListener('click', restartBot);
            document.getElementById('test-bot').addEventListener('click', testBot);
            document.getElementById('refresh-activity').addEventListener('click', loadDiscordActivity);
            
            // Auto-refresh bot status every 30 seconds
            setInterval(checkBotStatus, 30000);
        });

        async function checkBotStatus() {
            try {
                const response = await fetch('/api/discord/status');
                const result = await response.json();
                
                const statusDot = document.getElementById('bot-status');
                const statusText = document.getElementById('bot-status-text');
                const uptime = document.getElementById('bot-uptime');
                
                if (result.success && result.online) {
                    statusDot.className = 'status-dot online';
                    statusText.textContent = 'Online';
                    uptime.textContent = result.uptime || 'Unknown';
                } else {
                    statusDot.className = 'status-dot offline';
                    statusText.textContent = 'Offline';
                    uptime.textContent = '--';
                }
            } catch (error) {
                console.error('Error checking bot status:', error);
                document.getElementById('bot-status').className = 'status-dot offline';
                document.getElementById('bot-status-text').textContent = 'Error';
            }
        }

        async function restartBot() {
            if (!confirm('Are you sure you want to restart the Discord bot?')) return;
            
            try {
                const response = await fetch('/api/discord/restart', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Bot restart initiated', 'success');
                    setTimeout(checkBotStatus, 5000);
                } else {
                    showNotification('Failed to restart bot: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error restarting bot: ' + error.message, 'error');
            }
        }

        async function testBot() {
            try {
                const response = await fetch('/api/discord/test', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Bot test message sent successfully', 'success');
                } else {
                    showNotification('Bot test failed: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error testing bot: ' + error.message, 'error');
            }
        }

        async function sendDiscordCommand(command) {
            try {
                const response = await fetch('/api/discord/command', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ command })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`Command "${command}" executed successfully`, 'success');
                    loadDiscordActivity();
                } else {
                    showNotification(`Command failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification(`Error executing command: ${error.message}`, 'error');
            }
        }

        async function testWebhook(type) {
            try {
                const response = await fetch('/api/discord/webhook/test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(`${type} webhook test sent successfully`, 'success');
                } else {
                    showNotification(`Webhook test failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showNotification(`Error testing webhook: ${error.message}`, 'error');
            }
        }

        async function loadDiscordActivity() {
            try {
                const response = await fetch('/api/discord/logs?limit=10');
                const result = await response.json();
                
                const activityList = document.getElementById('discord-activity');
                
                if (result.success && result.logs.length > 0) {
                    activityList.innerHTML = result.logs.map(log => `
                        <div class="activity-item">
                            <div class="activity-icon ${log.success ? 'success' : 'error'}">
                                <i class="fas ${log.success ? 'fa-check' : 'fa-times'}"></i>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">${log.command || log.response || 'Discord activity'}</div>
                                <div class="activity-time">${new Date(log.created_at).toLocaleString()}</div>
                            </div>
                        </div>
                    `).join('');
                } else {
                    activityList.innerHTML = '<div class="empty-state">No recent Discord activity</div>';
                }
            } catch (error) {
                console.error('Error loading Discord activity:', error);
                document.getElementById('discord-activity').innerHTML = '<div class="error-state">Error loading activity</div>';
            }
        }

    </script>

    <script src="js/shared-utils.js"></script>
    <script>
        // Initialize shared components
        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
        });

        // Replace showNotification with AdminUtils.showNotification
        function showNotification(message, type) {
            AdminUtils.showNotification(message, type);
        }
    </script>
</body>
</html>
