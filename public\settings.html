<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Settings</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>

            <!-- General Settings -->
            <div class="card">
                <div class="card-header">
                    <h3>🔧 General Settings</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="systemName">System Name:</label>
                            <input type="text" id="systemName" class="form-control" value="Octane Recoil Scripts" placeholder="System name">
                        </div>
                        <div class="form-group">
                            <label for="maxLicenses">Max Licenses:</label>
                            <input type="number" id="maxLicenses" class="form-control" value="1000" placeholder="Maximum licenses">
                        </div>
                        <div class="form-group">
                            <label for="defaultDuration">Default License Duration:</label>
                            <select id="defaultDuration" class="form-control">
                                <option value="1day">1 Day</option>
                                <option value="1week">1 Week</option>
                                <option value="1month" selected>1 Month</option>
                                <option value="3months">3 Months</option>
                                <option value="6months">6 Months</option>
                                <option value="1year">1 Year</option>
                                <option value="lifetime">Lifetime</option>
                            </select>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="saveGeneralSettings()">
                        <i class="fas fa-save"></i> Save General Settings
                    </button>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card">
                <div class="card-header">
                    <h3>🔐 Security Settings</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="maxFailedAttempts">Max Failed Attempts:</label>
                            <input type="number" id="maxFailedAttempts" class="form-control" value="5" placeholder="Max failed login attempts">
                        </div>
                        <div class="form-group">
                            <label for="sessionTimeout">Session Timeout (minutes):</label>
                            <input type="number" id="sessionTimeout" class="form-control" value="60" placeholder="Session timeout">
                        </div>
                        <div class="form-group">
                            <label for="allowedIPs">Allowed Admin IPs:</label>
                            <textarea id="allowedIPs" class="form-control" rows="3" placeholder="**************&#10;127.0.0.1">**************
127.0.0.1</textarea>
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableRateLimit" checked> Enable Rate Limiting
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableSecurityLogs" checked> Enable Security Logging
                        </label>
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableHWIDBinding" checked> Enable HWID Binding
                        </label>
                    </div>
                    <button class="btn btn-primary" onclick="saveSecuritySettings()">
                        <i class="fas fa-shield-alt"></i> Save Security Settings
                    </button>
                </div>
            </div>

            <!-- Discord Configuration -->
            <div class="card">
                <div class="card-header">
                    <h3>🤖 Discord Configuration</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="discordToken">Bot Token:</label>
                            <input type="password" id="discordToken" class="form-control" placeholder="Discord bot token">
                            <small>Get this from Discord Developer Portal</small>
                        </div>
                        <div class="form-group">
                            <label for="guildId">Guild ID:</label>
                            <input type="text" id="guildId" class="form-control" placeholder="Discord server ID">
                        </div>
                    </div>

                    <h4 style="margin-top: 30px;">📡 Webhook Configuration</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="securityWebhook">Security Alerts Webhook:</label>
                            <input type="text" id="securityWebhook" class="form-control"
                                   placeholder="channelId/token">
                            <button class="btn btn-sm btn-secondary" onclick="testWebhook('security')" style="margin-top: 5px;">
                                🧪 Test Webhook
                            </button>
                        </div>
                        <div class="form-group">
                            <label for="esp32Webhook">ESP32 Errors Webhook:</label>
                            <input type="text" id="esp32Webhook" class="form-control"
                                   placeholder="channelId/token">
                            <button class="btn btn-sm btn-secondary" onclick="testWebhook('esp32')" style="margin-top: 5px;">
                                🧪 Test Webhook
                            </button>
                        </div>
                        <div class="form-group">
                            <label for="backendWebhook">Backend Errors Webhook:</label>
                            <input type="text" id="backendWebhook" class="form-control"
                                   placeholder="channelId/token">
                            <button class="btn btn-sm btn-secondary" onclick="testWebhook('backend')" style="margin-top: 5px;">
                                🧪 Test Webhook
                            </button>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="saveDiscordSettings()">
                        <i class="fas fa-robot"></i> Save Discord Settings
                    </button>
                </div>
            </div>

            <!-- Backup & Restore -->
            <div class="card">
                <div class="card-header">
                    <h3>💾 Backup & Restore</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <h4>📤 Export Data</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-secondary" onclick="exportLicenses()">
                                    <i class="fas fa-download"></i> Export Licenses
                                </button>
                                <button class="btn btn-secondary" onclick="exportSecurityLogs()">
                                    <i class="fas fa-download"></i> Export Security Logs
                                </button>
                                <button class="btn btn-secondary" onclick="exportFullBackup()">
                                    <i class="fas fa-download"></i> Full System Backup
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>📥 Import Data</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <input type="file" id="importFile" accept=".json,.sql" style="display: none;">
                                <button class="btn btn-warning" onclick="document.getElementById('importFile').click()">
                                    <i class="fas fa-upload"></i> Import Licenses
                                </button>
                                <button class="btn btn-warning" onclick="importSettings()">
                                    <i class="fas fa-upload"></i> Import Settings
                                </button>
                                <button class="btn btn-danger" onclick="restoreBackup()">
                                    <i class="fas fa-upload"></i> Restore Full Backup
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>🗑️ Maintenance</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-warning" onclick="cleanupExpired()">
                                    <i class="fas fa-trash"></i> Cleanup Expired
                                </button>
                                <button class="btn btn-warning" onclick="resetAllHWID()">
                                    <i class="fas fa-undo"></i> Reset All HWID
                                </button>
                                <button class="btn btn-danger" onclick="factoryReset()">
                                    <i class="fas fa-exclamation-triangle"></i> Factory Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                loadSettings();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // Settings management functions
        function loadSettings() {
            // Load current settings from server
            fetch('/api/settings')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        // Populate form fields with current settings
                        if (result.settings.general) {
                            document.getElementById('systemName').value = result.settings.general.systemName || 'Octane Recoil Scripts';
                            document.getElementById('maxLicenses').value = result.settings.general.maxLicenses || 1000;
                            document.getElementById('defaultDuration').value = result.settings.general.defaultDuration || '1month';
                        }
                        if (result.settings.security) {
                            document.getElementById('maxFailedAttempts').value = result.settings.security.maxFailedAttempts || 5;
                            document.getElementById('sessionTimeout').value = result.settings.security.sessionTimeout || 60;
                            document.getElementById('enableRateLimit').checked = result.settings.security.enableRateLimit !== false;
                            document.getElementById('enableSecurityLogs').checked = result.settings.security.enableSecurityLogs !== false;
                            document.getElementById('enableHWIDBinding').checked = result.settings.security.enableHWIDBinding !== false;
                        }
                        if (result.settings.discord) {
                            document.getElementById('guildId').value = result.settings.discord.guildId || '';
                            if (result.settings.discord.webhooks) {
                                document.getElementById('securityWebhook').value = result.settings.discord.webhooks.security || '';
                                document.getElementById('esp32Webhook').value = result.settings.discord.webhooks.esp32 || '';
                                document.getElementById('backendWebhook').value = result.settings.discord.webhooks.backend || '';
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading settings:', error);
                });
        }

        function saveGeneralSettings() {
            const settings = {
                systemName: document.getElementById('systemName').value,
                maxLicenses: parseInt(document.getElementById('maxLicenses').value),
                defaultDuration: document.getElementById('defaultDuration').value
            };

            fetch('/api/settings/general', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ General settings saved successfully', 'success');
                } else {
                    OctaneUtils.showAlert('❌ Failed to save settings: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error saving settings: ' + error.message, 'danger');
            });
        }

        function saveSecuritySettings() {
            const settings = {
                maxFailedAttempts: parseInt(document.getElementById('maxFailedAttempts').value),
                sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
                allowedIPs: document.getElementById('allowedIPs').value.split('\n').filter(ip => ip.trim()),
                enableRateLimit: document.getElementById('enableRateLimit').checked,
                enableSecurityLogs: document.getElementById('enableSecurityLogs').checked,
                enableHWIDBinding: document.getElementById('enableHWIDBinding').checked
            };

            fetch('/api/settings/security', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ Security settings saved successfully', 'success');
                } else {
                    OctaneUtils.showAlert('❌ Failed to save settings: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error saving settings: ' + error.message, 'danger');
            });
        }

        function saveDiscordSettings() {
            const settings = {
                token: document.getElementById('discordToken').value,
                guildId: document.getElementById('guildId').value,
                webhooks: {
                    security: document.getElementById('securityWebhook').value,
                    esp32: document.getElementById('esp32Webhook').value,
                    backend: document.getElementById('backendWebhook').value
                }
            };

            fetch('/api/settings/discord', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ Discord settings saved successfully', 'success');
                } else {
                    OctaneUtils.showAlert('❌ Failed to save settings: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error saving settings: ' + error.message, 'danger');
            });
        }

        function testWebhook(type) {
            const webhookMap = {
                security: document.getElementById('securityWebhook').value,
                esp32: document.getElementById('esp32Webhook').value,
                backend: document.getElementById('backendWebhook').value
            };

            const webhook = webhookMap[type];
            if (!webhook) {
                OctaneUtils.showAlert('❌ Please enter a webhook URL first', 'danger');
                return;
            }

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            btn.disabled = true;

            fetch('/api/discord/test-webhook', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ webhookType: type, webhookUrl: webhook })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert(`✅ ${type} webhook test successful!`, 'success');
                } else {
                    OctaneUtils.showAlert(`❌ ${type} webhook test failed: ` + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert(`❌ Error testing ${type} webhook: ` + error.message, 'danger');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function exportLicenses() {
            fetch('/api/export/licenses')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `licenses-export-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    OctaneUtils.showAlert('📊 Licenses exported successfully', 'success');
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error exporting licenses: ' + error.message, 'danger');
                });
        }

        function exportSecurityLogs() {
            fetch('/api/export/security-logs')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    OctaneUtils.showAlert('📊 Security logs exported successfully', 'success');
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error exporting security logs: ' + error.message, 'danger');
                });
        }

        function exportFullBackup() {
            OctaneUtils.showAlert('💾 Creating full backup...', 'info');

            fetch('/api/export/full-backup')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `octane-backup-${new Date().toISOString().split('T')[0]}.zip`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    OctaneUtils.showAlert('✅ Full backup created successfully', 'success');
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error creating backup: ' + error.message, 'danger');
                });
        }

        function importSettings() {
            OctaneUtils.showAlert('📥 Import settings feature coming soon...', 'info');
        }

        function restoreBackup() {
            if (!confirm('Restore from backup? This will overwrite all current data!')) return;
            OctaneUtils.showAlert('📥 Restore backup feature coming soon...', 'info');
        }

        function cleanupExpired() {
            if (!confirm('Delete all expired licenses?')) return;

            fetch('/api/admin/licenses/cleanup-expired', { method: 'DELETE' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert(`✅ Cleaned up ${result.deleted} expired licenses`, 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Failed to cleanup: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error during cleanup: ' + error.message, 'danger');
                });
        }

        function resetAllHWID() {
            if (!confirm('Reset HWID for ALL licenses? This will allow all users to rebind.')) return;
            OctaneUtils.showAlert('🔄 Reset all HWID feature coming soon...', 'info');
        }

        function factoryReset() {
            if (!confirm('FACTORY RESET? This will delete ALL data and cannot be undone!')) return;
            if (!confirm('Are you absolutely sure? This action is IRREVERSIBLE!')) return;
            OctaneUtils.showAlert('⚠️ Factory reset feature disabled for safety', 'warning');
        }
    </script>
</body>
</html>
