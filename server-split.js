const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-hashes'", "https://www.gstatic.com", "https://apis.google.com"],
            scriptSrcAttr: ["'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://identitytoolkit.googleapis.com", "https://securetoken.googleapis.com"],
        },
    },
    crossOriginOpenerPolicy: false,
    crossOriginEmbedderPolicy: false
}));

app.use(cors({
    origin: ['http://localhost:3000', 'http://*************:3000'],
    credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Database setup
const dbPath = path.join(__dirname, 'octane_auth.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Database connection error:', err);
        process.exit(1);
    }
    console.log('✅ Connected to SQLite database');
});

// Initialize database tables
db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS licenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        duration TEXT NOT NULL,
        expires_at INTEGER NOT NULL,
        hardware_id TEXT,
        is_active INTEGER DEFAULT 1,
        created_at INTEGER DEFAULT (strftime('%s', 'now')),
        notes TEXT
    )`);
    
    db.run(`CREATE TABLE IF NOT EXISTS security_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        timestamp INTEGER DEFAULT (strftime('%s', 'now')),
        ip_address TEXT,
        event_type TEXT,
        details TEXT,
        severity TEXT DEFAULT 'info'
    )`);
    
    console.log('✅ Database tables initialized');
});

// Utility functions
function sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.replace(/[<>'"&]/g, '').trim().substring(0, 255);
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const segments = [];
    for (let i = 0; i < 3; i++) {
        let segment = '';
        for (let j = 0; j < 3; j++) {
            segment += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        segments.push(segment);
    }
    return segments.join('-');
}

function calculateExpiryDate(duration) {
    const now = Date.now();
    const durationMap = {
        '1 hour': 60 * 60 * 1000,
        '1 day': 24 * 60 * 60 * 1000,
        '1 week': 7 * 24 * 60 * 60 * 1000,
        '1 month': 30 * 24 * 60 * 60 * 1000,
        '3 months': 90 * 24 * 60 * 60 * 1000,
        '6 months': 180 * 24 * 60 * 60 * 1000,
        '1 year': 365 * 24 * 60 * 60 * 1000,
        'lifetime': 50 * 365 * 24 * 60 * 60 * 1000
    };
    
    const durationMs = durationMap[duration.toLowerCase()] || durationMap['1 month'];
    return now + durationMs;
}

function logSecurityEvent(ip, eventType, details, severity = 'info') {
    db.run(
        `INSERT INTO security_logs (ip_address, event_type, details, severity) VALUES (?, ?, ?, ?)`,
        [ip, eventType, details, severity],
        (err) => {
            if (err) console.error('Security log error:', err);
        }
    );
}

// Rate limiting
const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // 3 requests per window
    message: {
        success: false,
        message: 'Too many authentication attempts. Try again in 15 minutes.',
        retryAfter: Math.floor(Date.now() / 1000) + (15 * 60)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
        // Skip rate limiting for health checks and admin from localhost
        return req.path === '/api/health' || 
               (req.path.startsWith('/api/admin') && req.ip === '127.0.0.1');
    }
});

// Apply rate limiting to API routes
app.use('/api/validate', apiLimiter);

// Store utilities in app.locals for routes to access
app.locals.db = db;
app.locals.sanitizeInput = sanitizeInput;
app.locals.generateLicenseKey = generateLicenseKey;
app.locals.calculateExpiryDate = calculateExpiryDate;
app.locals.logSecurityEvent = logSecurityEvent;

// Load route modules
const mainRoutes = require('./routes/main-routes');
const adminRoutes = require('./routes/admin-routes');
const webhookRoutes = require('./routes/webhook-routes');

// Use routes
app.use('/', mainRoutes);
app.use('/', adminRoutes);
app.use('/', webhookRoutes);

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Octane Auth Server running on port ${PORT}`);
    console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🌐 Admin panel: http://localhost:${PORT}/admin`);
});
