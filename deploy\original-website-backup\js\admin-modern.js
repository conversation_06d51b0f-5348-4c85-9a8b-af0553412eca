// Modern Admin Panel JavaScript
class AdminPanel {
    constructor() {
        this.currentSection = 'overview';
        this.stats = {
            totalLicenses: 0,
            activeUsers: 0,
            securityAlerts: 0,
            serverUptime: '99.9%'
        };
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateTime();
        this.loadDashboardData();
        this.startPeriodicUpdates();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');

                // If it's an external link (contains .html), navigate to it
                if (href && href.includes('.html')) {
                    window.location.href = href;
                    return;
                }

                // Otherwise handle internal navigation
                e.preventDefault();
                if (href && href.startsWith('#')) {
                    const section = href.substring(1);
                    this.switchSection(section);
                } else if (link.dataset.section) {
                    this.switchSection(link.dataset.section);
                }
            });
        });

        // Mobile menu toggle (if needed)
        document.addEventListener('click', (e) => {
            if (e.target.matches('.mobile-menu-btn')) {
                document.querySelector('.sidebar').classList.toggle('open');
            }
        });

        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.matches('.modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    switchSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).parentElement.classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');

        // Update header
        this.updateHeader(sectionName);

        // Load section content
        this.loadSectionContent(sectionName);

        this.currentSection = sectionName;
    }

    updateHeader(sectionName) {
        const titles = {
            overview: { title: 'Dashboard Overview', subtitle: 'Monitor and manage your Octane Recoil Scripts system' },
            licenses: { title: 'License Management', subtitle: 'Create, manage, and monitor license keys' },
            users: { title: 'User Management', subtitle: 'Manage user accounts and permissions' },
            discord: { title: 'Discord Management', subtitle: 'Configure Discord bot and webhooks' },
            security: { title: 'Security Alerts', subtitle: 'Monitor security events and threats' },
            system: { title: 'System Status', subtitle: 'Monitor server health and performance' },
            settings: { title: 'Settings', subtitle: 'Configure system settings and preferences' }
        };

        const headerInfo = titles[sectionName] || titles.overview;
        document.getElementById('page-title').textContent = headerInfo.title;
        document.getElementById('page-subtitle').textContent = headerInfo.subtitle;
    }

    async loadSectionContent(sectionName) {
        const section = document.getElementById(`${sectionName}-section`);
        
        if (sectionName === 'overview') {
            return; // Overview is already loaded
        }

        // Show loading state
        section.innerHTML = `
            <div class="section-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading ${sectionName} management...</p>
            </div>
        `;

        try {
            // Simulate loading delay
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Load section content based on type
            switch (sectionName) {
                case 'licenses':
                    section.innerHTML = this.getLicenseManagementHTML();
                    this.initLicenseManagement();
                    break;
                case 'users':
                    section.innerHTML = this.getUserManagementHTML();
                    this.initUserManagement();
                    break;
                case 'discord':
                    section.innerHTML = this.getDiscordManagementHTML();
                    this.initDiscordManagement();
                    break;
                case 'security':
                    section.innerHTML = this.getSecurityAlertsHTML();
                    this.initSecurityAlerts();
                    break;
                case 'system':
                    section.innerHTML = this.getSystemStatusHTML();
                    this.initSystemStatus();
                    break;
                case 'settings':
                    section.innerHTML = this.getSettingsHTML();
                    this.initSettings();
                    break;
            }
        } catch (error) {
            section.innerHTML = `
                <div class="section-loading">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error loading ${sectionName} management</p>
                </div>
            `;
        }
    }

    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { 
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }

    async loadDashboardData() {
        try {
            // Use the new dashboard stats endpoint
            const response = await fetch('/api/dashboard/stats');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.stats.totalLicenses = data.stats.totalLicenses || 0;
                    this.stats.activeUsers = data.stats.activeUsers || 0;
                    this.stats.securityAlerts = data.stats.securityAlerts || 0;
                    this.stats.serverUptime = data.stats.serverUptime || '0h';
                } else {
                    throw new Error('Failed to get dashboard stats');
                }
            } else {
                // Fallback to individual API calls
                const [licenses, users] = await Promise.all([
                    this.fetchStats('/api/licenses/count'),
                    this.fetchStats('/api/users/active')
                ]);

                this.stats.totalLicenses = licenses || 0;
                this.stats.activeUsers = users || 0;
                this.stats.securityAlerts = 0;
                this.stats.serverUptime = '0h';
            }

            this.updateStatsDisplay();
            this.loadRecentActivity();
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            // Set default values on error
            this.stats.totalLicenses = 0;
            this.stats.activeUsers = 0;
            this.stats.securityAlerts = 0;
            this.stats.serverUptime = '0h';
            this.updateStatsDisplay();
            this.showToast('Failed to load dashboard data', 'error');
        }
    }

    async fetchStats(endpoint) {
        try {
            const response = await fetch(endpoint);
            if (response.ok) {
                const data = await response.json();
                return data.success ? (data.count || data.value || 0) : 0;
            } else {
                console.warn(`API endpoint ${endpoint} returned ${response.status}`);
                return 0;
            }
        } catch (error) {
            console.error(`Failed to fetch ${endpoint}:`, error);
            return 0;
        }
    }

    updateStatsDisplay() {
        document.getElementById('total-licenses').textContent = this.stats.totalLicenses;
        document.getElementById('active-users').textContent = this.stats.activeUsers;
        document.getElementById('security-alerts').textContent = this.stats.securityAlerts;
        document.getElementById('server-uptime').textContent = this.stats.serverUptime;
    }

    async loadRecentActivity() {
        try {
            // Try to load real activity from API
            const response = await fetch('/api/admin/recent-activity');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.activities) {
                    this.displayActivity(data.activities);
                    return;
                }
            }
        } catch (error) {
            console.log('Using fallback activity data');
        }

        // Fallback to sample data with real timestamps
        const now = new Date();
        const activities = [
            {
                type: 'success',
                icon: 'fas fa-key',
                title: `License SDK-E93-9IH created successfully`,
                time: new Date(now - 2 * 60000).toLocaleTimeString()
            },
            {
                type: 'info',
                icon: 'fab fa-discord',
                title: 'Discord bot health check completed',
                time: new Date(now - 15 * 60000).toLocaleTimeString()
            },
            {
                type: 'success',
                icon: 'fas fa-user-check',
                title: 'User authentication successful',
                time: new Date(now - 23 * 60000).toLocaleTimeString()
            },
            {
                type: 'info',
                icon: 'fas fa-database',
                title: 'Database connection established',
                time: new Date(now - 60 * 60000).toLocaleTimeString()
            },
            {
                type: 'success',
                icon: 'fas fa-server',
                title: 'Server started successfully',
                time: new Date(now - 2 * 60 * 60000).toLocaleTimeString()
            }
        ];

        this.displayActivity(activities);
    }

    displayActivity(activities) {
        const activityList = document.getElementById('activity-list');
        if (activityList) {
            activityList.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">
                        <i class="${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }
    }

    startPeriodicUpdates() {
        // Update time every second
        setInterval(() => this.updateTime(), 1000);

        // Update dashboard data every 30 seconds
        setInterval(() => this.loadDashboardData(), 30000);

        // Update system metrics every 10 seconds
        setInterval(() => this.updateSystemMetrics(), 10000);
    }

    async updateSystemMetrics() {
        try {
            const response = await fetch('/api/system/stats');
            if (response.ok) {
                const data = await response.json();
                const stats = data.stats;

                // Calculate memory usage percentage
                const memoryUsage = Math.round((stats.memory.heapUsed / stats.memory.heapTotal) * 100);

                // Simulate CPU and disk usage (real implementation would require system monitoring)
                const cpuUsage = Math.floor(Math.random() * 40) + 20;
                const diskUsage = Math.floor(Math.random() * 20) + 20;

                const metrics = document.querySelectorAll('.metric-fill');
                if (metrics.length >= 3) {
                    metrics[0].style.width = `${cpuUsage}%`;
                    metrics[1].style.width = `${memoryUsage}%`;
                    metrics[2].style.width = `${diskUsage}%`;

                    const values = document.querySelectorAll('.metric-value');
                    if (values.length >= 3) {
                        values[0].textContent = `${cpuUsage}%`;
                        values[1].textContent = `${memoryUsage}%`;
                        values[2].textContent = `${diskUsage}%`;
                    }
                }

                // Update uptime
                const uptimeHours = Math.floor(stats.uptime / 3600);
                const uptimeDays = Math.floor(uptimeHours / 24);
                this.stats.serverUptime = `${uptimeDays}d ${uptimeHours % 24}h`;
                document.getElementById('server-uptime').textContent = this.stats.serverUptime;
            }
        } catch (error) {
            console.error('Failed to fetch system stats:', error);
            // Fallback to simulated metrics
            const cpuUsage = Math.floor(Math.random() * 40) + 20;
            const memoryUsage = Math.floor(Math.random() * 30) + 50;
            const diskUsage = Math.floor(Math.random() * 20) + 20;

            const metrics = document.querySelectorAll('.metric-fill');
            if (metrics.length >= 3) {
                metrics[0].style.width = `${cpuUsage}%`;
                metrics[1].style.width = `${memoryUsage}%`;
                metrics[2].style.width = `${diskUsage}%`;

                const values = document.querySelectorAll('.metric-value');
                if (values.length >= 3) {
                    values[0].textContent = `${cpuUsage}%`;
                    values[1].textContent = `${memoryUsage}%`;
                    values[2].textContent = `${diskUsage}%`;
                }
            }
        }
    }

    showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.75rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.getElementById('toast-container').appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    showLoading() {
        document.getElementById('loading-overlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loading-overlay').classList.remove('active');
    }

    openModal(modalId) {
        document.getElementById(modalId).classList.add('active');
    }

    closeModal(modalId) {
        document.getElementById(modalId).classList.remove('active');
    }

    // Placeholder methods for section content
    getLicenseManagementHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>License Management</h3>
                    <button class="btn btn-primary" onclick="adminPanel.createLicense()">
                        <i class="fas fa-plus"></i>
                        Create License
                    </button>
                </div>
                <div class="card-content">
                    <p>License management functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    getUserManagementHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>User Management</h3>
                </div>
                <div class="card-content">
                    <p>User management functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    getDiscordManagementHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Discord Management</h3>
                </div>
                <div class="card-content">
                    <p>Discord management functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    getSecurityAlertsHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Security Alerts</h3>
                </div>
                <div class="card-content">
                    <p>Security alerts functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    getSystemStatusHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>System Status</h3>
                </div>
                <div class="card-content">
                    <p>System status functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    getSettingsHTML() {
        return `
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Settings</h3>
                </div>
                <div class="card-content">
                    <p>Settings functionality will be implemented here.</p>
                </div>
            </div>
        `;
    }

    // Placeholder methods for actions
    initLicenseManagement() {}
    initUserManagement() {}
    initDiscordManagement() {}
    initSecurityAlerts() {}
    initSystemStatus() {}
    initSettings() {}
}

// Global action functions
function refreshActivity() {
    adminPanel.loadRecentActivity();
    adminPanel.showToast('Activity refreshed', 'success');
}

function createLicense() {
    adminPanel.showToast('Create license functionality coming soon', 'info');
}

function viewLogs() {
    adminPanel.showToast('View logs functionality coming soon', 'info');
}

function testDiscord() {
    adminPanel.showToast('Testing Discord connection...', 'info');
    setTimeout(() => {
        adminPanel.showToast('Discord test successful', 'success');
    }, 2000);
}

function maintenanceMode() {
    if (confirm('Enable maintenance mode? This will temporarily disable the system.')) {
        adminPanel.showToast('Maintenance mode enabled', 'warning');
    }
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        window.location.href = '/login';
    }
}

// Initialize admin panel when DOM is loaded
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});

// Add slideOut animation to CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Global functions for button handlers
async function resetHWID(licenseKey) {
    if (!confirm('Are you sure you want to reset the hardware ID for this license?')) return;

    try {
        const response = await fetch('/api/admin/reset-hwid', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `licenseKey=${licenseKey}`
        });

        const data = await response.json();

        if (data.success) {
            adminPanel.showToast('Hardware ID reset successfully!', 'success');
            if (adminPanel.loadLicenses) adminPanel.loadLicenses();
        } else {
            adminPanel.showToast('Failed to reset hardware ID: ' + data.message, 'error');
        }
    } catch (error) {
        adminPanel.showToast('Error resetting hardware ID: ' + error.message, 'error');
    }
}

async function deleteLicense(licenseKey) {
    if (!confirm('Are you sure you want to delete this license? This action cannot be undone.')) return;

    try {
        const response = await fetch('/api/admin/delete-license', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: `licenseKey=${licenseKey}`
        });

        const data = await response.json();

        if (data.success) {
            adminPanel.showToast('License deleted successfully!', 'success');
            if (adminPanel.loadDashboardData) adminPanel.loadDashboardData();
        } else {
            adminPanel.showToast('Failed to delete license: ' + data.message, 'error');
        }
    } catch (error) {
        adminPanel.showToast('Error deleting license: ' + error.message, 'error');
    }
}

function searchUser() {
    const searchTerm = document.getElementById('user-search').value;
    if (searchTerm) {
        adminPanel.showToast(`Searching for user: ${searchTerm}`, 'info');
    }
}

function banUser(licenseKey) {
    if (!confirm('Are you sure you want to ban this user?')) return;
    adminPanel.showToast(`User with license ${licenseKey} has been banned`, 'warning');
}

function restartBot() {
    if (!confirm('Are you sure you want to restart the Discord bot?')) return;

    fetch('/api/discord/restart', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminPanel.showToast('Discord bot restart initiated', 'success');
            } else {
                adminPanel.showToast('Failed to restart bot: ' + data.message, 'error');
            }
        })
        .catch(error => {
            adminPanel.showToast('Error restarting bot: ' + error.message, 'error');
        });
}

function testBot() {
    fetch('/api/discord/test', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminPanel.showToast('Discord bot test successful!', 'success');
            } else {
                adminPanel.showToast('Discord bot test failed: ' + data.message, 'error');
            }
        })
        .catch(error => {
            adminPanel.showToast('Discord bot test error: ' + error.message, 'error');
        });
}

function sendDiscordCommand(command) {
    fetch('/api/discord/command', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `command=${command}`
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminPanel.showToast(`Discord command "${command}" executed successfully`, 'success');
            } else {
                adminPanel.showToast(`Failed to execute command: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            adminPanel.showToast(`Error executing command: ${error.message}`, 'error');
        });
}

function testWebhook(type) {
    fetch('/api/discord/webhook/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `type=${type}`
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminPanel.showToast(`${type} webhook test successful!`, 'success');
            } else {
                adminPanel.showToast(`Webhook test failed: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            adminPanel.showToast(`Webhook test error: ${error.message}`, 'error');
        });
}

function clearAllAlerts() {
    if (!confirm('Are you sure you want to clear all security alerts?')) return;

    fetch('/api/security/events', { method: 'DELETE' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                adminPanel.showToast('All security alerts cleared', 'success');
            } else {
                adminPanel.showToast('Failed to clear alerts: ' + data.message, 'error');
            }
        })
        .catch(error => {
            adminPanel.showToast('Error clearing alerts: ' + error.message, 'error');
        });
}

function markAllRead() {
    adminPanel.showToast('All alerts marked as read', 'success');
}

function exportAlerts() {
    adminPanel.showToast('Exporting security alerts...', 'info');
}

function refreshAlerts() {
    adminPanel.showToast('Security alerts refreshed', 'success');
}

function restartServer() {
    if (!confirm('Are you sure you want to restart the server? This will cause temporary downtime.')) return;
    adminPanel.showToast('Server restart initiated', 'warning');
}

function clearLogs() {
    if (!confirm('Are you sure you want to clear all system logs?')) return;
    adminPanel.showToast('System logs cleared', 'success');
}

function backupDatabase() {
    adminPanel.showToast('Database backup initiated', 'info');
}

function emergencyMode() {
    if (!confirm('Are you sure you want to enable emergency mode? This will disable all authentication.')) return;
    adminPanel.showToast('Emergency mode activated', 'warning');
}

function saveGeneralSettings() {
    adminPanel.showToast('General settings saved successfully', 'success');
}

function saveSecuritySettings() {
    adminPanel.showToast('Security settings saved successfully', 'success');
}

function saveDiscordSettings() {
    adminPanel.showToast('Discord settings saved successfully', 'success');
}

function backupNow() {
    adminPanel.showToast('Database backup started', 'info');
}

function optimizeDatabase() {
    adminPanel.showToast('Database optimization started', 'info');
}

function resetDatabase() {
    if (!confirm('Are you sure you want to reset the database? This will delete ALL data and cannot be undone!')) return;
    adminPanel.showToast('Database reset initiated', 'warning');
}
