# ESP32-S2 Firmware Libraries and Dependencies

## Required Libraries

### Core ESP32 Libraries (Built-in)
These libraries are included with the ESP32 Arduino core:

#### USB & HID Support
- **USB.h** - Core USB functionality for ESP32-S2
- **USBHIDMouse.h** - HID Mouse implementation
- **USBCDC.h** - USB CDC (Serial) communication
- **esp32-hal-gpio.h** - GPIO control for LED

#### System Libraries
- **Arduino.h** - Core Arduino framework
- **WiFi.h** - WiFi functionality (for future features)
- **EEPROM.h** - Non-volatile storage
- **esp_system.h** - System utilities

#### FreeRTOS Libraries
- **freertos/FreeRTOS.h** - Real-time operating system
- **freertos/task.h** - Task management
- **freertos/queue.h** - Command queue implementation
- **freertos/semphr.h** - Synchronization primitives

### External Libraries (Install via Library Manager)

#### JSON Processing
**ArduinoJson** (v6.21.0 or later)
- **Purpose**: Parse JSON commands from desktop app
- **Installation**: Arduino IDE → Tools → Manage Libraries → Search "ArduinoJson"
- **Usage**: Command parsing, response generation
- **Memory**: ~8KB RAM, ~20KB Flash
- **Documentation**: https://arduinojson.org/

```cpp
#include <ArduinoJson.h>

// Example usage
StaticJsonDocument<512> doc;
deserializeJson(doc, jsonString);
String type = doc["Type"];
```

#### Optional Libraries

**ESP32TimerInterrupt** (for precise timing)
- **Purpose**: Hardware timer for exact 1ms intervals
- **Installation**: Library Manager → Search "ESP32TimerInterrupt"
- **Usage**: High-precision command queue processing
- **Alternative**: Use built-in `hw_timer_t`

## Development Environment Dependencies

### Arduino IDE Setup
1. **ESP32 Board Package**
   - URL: `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json`
   - Version: 2.0.11 or later
   - Includes: USB HID support, ESP32-S2 board definitions

2. **Board Configuration**
   ```
   Board: ESP32S2 Dev Module
   Upload Speed: 921600
   CPU Frequency: 240MHz
   Flash Size: 4MB (32Mb)
   Partition Scheme: Default 4MB with spiffs (1.2MB APP/1.5MB SPIFFS)
   PSRAM: Enabled
   USB Mode: Hardware CDC and JTAG
   USB CDC On Boot: Enabled
   USB Firmware MSC On Boot: Disabled
   USB DFU On Boot: Disabled
   Upload Mode: UART0 / Hardware CDC
   ```

### PlatformIO Setup (Alternative)
If using PlatformIO instead of Arduino IDE:

**platformio.ini**
```ini
[env:esp32-s2-saola-1]
platform = espressif32
board = esp32-s2-saola-1
framework = arduino
monitor_speed = 115200
upload_speed = 921600

; Build flags
build_flags = 
    -DARDUINO_USB_MODE=1
    -DARDUINO_USB_CDC_ON_BOOT=1
    -DCORE_DEBUG_LEVEL=3

; Libraries
lib_deps = 
    bblanchon/ArduinoJson@^6.21.0

; Board configuration
board_build.partitions = default.csv
board_build.filesystem = spiffs
board_upload.flash_size = 4MB
```

## Memory Requirements

### Flash Memory Usage
- **Arduino Core**: ~1.2MB
- **USB HID Libraries**: ~100KB
- **ArduinoJson**: ~20KB
- **Application Code**: ~50KB
- **SPIFFS (Optional)**: ~1.5MB
- **Total Used**: ~1.4MB / 4MB available

### RAM Usage
- **Arduino Core**: ~50KB
- **USB HID Stack**: ~20KB
- **Command Queue (64 items)**: ~2KB
- **JSON Buffer**: ~1KB
- **Serial Buffers**: ~2KB
- **Application Variables**: ~5KB
- **Total Used**: ~80KB / 320KB available

### PSRAM Usage (2MB available)
- **Large Buffers**: Store large data structures
- **Pattern Storage**: Recoil patterns for multiple weapons
- **Debug Logging**: Circular log buffer
- **Future Features**: Image processing, ML models

## Library Compatibility Matrix

| Library | Arduino IDE | PlatformIO | ESP-IDF | Notes |
|---------|-------------|------------|---------|-------|
| USB.h | ✅ | ✅ | ❌ | Arduino framework only |
| USBHIDMouse.h | ✅ | ✅ | ❌ | Arduino framework only |
| ArduinoJson | ✅ | ✅ | ✅ | Cross-platform |
| FreeRTOS | ✅ | ✅ | ✅ | Built into ESP32 |
| WiFi.h | ✅ | ✅ | ❌ | Arduino framework only |

## USB HID Implementation Details

### HID Report Descriptor
The ESP32-S2 uses this HID descriptor for mouse functionality:
```cpp
// Built into USBHIDMouse library
static const uint8_t report_descriptor[] = {
    0x05, 0x01,        // Usage Page (Generic Desktop Ctrls)
    0x09, 0x02,        // Usage (Mouse)
    0xA1, 0x01,        // Collection (Application)
    0x09, 0x01,        //   Usage (Pointer)
    0xA1, 0x00,        //   Collection (Physical)
    0x05, 0x09,        //     Usage Page (Button)
    0x19, 0x01,        //     Usage Minimum (0x01)
    0x29, 0x03,        //     Usage Maximum (0x03)
    0x15, 0x00,        //     Logical Minimum (0)
    0x25, 0x01,        //     Logical Maximum (1)
    0x95, 0x03,        //     Report Count (3)
    0x75, 0x01,        //     Report Size (1)
    0x81, 0x02,        //     Input (Data,Var,Abs)
    0x95, 0x01,        //     Report Count (1)
    0x75, 0x05,        //     Report Size (5)
    0x81, 0x03,        //     Input (Const,Var,Abs)
    0x05, 0x01,        //     Usage Page (Generic Desktop Ctrls)
    0x09, 0x30,        //     Usage (X)
    0x09, 0x31,        //     Usage (Y)
    0x15, 0x81,        //     Logical Minimum (-127)
    0x25, 0x7F,        //     Logical Maximum (127)
    0x75, 0x08,        //     Report Size (8)
    0x95, 0x02,        //     Report Count (2)
    0x81, 0x06,        //     Input (Data,Var,Rel)
    0xC0,              //   End Collection
    0xC0,              // End Collection
};
```

### HID Report Structure
```cpp
typedef struct {
    uint8_t buttons;   // Bit 0: Left, Bit 1: Right, Bit 2: Middle
    int8_t x;          // X movement (-127 to 127)
    int8_t y;          // Y movement (-127 to 127)
} __attribute__((packed)) hid_mouse_report_t;
```

## Alternative Implementations

### ESP-IDF Native (Not Recommended)
If using ESP-IDF instead of Arduino:

**Required Components**:
- `tinyusb` - USB HID implementation
- `esp_timer` - High-precision timing
- `nvs_flash` - Non-volatile storage
- `json` - JSON parsing (cJSON)

**CMakeLists.txt**:
```cmake
idf_component_register(
    SRCS "main.c"
    INCLUDE_DIRS "."
    REQUIRES tinyusb esp_timer nvs_flash json
)
```

**Issues with ESP-IDF**:
- Complex TinyUSB configuration
- Manual USB descriptor setup
- More complex build system
- Limited documentation
- Longer development time

### Arduino Libraries vs ESP-IDF

| Feature | Arduino | ESP-IDF |
|---------|---------|---------|
| Development Speed | Fast | Slow |
| USB HID Setup | 2 lines | 50+ lines |
| JSON Parsing | ArduinoJson | cJSON |
| Documentation | Excellent | Technical |
| Community Support | Large | Smaller |
| Learning Curve | Easy | Steep |

## Troubleshooting Dependencies

### Common Issues

#### 1. USB HID Not Working
**Symptoms**: ESP32 not recognized as mouse
**Solutions**:
- Verify board configuration (USB Mode: Hardware CDC and JTAG)
- Check USB cable (must support data)
- Ensure `USB.begin()` is called before `Mouse.begin()`
- Try different USB port

#### 2. ArduinoJson Compilation Errors
**Symptoms**: JSON parsing fails to compile
**Solutions**:
- Update to ArduinoJson v6.21.0+
- Check memory allocation (use StaticJsonDocument)
- Verify JSON syntax in code

#### 3. Serial Communication Issues
**Symptoms**: Desktop app can't connect
**Solutions**:
- Check baud rate (115200)
- Verify COM port in Device Manager
- Ensure USB CDC is enabled
- Test with Arduino Serial Monitor

#### 4. Memory Issues
**Symptoms**: Crashes, resets, or erratic behavior
**Solutions**:
- Monitor heap usage with `ESP.getFreeHeap()`
- Use static allocation where possible
- Enable PSRAM for large buffers
- Reduce queue size if needed

### Debugging Tools

#### Memory Monitoring
```cpp
void printMemoryInfo() {
    Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
    Serial.printf("Largest free block: %d bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("PSRAM free: %d bytes\n", ESP.getFreePsram());
}
```

#### USB Status Check
```cpp
void checkUSBStatus() {
    Serial.printf("USB initialized: %s\n", USB.isConnected() ? "Yes" : "No");
    Serial.printf("HID ready: %s\n", Mouse.isConnected() ? "Yes" : "No");
}
```

## Performance Optimization

### Memory Optimization
- Use `StaticJsonDocument` instead of `DynamicJsonDocument`
- Allocate buffers statically when possible
- Use PSRAM for large data structures
- Implement memory pooling for frequent allocations

### CPU Optimization
- Use hardware timers for precise timing
- Minimize string operations in critical paths
- Use efficient data structures (arrays vs vectors)
- Optimize JSON parsing with filters

### Power Optimization
- Use appropriate CPU frequency (240MHz for performance)
- Implement sleep modes when idle
- Optimize LED update frequency
- Use efficient GPIO operations

## Future Dependencies

### Planned Features
- **WiFi OTA Updates**: ESP32 WiFi library
- **Bluetooth Support**: ESP32 Bluetooth library
- **Advanced Security**: Crypto libraries
- **Machine Learning**: TensorFlow Lite for ESP32
- **File System**: SPIFFS or LittleFS for configuration

### Upgrade Path
- Maintain backward compatibility
- Version-controlled dependency updates
- Automated testing for library updates
- Documentation updates for new dependencies
