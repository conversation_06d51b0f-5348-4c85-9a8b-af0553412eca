class ReminderManager {
    constructor() {
        this.reminders = JSON.parse(localStorage.getItem('octane_reminders') || '[]');
        this.init();
    }

    init() {
        this.updateStats();
        this.renderCustomReminders();
        this.setupEventListeners();
        this.checkOverdueReminders();
    }

    setupEventListeners() {
        document.getElementById('addReminderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addReminder();
        });
    }

    addReminder() {
        const title = document.getElementById('reminderTitle').value;
        const description = document.getElementById('reminderDescription').value;
        const priority = document.getElementById('reminderPriority').value;
        const frequency = document.getElementById('reminderFrequency').value;
        const dueDate = document.getElementById('reminderDueDate').value;

        const reminder = {
            id: Date.now(),
            title: title,
            description: description,
            priority: priority,
            frequency: frequency,
            dueDate: new Date(dueDate),
            createdAt: new Date(),
            completed: false,
            completedAt: null,
            snoozedUntil: null
        };

        this.reminders.push(reminder);
        this.saveReminders();
        this.updateStats();
        this.renderCustomReminders();
        
        // Reset form
        document.getElementById('addReminderForm').reset();
        
        this.showNotification('Reminder added successfully!', 'success');
    }

    markCompleted(reminderId) {
        const reminder = this.reminders.find(r => r.id === reminderId);
        if (reminder) {
            reminder.completed = true;
            reminder.completedAt = new Date();
            
            // If it's a recurring reminder, create next occurrence
            if (reminder.frequency !== 'once') {
                this.createNextOccurrence(reminder);
            }
            
            this.saveReminders();
            this.updateStats();
            this.renderCustomReminders();
            this.showNotification('Reminder marked as completed!', 'success');
        }
    }

    createNextOccurrence(originalReminder) {
        const nextDue = new Date(originalReminder.dueDate);
        
        switch (originalReminder.frequency) {
            case 'daily':
                nextDue.setDate(nextDue.getDate() + 1);
                break;
            case 'weekly':
                nextDue.setDate(nextDue.getDate() + 7);
                break;
            case 'monthly':
                nextDue.setMonth(nextDue.getMonth() + 1);
                break;
            case 'quarterly':
                nextDue.setMonth(nextDue.getMonth() + 3);
                break;
        }

        const newReminder = {
            ...originalReminder,
            id: Date.now(),
            dueDate: nextDue,
            createdAt: new Date(),
            completed: false,
            completedAt: null,
            snoozedUntil: null
        };

        this.reminders.push(newReminder);
    }

    snoozeReminder(reminderId, days = 7) {
        const reminder = this.reminders.find(r => r.id === reminderId);
        if (reminder) {
            const snoozeUntil = new Date();
            snoozeUntil.setDate(snoozeUntil.getDate() + days);
            reminder.snoozedUntil = snoozeUntil;
            
            this.saveReminders();
            this.updateStats();
            this.renderCustomReminders();
            this.showNotification(`Reminder snoozed for ${days} days`, 'info');
        }
    }

    deleteReminder(reminderId) {
        this.reminders = this.reminders.filter(r => r.id !== reminderId);
        this.saveReminders();
        this.updateStats();
        this.renderCustomReminders();
        this.showNotification('Reminder deleted', 'info');
    }

    updateStats() {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        
        const total = this.reminders.length;
        const critical = this.reminders.filter(r => r.priority === 'critical' && !r.completed).length;
        const overdue = this.reminders.filter(r => {
            if (r.completed || r.snoozedUntil > now) return false;
            return new Date(r.dueDate) < now;
        }).length;
        const completedToday = this.reminders.filter(r => {
            if (!r.completedAt) return false;
            const completedDate = new Date(r.completedAt);
            return completedDate >= today;
        }).length;

        document.getElementById('totalReminders').textContent = total;
        document.getElementById('criticalReminders').textContent = critical;
        document.getElementById('overdueReminders').textContent = overdue;
        document.getElementById('completedToday').textContent = completedToday;
    }

    renderCustomReminders() {
        const container = document.getElementById('customReminders');
        const activeReminders = this.reminders.filter(r => !r.completed);
        
        if (activeReminders.length === 0) {
            container.innerHTML = '<p style="color: var(--muted-text); text-align: center; padding: 40px;">No custom reminders yet. Add one above!</p>';
            return;
        }

        container.innerHTML = activeReminders.map(reminder => {
            const isOverdue = new Date(reminder.dueDate) < new Date() && (!reminder.snoozedUntil || reminder.snoozedUntil < new Date());
            const isSnoozed = reminder.snoozedUntil && reminder.snoozedUntil > new Date();
            
            return `
                <div class="reminder-card ${isOverdue ? 'overdue' : ''} ${isSnoozed ? 'snoozed' : ''}">
                    <div class="reminder-priority priority-${reminder.priority}">${reminder.priority.toUpperCase()}</div>
                    <div class="reminder-title">${reminder.title}</div>
                    <div class="reminder-description">${reminder.description}</div>
                    <div class="reminder-actions">
                        <button class="btn btn-small btn-primary" onclick="reminderManager.markCompleted(${reminder.id})">Mark Completed</button>
                        <button class="btn btn-small btn-secondary" onclick="reminderManager.snoozeReminder(${reminder.id})">Snooze 1 Week</button>
                        <button class="btn btn-small btn-danger" onclick="reminderManager.deleteReminder(${reminder.id})">Delete</button>
                    </div>
                    <div class="reminder-frequency">
                        Due: ${new Date(reminder.dueDate).toLocaleDateString()} | 
                        Frequency: ${reminder.frequency}
                        ${isOverdue ? ' | <span style="color: #ff4444;">OVERDUE</span>' : ''}
                        ${isSnoozed ? ' | <span style="color: #ffaa00;">SNOOZED</span>' : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    checkOverdueReminders() {
        const now = new Date();
        const overdueReminders = this.reminders.filter(r => {
            if (r.completed || (r.snoozedUntil && r.snoozedUntil > now)) return false;
            return new Date(r.dueDate) < now;
        });

        if (overdueReminders.length > 0) {
            this.showNotification(`You have ${overdueReminders.length} overdue reminder(s)!`, 'warning');
        }
    }

    saveReminders() {
        localStorage.setItem('octane_reminders', JSON.stringify(this.reminders));
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            animation: slideIn 0.3s ease;
        `;
        
        switch (type) {
            case 'success':
                notification.style.background = '#28a745';
                break;
            case 'warning':
                notification.style.background = '#ffc107';
                notification.style.color = '#000';
                break;
            case 'error':
                notification.style.background = '#dc3545';
                break;
            default:
                notification.style.background = '#17a2b8';
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Global functions for inline event handlers
function markCompleted(element) {
    // This is for the pre-populated reminders
    element.textContent = 'Completed ✓';
    element.disabled = true;
    element.style.background = '#28a745';
    reminderManager.showNotification('Reminder marked as completed!', 'success');
}

function snoozeReminder(element) {
    // This is for the pre-populated reminders
    element.textContent = 'Snoozed';
    element.disabled = true;
    element.style.background = '#ffc107';
    element.style.color = '#000';
    reminderManager.showNotification('Reminder snoozed for 1 week', 'info');
}

// Initialize the reminder manager
const reminderManager = new ReminderManager();

// Add some CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .reminder-card.overdue {
        border-color: #ff4444 !important;
        background: rgba(255, 68, 68, 0.1) !important;
    }
    
    .reminder-card.snoozed {
        opacity: 0.7;
        border-color: #ffaa00 !important;
    }
`;
document.head.appendChild(style);
