<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - License Management</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/admin.css?v=1.1">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ OCTANE</h1>
            <p class="subtitle">Professional License Management System</p>
        </div>

        <div id="alertContainer"></div>

        <!-- Firebase Login Form -->
        <div id="loginForm" class="login-form">
            <h2>🔐 Admin Access</h2>
            <div class="form-group">
                <label for="email">Email Address:</label>
                <input type="email" id="email" name="email" required placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required placeholder="Enter your password">
            </div>
            <button id="loginBtn" class="btn">🚀 Sign In</button>
            <div id="loginError" style="display: none;"></div>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="panel-header">
                <h2>📊 License Dashboard</h2>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <a href="/key-maintenance.html" class="btn btn-primary">🔧 Key Maintenance</a>
                    <a href="/reminders.html" class="btn btn-primary">📋 Reminders</a>
                    <button id="testDiscordBtn" class="btn btn-secondary">🎮 Test Discord</button>
                    <button id="dailyReportBtn" class="btn btn-secondary">📊 Daily Report</button>
                    <button id="updateDiscordBtn" class="btn btn-secondary">🔧 Update Token</button>
                    <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                    <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                    <button id="logoutBtn" class="btn btn-secondary">🚪 Logout</button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalLicenses">0</h3>
                    <p>Total Licenses</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeLicenses">0</h3>
                    <p>Active Licenses</p>
                </div>
                <div class="stat-card">
                    <h3 id="boundLicenses">0</h3>
                    <p>Bound to Devices</p>
                </div>
                <div class="stat-card">
                    <h3 id="expiredLicenses">0</h3>
                    <p>Expired Licenses</p>
                </div>
                <div class="stat-card">
                    <h3 id="discordStatus">🔄</h3>
                    <p>Discord Bot</p>
                    <small id="discordBotTag">Checking...</small>
                </div>
            </div>

            <!-- Admin Tabs -->
            <div class="admin-tabs">
                <button class="tab-btn active" data-tab="create">🎫 Create License</button>
                <button class="tab-btn" data-tab="maintenance">🔧 Key Maintenance</button>
                <button class="tab-btn" data-tab="reminders">📋 Reminders</button>
                <button class="tab-btn" data-tab="discord">🤖 Discord Management</button>
                <button class="tab-btn" data-tab="security">🛡️ Security Monitor</button>
                <button class="tab-btn" data-tab="system">📊 System Status</button>
            </div>

            <!-- Create License Tab -->
            <div id="create" class="tab-content active">
                <div class="create-license-section">
                <h3>🎫 Create New License</h3>
                
                <form id="createLicenseForm">
                    <!-- Duration Selector -->
                    <div class="form-group">
                        <label>License Duration:</label>
                        <div class="duration-selector">
                            <div class="duration-btn" data-duration="1hour">1 Hour</div>
                            <div class="duration-btn" data-duration="1day">1 Day</div>
                            <div class="duration-btn" data-duration="1week">1 Week</div>
                            <div class="duration-btn" data-duration="1month">1 Month</div>
                            <div class="duration-btn" data-duration="3months">3 Months</div>
                            <div class="duration-btn" data-duration="6months">6 Months</div>
                            <div class="duration-btn" data-duration="1year">1 Year</div>
                            <div class="duration-btn active" data-duration="lifetime">Lifetime</div>
                        </div>
                    </div>

                    <!-- Custom Date (Hidden by default) -->
                    <div id="customDateGroup" class="form-group hidden">
                        <label for="customDate">Custom Expiry Date:</label>
                        <input type="datetime-local" id="customDate">
                    </div>

                    <!-- Notes -->
                    <div class="form-group">
                        <label for="notes">Notes (Optional):</label>
                        <textarea id="notes" rows="3" placeholder="Add any notes about this license..."></textarea>
                    </div>

                    <button type="submit" class="btn">✨ Generate License Key</button>
                </form>
                </div>
            </div>

            <!-- Key Maintenance Tab -->
            <div id="maintenance" class="tab-content">
                <div class="key-maintenance">
                    <h3>🔧 Key Maintenance</h3>

                    <!-- Search and Filters -->
                    <div class="maintenance-controls">
                        <div class="search-section">
                            <input type="text" id="keySearch" placeholder="Search by license key, notes, or hardware ID..." class="search-input">
                            <button id="searchKeysBtn" class="btn btn-secondary">🔍 Search</button>
                        </div>

                        <div class="filter-section">
                            <select id="statusFilter">
                                <option value="all">All Status</option>
                                <option value="active">Active Only</option>
                                <option value="expired">Expired Only</option>
                                <option value="suspended">Suspended Only</option>
                            </select>

                            <select id="durationFilter">
                                <option value="all">All Durations</option>
                                <option value="1hour">1 Hour</option>
                                <option value="1day">1 Day</option>
                                <option value="1week">1 Week</option>
                                <option value="1month">1 Month</option>
                                <option value="3months">3 Months</option>
                                <option value="6months">6 Months</option>
                                <option value="1year">1 Year</option>
                                <option value="lifetime">Lifetime</option>
                            </select>

                            <button id="refreshKeysBtn" class="btn btn-secondary">🔄 Refresh</button>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="bulk-actions">
                        <button id="selectAllBtn" class="btn btn-secondary">☑️ Select All</button>
                        <button id="deselectAllBtn" class="btn btn-secondary">☐ Deselect All</button>
                        <button id="bulkDeleteBtn" class="btn btn-danger">🗑️ Delete Selected</button>
                        <button id="bulkSuspendBtn" class="btn btn-warning">⏸️ Suspend Selected</button>
                        <button id="bulkActivateBtn" class="btn btn-success">▶️ Activate Selected</button>
                        <span id="selectedCount" class="selected-count">0 selected</span>
                    </div>

                    <!-- Keys Table -->
                    <div id="keysTableContainer" class="keys-table-container">
                        <div class="loading">Loading license keys...</div>
                    </div>
                </div>
            </div>

            <!-- Security Monitor Tab -->
            <div id="security" class="tab-content">
                <div class="security-section">
                    <h3>🛡️ Security Monitor</h3>

                    <!-- Security Stats -->
                    <div class="security-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="totalSecurityEvents">0</div>
                            <div class="stat-label">Total Events</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number critical" id="criticalThreats">0</div>
                            <div class="stat-label">Critical Threats</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number warning" id="blockedUsers">0</div>
                            <div class="stat-label">Blocked Users</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number success" id="activeConnections">0</div>
                            <div class="stat-label">Active Connections</div>
                        </div>
                    </div>

                    <!-- Recent Security Events -->
                    <div class="recent-events">
                        <h4>Recent Security Events</h4>
                        <div id="securityEventsList" class="events-list">
                            <div class="loading">
                                <div class="spinner"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Actions -->
                    <div class="security-actions">
                        <h4>Security Actions</h4>
                        <div class="action-buttons">
                            <button class="btn btn-danger" onclick="clearSecurityLog()">🗑️ Clear Log</button>
                            <button class="btn btn-secondary" onclick="exportSecurityLog()">📥 Export Log</button>
                            <button class="btn btn-warning" onclick="blockAllSuspicious()">🚫 Block Suspicious</button>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Reminders Tab -->
            <div id="reminders" class="tab-content">
                <div class="reminders-section">
                    <h3>📋 System Reminders</h3>
                    <div class="reminders-content">
                        <p>No reminders configured yet.</p>
                    </div>
                </div>
            </div>

            <!-- Discord Management Tab -->
            <div id="discord" class="tab-content">
                <div class="discord-section">
                    <h3>🤖 Discord Management</h3>
                    <div class="discord-content">
                        <div class="discord-status">
                            <h4>Bot Status</h4>
                            <div class="status-indicator online">✅ Online</div>
                        </div>
                        <div class="discord-actions">
                            <button class="btn btn-primary">🎮 Test Bot</button>
                            <button class="btn btn-secondary">📊 Daily Report</button>
                            <button class="btn btn-warning">🔧 Update Token</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Status Tab -->
            <div id="system" class="tab-content">
                <div class="system-section">
                    <h3>📊 System Status</h3>
                    <div class="system-stats">
                        <div class="stat-card">
                            <h4>VPS Memory</h4>
                            <div class="stat-value">578Mi / 848Mi</div>
                        </div>
                        <div class="stat-card">
                            <h4>CPU Usage</h4>
                            <div class="stat-value">Low</div>
                        </div>
                        <div class="stat-card">
                            <h4>Services</h4>
                            <div class="stat-value">2 Running</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Discord Token Update Modal -->
    <div id="discordTokenModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>🔧 Update Discord Token</h3>
            <p>Enter your new Discord bot token:</p>
            <form id="discordTokenForm">
                <div class="form-group">
                    <label for="discordToken">Discord Bot Token:</label>
                    <input type="password" id="discordToken" placeholder="MTM5ODQ2MDg3NjQwMjU5MzkyNA.XXXXXX.XXXXXXXXXXXXXXXXXXXXXXXXXX" required>
                    <small>Get this from Discord Developer Portal → Your App → Bot → Token</small>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Update Token</button>
                    <button type="button" id="cancelTokenUpdate" class="btn btn-secondary">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script>
        // Check if Firebase is already loaded
        if (typeof firebase === 'undefined') {
            document.write('<script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"><\/script>');
            document.write('<script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"><\/script>');
        }
    </script>

    <!-- Scripts -->
    <script src="/js/firebase-config.js?v=1.3"></script>
    <script src="/js/admin-panel.js?v=1.2"></script>
</body>
</html>
