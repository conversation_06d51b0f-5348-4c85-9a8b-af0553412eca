using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RecoilController.Data;
using RecoilController.Models;
using RecoilController.Services;

namespace RecoilController.ViewModels
{
    /// <summary>
    /// Main view model for the application
    /// </summary>
    public partial class MainViewModel : ObservableObject
    {
        private readonly ConfigurationService _configService;
        private readonly DeviceService _deviceService;
        private readonly AuthenticationService _authService;

        [ObservableProperty]
        private UserConfiguration _currentConfiguration;

        [ObservableProperty]
        private DeviceInfo _deviceInfo;

        [ObservableProperty]
        private bool _isAuthenticated;

        [ObservableProperty]
        private string _statusMessage = "Ready";

        [ObservableProperty]
        private bool _recoilControlActive;

        [ObservableProperty]
        private Weapon _selectedWeapon;

        [ObservableProperty]
        private string _authenticationStatus = "NOT AUTHENTICATED";

        [ObservableProperty]
        private string _maskedLicenseKey = "Not Set";

        [ObservableProperty]
        private string _licenseExpiry = "Unknown";

        [ObservableProperty]
        private string _hardwareId = string.Empty;

        [ObservableProperty]
        private bool _isLicenseExpiringSoon = false;

        public ObservableCollection<Weapon> AvailableWeapons { get; }
        public ObservableCollection<string> AvailableConfigurations { get; }
        public ObservableCollection<string> AvailablePorts { get; }

        public ICommand SaveConfigurationCommand { get; }
        public ICommand LoadConfigurationCommand { get; }
        public ICommand DeleteConfigurationCommand { get; }
        public ICommand ConnectDeviceCommand { get; }
        public ICommand DisconnectDeviceCommand { get; }
        public ICommand StartRecoilCommand { get; }
        public ICommand StopRecoilCommand { get; }
        public ICommand ValidateLicenseCommand { get; }
        public ICommand RefreshPortsCommand { get; }
        public ICommand ChangeLicenseCommand { get; }
        public ICommand LogoutCommand { get; }

        public MainViewModel(ConfigurationService configService, DeviceService deviceService, AuthenticationService authService)
        {
            _configService = configService;
            _deviceService = deviceService;
            _authService = authService;

            // Initialize collections
            AvailableWeapons = new ObservableCollection<Weapon>(WeaponData.GetAllWeapons());
            AvailableConfigurations = new ObservableCollection<string>();
            AvailablePorts = new ObservableCollection<string>();

            // Initialize default configuration
            CurrentConfiguration = new UserConfiguration("Default");
            SelectedWeapon = AvailableWeapons.FirstOrDefault();

            // Initialize commands
            SaveConfigurationCommand = new AsyncRelayCommand<string>(SaveConfiguration);
            LoadConfigurationCommand = new AsyncRelayCommand<string>(LoadConfiguration);
            DeleteConfigurationCommand = new AsyncRelayCommand<string>(DeleteConfiguration);
            ConnectDeviceCommand = new AsyncRelayCommand<string>(ConnectDevice);
            DisconnectDeviceCommand = new RelayCommand(DisconnectDevice);
            StartRecoilCommand = new AsyncRelayCommand(StartRecoil);
            StopRecoilCommand = new AsyncRelayCommand(StopRecoil);
            ValidateLicenseCommand = new AsyncRelayCommand<string>(ValidateLicense);
            RefreshPortsCommand = new RelayCommand(RefreshPorts);
            ChangeLicenseCommand = new RelayCommand(ChangeLicense);
            LogoutCommand = new RelayCommand(PerformLogout);

            // Subscribe to device events
            _deviceService.DeviceStatusChanged += OnDeviceStatusChanged;
            _deviceService.ResponseReceived += OnDeviceResponseReceived;

            // Initialize
            Initialize();
        }

        private async void Initialize()
        {
            RefreshConfigurations();
            RefreshPorts();
            
            // Try to load auto-load configuration
            var autoLoadConfig = _configService.GetAutoLoadConfiguration();
            if (!string.IsNullOrEmpty(autoLoadConfig))
            {
                await LoadConfiguration(autoLoadConfig);
            }

            IsAuthenticated = _authService.IsAuthenticated;
            DeviceInfo = _deviceService.CurrentDevice;

            // Update authentication info
            UpdateAuthenticationInfo();
        }

        private async Task SaveConfiguration(string configName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(configName))
                {
                    StatusMessage = "Please enter a configuration name";
                    return;
                }

                CurrentConfiguration.ConfigName = configName;
                CurrentConfiguration.WeaponId = SelectedWeapon?.Id ?? 1;
                CurrentConfiguration.WeaponName = SelectedWeapon?.Name ?? "Unknown";

                if (_configService.SaveConfiguration(CurrentConfiguration))
                {
                    StatusMessage = $"Configuration '{configName}' saved successfully";
                    RefreshConfigurations();
                }
                else
                {
                    StatusMessage = "Failed to save configuration";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error saving configuration: {ex.Message}";
            }
        }

        private async Task LoadConfiguration(string configName)
        {
            try
            {
                var config = _configService.LoadConfiguration(configName);
                if (config != null)
                {
                    CurrentConfiguration = config;
                    SelectedWeapon = AvailableWeapons.FirstOrDefault(w => w.Id == config.WeaponId);
                    StatusMessage = $"Configuration '{configName}' loaded successfully";
                    
                    // Update device if connected
                    if (DeviceInfo?.IsConnected == true)
                    {
                        await _deviceService.SetWeapon(CurrentConfiguration.WeaponId);
                    }
                }
                else
                {
                    StatusMessage = $"Configuration '{configName}' not found";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading configuration: {ex.Message}";
            }
        }

        private Task DeleteConfiguration(string configName)
        {
            try
            {
                if (_configService.DeleteConfiguration(configName))
                {
                    StatusMessage = $"Configuration '{configName}' deleted successfully";
                    RefreshConfigurations();
                }
                else
                {
                    StatusMessage = "Failed to delete configuration";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error deleting configuration: {ex.Message}";
            }
            return Task.CompletedTask;
        }

        private async Task ConnectDevice(string portName)
        {
            try
            {
                StatusMessage = "Connecting to device...";
                
                if (await _deviceService.ConnectToDevice(portName))
                {
                    StatusMessage = $"Connected to device on {portName}";
                    
                    // Send current weapon configuration
                    await _deviceService.SetWeapon(CurrentConfiguration.WeaponId);
                }
                else
                {
                    StatusMessage = "Failed to connect to device";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error connecting to device: {ex.Message}";
            }
        }

        private void DisconnectDevice()
        {
            try
            {
                _deviceService.DisconnectDevice();
                StatusMessage = "Device disconnected";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error disconnecting device: {ex.Message}";
            }
        }

        private async Task StartRecoil()
        {
            try
            {
                if (!IsAuthenticated)
                {
                    StatusMessage = "Please validate your license key first";
                    return;
                }

                if (DeviceInfo?.IsConnected != true)
                {
                    StatusMessage = "Device not connected";
                    return;
                }

                if (await _deviceService.StartRecoil())
                {
                    RecoilControlActive = true;
                    StatusMessage = "Recoil control started";
                }
                else
                {
                    StatusMessage = "Failed to start recoil control";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error starting recoil control: {ex.Message}";
            }
        }

        private async Task StopRecoil()
        {
            try
            {
                if (await _deviceService.StopRecoil())
                {
                    RecoilControlActive = false;
                    StatusMessage = "Recoil control stopped";
                }
                else
                {
                    StatusMessage = "Failed to stop recoil control";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error stopping recoil control: {ex.Message}";
            }
        }

        private async Task ValidateLicense(string licenseKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(licenseKey))
                {
                    StatusMessage = "Please enter a license key";
                    return;
                }

                StatusMessage = "Validating license key...";
                
                var result = await _authService.ValidateLicenseKey(licenseKey);
                
                if (result.Success)
                {
                    IsAuthenticated = true;
                    StatusMessage = "License validated successfully";
                }
                else
                {
                    IsAuthenticated = false;
                    StatusMessage = result.Message;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error validating license: {ex.Message}";
                IsAuthenticated = false;
            }
        }

        private void RefreshPorts()
        {
            try
            {
                AvailablePorts.Clear();
                var ports = _deviceService.ScanForDevices();
                foreach (var port in ports)
                {
                    AvailablePorts.Add(port);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing ports: {ex.Message}";
            }
        }

        private void RefreshConfigurations()
        {
            try
            {
                AvailableConfigurations.Clear();
                var configs = _configService.GetAvailableConfigurations();
                foreach (var config in configs)
                {
                    AvailableConfigurations.Add(config);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing configurations: {ex.Message}";
            }
        }

        private void OnDeviceStatusChanged(object sender, DeviceInfo deviceInfo)
        {
            DeviceInfo = deviceInfo;
            
            if (deviceInfo.Status == DeviceStatus.Connected)
            {
                StatusMessage = $"Device connected on {deviceInfo.PortName}";
            }
            else if (deviceInfo.Status == DeviceStatus.Error)
            {
                StatusMessage = $"Device error: {deviceInfo.ErrorMessage}";
            }
        }

        private void OnDeviceResponseReceived(object sender, DeviceResponsePacket response)
        {
            // Handle device responses
            if (response.Response == DeviceResponse.VersionInfo)
            {
                StatusMessage = $"Firmware version: {response.GetDataAsString()}";
            }
        }

        private void ChangeLicense()
        {
            // This would open the login window again
            // For now, just show a message
            StatusMessage = "Please restart the application to change license key";
        }

        private void PerformLogout()
        {
            try
            {
                _authService.Logout();
                _configService.ClearSavedLicenseKey();
                IsAuthenticated = false;
                UpdateAuthenticationInfo();
                StatusMessage = "Logged out successfully. Please restart the application.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error during logout: {ex.Message}";
            }
        }

        private void UpdateAuthenticationInfo()
        {
            if (_authService.IsAuthenticated)
            {
                AuthenticationStatus = "AUTHENTICATED";
                MaskedLicenseKey = _authService.MaskedLicenseKey;
                HardwareId = _authService.HardwareId;

                if (_authService.LicenseExpiry.HasValue)
                {
                    var expiry = _authService.LicenseExpiry.Value;
                    LicenseExpiry = expiry.ToString("yyyy-MM-dd HH:mm");

                    // Check if license expires within 30 days
                    IsLicenseExpiringSoon = (expiry - DateTime.Now).TotalDays <= 30;
                }
                else
                {
                    LicenseExpiry = "Lifetime";
                    IsLicenseExpiringSoon = false;
                }
            }
            else
            {
                AuthenticationStatus = "NOT AUTHENTICATED";
                MaskedLicenseKey = "Not Set";
                LicenseExpiry = "Unknown";
                HardwareId = _authService.HardwareId;
                IsLicenseExpiringSoon = false;
            }
        }

        partial void OnSelectedWeaponChanged(Weapon value)
        {
            if (value != null && CurrentConfiguration != null)
            {
                CurrentConfiguration.WeaponId = value.Id;
                CurrentConfiguration.WeaponName = value.Name;

                // Update device if connected
                if (DeviceInfo?.IsConnected == true)
                {
                    _ = Task.Run(async () => await _deviceService.SetWeapon(value.Id));
                }
            }
        }

        partial void OnIsAuthenticatedChanged(bool value)
        {
            UpdateAuthenticationInfo();
        }
    }
}
