using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace RecoilController.Security
{
    /// <summary>
    /// Advanced security manager with anti-tampering and protection features
    /// </summary>
    public static class SecurityManager
    {
        private static readonly Timer _integrityTimer;
        private static readonly string _originalHash;
        private static bool _isSecurityActive = false;
        private static int _tamperAttempts = 0;
        private const int MAX_TAMPER_ATTEMPTS = 3;

        // Windows API for process protection
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("kernel32.dll")]
        private static extern bool SetProcessWorkingSetSize(IntPtr hProcess, UIntPtr dwMinimumWorkingSetSize, UIntPtr dwMaximumWorkingSetSize);

        [DllImport("kernel32.dll")]
        private static extern bool IsDebuggerPresent();

        [DllImport("kernel32.dll")]
        private static extern bool CheckRemoteDebuggerPresent(IntPtr hProcess, ref bool isDebuggerPresent);

        [DllImport("ntdll.dll")]
        private static extern int NtSetInformationProcess(IntPtr processHandle, int processInformationClass, ref int processInformation, int processInformationLength);

        [DllImport("kernel32.dll")]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        static SecurityManager()
        {
            _originalHash = CalculateAssemblyHash();
            _integrityTimer = new Timer(PerformSecurityChecks, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        /// <summary>
        /// Initializes security protection
        /// </summary>
        public static void Initialize()
        {
#if DEBUG
            // Disable security in debug mode to prevent ExecutionEngineException
            _isSecurityActive = false;
            Console.WriteLine("🔓 Security protection disabled (Debug mode)");
            return;
#endif

            try
            {
                _isSecurityActive = true;

                // Enable anti-debugging protection
                EnableAntiDebugging();

                // Protect process memory
                ProtectProcessMemory();

                // Verify assembly integrity
                VerifyAssemblyIntegrity();

                // Check for known analysis tools
                DetectAnalysisTools();

                // Obfuscate critical strings
                ObfuscateCriticalData();

                Console.WriteLine("🔒 Security protection initialized");
            }
            catch (Exception ex)
            {
                // Security initialization failed - terminate
                Environment.FailFast($"Security initialization failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Performs periodic security checks
        /// </summary>
        private static void PerformSecurityChecks(object state)
        {
            if (!_isSecurityActive) return;

            try
            {
                // Check for debugger attachment
                if (IsDebuggerAttached())
                {
                    HandleTamperAttempt("Debugger detected");
                    return;
                }

                // Verify assembly integrity
                if (!VerifyAssemblyIntegrity())
                {
                    HandleTamperAttempt("Assembly integrity compromised");
                    return;
                }

                // Check for memory manipulation
                if (DetectMemoryManipulation())
                {
                    HandleTamperAttempt("Memory manipulation detected");
                    return;
                }

                // Check for known cracking tools
                if (DetectAnalysisTools())
                {
                    HandleTamperAttempt("Analysis tools detected");
                    return;
                }

                // Verify license integrity
                if (!VerifyLicenseIntegrity())
                {
                    HandleTamperAttempt("License tampering detected");
                    return;
                }
            }
            catch (Exception ex)
            {
                HandleTamperAttempt($"Security check failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Enables anti-debugging protection
        /// </summary>
        private static void EnableAntiDebugging()
        {
            try
            {
                // Set debug privilege
                var process = GetCurrentProcess();
                int debugFlag = 1;
                NtSetInformationProcess(process, 0x1e, ref debugFlag, sizeof(int));

                // Additional anti-debugging measures
                Task.Run(() =>
                {
                    while (_isSecurityActive)
                    {
                        if (IsDebuggerPresent())
                        {
                            Environment.FailFast("Debugger detected - terminating");
                        }

                        bool remoteDebugger = false;
                        CheckRemoteDebuggerPresent(GetCurrentProcess(), ref remoteDebugger);
                        if (remoteDebugger)
                        {
                            Environment.FailFast("Remote debugger detected - terminating");
                        }

                        Thread.Sleep(1000);
                    }
                });
            }
            catch
            {
                // Fail silently to avoid revealing protection
            }
        }

        /// <summary>
        /// Protects process memory from manipulation
        /// </summary>
        private static void ProtectProcessMemory()
        {
            try
            {
                // Lock working set to prevent memory dumps
                SetProcessWorkingSetSize(GetCurrentProcess(), UIntPtr.Zero, UIntPtr.Zero);
            }
            catch
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Verifies assembly integrity using hash comparison
        /// </summary>
        private static bool VerifyAssemblyIntegrity()
        {
            try
            {
                var currentHash = CalculateAssemblyHash();
                return currentHash.Equals(_originalHash, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Calculates hash of the current assembly
        /// </summary>
        private static string CalculateAssemblyHash()
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                var assemblyPath = assembly.Location;
                
                using (var sha256 = SHA256.Create())
                {
                    var fileBytes = File.ReadAllBytes(assemblyPath);
                    var hashBytes = sha256.ComputeHash(fileBytes);
                    return Convert.ToBase64String(hashBytes);
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Detects if a debugger is attached
        /// </summary>
        private static bool IsDebuggerAttached()
        {
            try
            {
                // Multiple debugger detection methods
                if (Debugger.IsAttached) return true;
                if (IsDebuggerPresent()) return true;

                bool remoteDebugger = false;
                CheckRemoteDebuggerPresent(GetCurrentProcess(), ref remoteDebugger);
                if (remoteDebugger) return true;

                // Check for common debugger processes
                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        if (processName.Contains("ollydbg") || 
                            processName.Contains("x64dbg") || 
                            processName.Contains("windbg") ||
                            processName.Contains("ida") ||
                            processName.Contains("cheat") ||
                            processName.Contains("trainer"))
                        {
                            return true;
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }

                return false;
            }
            catch
            {
                return true; // Assume compromised if check fails
            }
        }

        /// <summary>
        /// Detects memory manipulation attempts
        /// </summary>
        private static bool DetectMemoryManipulation()
        {
            try
            {
                // Check for common memory manipulation tools
                var suspiciousModules = new[]
                {
                    "kernel32.dll",
                    "ntdll.dll",
                    "user32.dll"
                };

                foreach (var module in suspiciousModules)
                {
                    var handle = LoadLibrary(module);
                    if (handle == IntPtr.Zero) continue;

                    // Check for hooked functions
                    var suspiciousFunctions = new[]
                    {
                        "ReadProcessMemory",
                        "WriteProcessMemory",
                        "VirtualProtect",
                        "SetWindowsHookEx"
                    };

                    foreach (var function in suspiciousFunctions)
                    {
                        var address = GetProcAddress(handle, function);
                        if (address != IntPtr.Zero)
                        {
                            // Check if function is hooked (simplified check)
                            var bytes = new byte[5];
                            Marshal.Copy(address, bytes, 0, 5);
                            
                            // Check for common hook patterns (JMP instruction)
                            if (bytes[0] == 0xE9 || bytes[0] == 0xEB)
                            {
                                return true;
                            }
                        }
                    }
                }

                return false;
            }
            catch
            {
                return true; // Assume compromised if check fails
            }
        }

        /// <summary>
        /// Detects known analysis and cracking tools
        /// </summary>
        private static bool DetectAnalysisTools()
        {
            try
            {
                var suspiciousProcesses = new[]
                {
                    "processhacker", "procexp", "procmon", "wireshark", "fiddler",
                    "ollydbg", "x64dbg", "ida", "ghidra", "radare2", "dnspy",
                    "reflexil", "de4dot", "megadumper", "scylla", "importrec",
                    "cheatengine", "artmoney", "tsearch", "scanmem"
                };

                var processes = Process.GetProcesses();
                foreach (var process in processes)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        foreach (var suspicious in suspiciousProcesses)
                        {
                            if (processName.Contains(suspicious))
                            {
                                return true;
                            }
                        }
                    }
                    catch
                    {
                        // Ignore access denied errors
                    }
                }

                return false;
            }
            catch
            {
                return true; // Assume compromised if check fails
            }
        }

        /// <summary>
        /// Verifies license data integrity
        /// </summary>
        private static bool VerifyLicenseIntegrity()
        {
            try
            {
                // This would integrate with the license system
                // For now, return true as placeholder
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obfuscates critical data in memory
        /// </summary>
        private static void ObfuscateCriticalData()
        {
            try
            {
                // Implement string obfuscation and critical data protection
                // This would encrypt sensitive strings and data in memory
            }
            catch
            {
                // Fail silently
            }
        }

        /// <summary>
        /// Handles tamper attempts
        /// </summary>
        private static void HandleTamperAttempt(string reason)
        {
            _tamperAttempts++;
            
            Console.WriteLine($"🚨 Security violation detected: {reason} (Attempt {_tamperAttempts}/{MAX_TAMPER_ATTEMPTS})");

            if (_tamperAttempts >= MAX_TAMPER_ATTEMPTS)
            {
                // Critical security violation - terminate silently
                Console.WriteLine($"🚨 Critical security violation: {reason} - Terminating silently");
                Environment.FailFast($"Multiple security violations detected: {reason}");
            }
            else
            {
                // Log warning silently (no popup)
                Console.WriteLine($"⚠️ Security warning: {reason} (Attempt {_tamperAttempts}/{MAX_TAMPER_ATTEMPTS})");
            }
        }

        /// <summary>
        /// Disables security protection (for testing only)
        /// </summary>
        public static void Disable()
        {
            _isSecurityActive = false;
            _integrityTimer?.Dispose();
        }

        /// <summary>
        /// Checks if security is active
        /// </summary>
        public static bool IsActive => _isSecurityActive;

        /// <summary>
        /// Gets current tamper attempt count
        /// </summary>
        public static int TamperAttempts => _tamperAttempts;
    }
}
