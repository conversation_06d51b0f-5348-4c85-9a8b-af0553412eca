using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.Wpf;
using Newtonsoft.Json;
using RecoilController.Services;
using RecoilController.Data;

namespace RecoilController.Views
{
    public partial class ModernMainWindow : Window
    {
        private bool _isScriptActive = false;
        private bool _isPanicMode = false;
        private DateTime _sessionStartTime = DateTime.Now;
        private TimeSpan _licenseRemainingTime = TimeSpan.Zero;
        private ESP32Service _esp32Service;
        private WeaponConfiguration _currentConfig;

        public ModernMainWindow()
        {
            InitializeComponent();
            this.Loaded += ModernMainWindow_Loaded;

            // Initialize ESP32 service
            _esp32Service = new ESP32Service();
            _esp32Service.ConnectionStatusChanged += OnESP32ConnectionChanged;
            _esp32Service.ErrorOccurred += OnESP32Error;

            // Initialize default configuration
            _currentConfig = new WeaponConfiguration
            {
                WeaponName = "AK-47",
                Sight = "none",
                Muzzle = "none",
                Barrel = "none",
                RecoilCompensation = 85f,
                Humanization = 15f,
                Sensitivity = 2.5f,
                ADSSensitivity = 1.0f,
                FOV = 90,
                HorizontalMultiplier = 100f,
                VerticalMultiplier = 100f,
                Smoothing = 50f,
                CursorCheck = false,
                RapidFire = false,
                AntiAFK = false
            };
        }

        private async void ModernMainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Hide main window initially
            this.Hide();

            try
            {
                // Show login window first - this is the ONLY way to access the app
                var loginWindow = new LoginWindow();
                var loginResult = loginWindow.ShowDialog();

                if (loginResult != true)
                {
                    // User cancelled login or authentication failed
                    Application.Current.Shutdown();
                    return;
                }

                // Store license information from successful login
                _licenseRemainingTime = loginWindow.RemainingTime;

                // Show main window only after successful authentication
                this.Show();

                // Initialize WebView2 only after successful authentication
                await webView.EnsureCoreWebView2Async();
                webView.CoreWebView2.WebMessageReceived += OnWebMessageReceived;

                // Load the HTML content
                webView.NavigateToString(GetModernHtmlContent());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Critical startup error: {ex.Message}", "Fatal Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        private async Task<bool> VerifyBackendConnectivity()
        {
            try
            {
                using var client = new System.Net.Http.HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                var response = await client.GetAsync("http://217.154.58.14:3000/api/health");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        private void StartBackendMonitoring()
        {
            // Monitor backend connectivity every 30 seconds
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(30);
            timer.Tick += async (s, e) =>
            {
                if (!await VerifyBackendConnectivity())
                {
                    MessageBox.Show("Lost connection to authentication server. Application will close for security.",
                                  "Security Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Application.Current.Shutdown();
                }
            };
            timer.Start();
        }

        private string GetModernHtmlContent()
        {
            return MainWindowHtml.GetHtmlContent();
        }

        private async void OnWebMessageReceived(object? sender, CoreWebView2WebMessageReceivedEventArgs e)
        {
            try
            {
                var messageJson = e.TryGetWebMessageAsString();
                var message = JsonConvert.DeserializeObject<dynamic>(messageJson);
                
                string action = message.action;
                
                switch (action)
                {
                    case "toggleScript":
                        _isScriptActive = message.data.active;
                        // TODO: Implement actual script toggle
                        break;
                        
                    case "panicMode":
                        TogglePanicMode();
                        break;

                    case "minimizeWindow":
                        this.WindowState = WindowState.Minimized;
                        break;

                    case "maximizeWindow":
                        this.WindowState = this.WindowState == WindowState.Maximized 
                            ? WindowState.Normal 
                            : WindowState.Maximized;
                        break;

                    case "closeWindow":
                        this.Close();
                        break;

                    case "weaponChanged":
                        // TODO: Handle weapon change
                        break;

                    case "sensitivityChanged":
                        // TODO: Handle sensitivity change
                        break;

                    case "fovChanged":
                        // TODO: Handle FOV change
                        break;

                    case "adsChanged":
                        // TODO: Handle ADS sensitivity change
                        break;

                    case "loadoutChanged":
                        // TODO: Handle loadout change
                        break;

                    case "connectESP32":
                        await HandleESP32Connection(message);
                        break;

                    case "updateWeaponConfig":
                        await HandleWeaponConfigUpdate(message);
                        break;

                    case "enableScript":
                        _isScriptActive = true;
                        await SendScriptStatusToESP32();
                        break;

                    case "disableScript":
                        _isScriptActive = false;
                        await SendScriptStatusToESP32();
                        break;

                    case "toggleFeature":
                        await HandleFeatureToggle(message);
                        break;

                    case "keybindChanged":
                        // TODO: Handle keybind change
                        break;

                    case "tabChanged":
                        // TODO: Handle tab change
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling web message: {ex.Message}");
                _ = SendErrorToDiscord("Desktop App Error", ex.Message, ex.StackTrace);
            }
        }

        private async Task SendErrorToDiscord(string errorType, string errorMessage, string stackTrace = "")
        {
            try
            {

                var errorData = new
                {
                    embeds = new[]
                    {
                        new
                        {
                            title = $"🚨 {errorType}",
                            description = $"```{errorMessage}```",
                            color = 0xff0000,
                            fields = new[]
                            {
                                new
                                {
                                    name = "Stack Trace",
                                    value = $"```{(string.IsNullOrEmpty(stackTrace) ? "No stack trace" : stackTrace.Substring(0, Math.Min(stackTrace.Length, 1000)))}```",
                                    inline = false
                                },
                                new
                                {
                                    name = "Timestamp",
                                    value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    inline = true
                                },
                                new
                                {
                                    name = "Source",
                                    value = "Desktop Application",
                                    inline = true
                                }
                            }
                        }
                    }
                };

                // Use WebhookService to send to VPS-managed webhook
                await Services.WebhookService.SendWebhookAsync("security", errorData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send error to Discord: {ex.Message}");
            }
        }

        private async Task SendESP32ErrorToDiscord(string errorType, string errorMessage)
        {
            try
            {

                var errorData = new
                {
                    embeds = new[]
                    {
                        new
                        {
                            title = $"🔧 ESP32 {errorType} Error",
                            description = $"```{errorMessage}```",
                            color = 0xffa500,
                            fields = new[]
                            {
                                new
                                {
                                    name = "Timestamp",
                                    value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                    inline = true
                                },
                                new
                                {
                                    name = "Source",
                                    value = "ESP32 Hardware",
                                    inline = true
                                }
                            }
                        }
                    }
                };

                // Use WebhookService to send to VPS-managed webhook
                await Services.WebhookService.SendWebhookAsync("esp32", errorData);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send ESP32 error to Discord: {ex.Message}");
            }
        }

        private void TogglePanicMode()
        {
            _isPanicMode = !_isPanicMode;
            
            if (_isPanicMode)
            {
                // Hide from task manager and minimize to system tray
                this.WindowState = WindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Visibility = Visibility.Hidden;
                
                System.Diagnostics.Debug.WriteLine("Panic mode activated - Application hidden");
            }
            else
            {
                // Restore window
                this.Visibility = Visibility.Visible;
                this.ShowInTaskbar = true;
                this.WindowState = WindowState.Normal;
                this.Activate();
                
                System.Diagnostics.Debug.WriteLine("Panic mode deactivated - Application restored");
            }
        }

        // Window controls
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                this.WindowState = this.WindowState == WindowState.Maximized 
                    ? WindowState.Normal 
                    : WindowState.Maximized;
            }
            else
            {
                this.DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = this.WindowState == WindowState.Maximized 
                ? WindowState.Normal 
                : WindowState.Maximized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // Add keyboard shortcut handling
        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);

            // F12 for panic mode
            if (e.Key == Key.F12)
            {
                TogglePanicMode();
                e.Handled = true;
            }
            // Double-tap Escape to restore from panic mode
            else if (e.Key == Key.Escape && _isPanicMode)
            {
                TogglePanicMode();
                e.Handled = true;
            }
        }

        private void CreateSystemTrayIcon()
        {
            try
            {
                // Create a simple system tray icon (requires additional NuGet package for full implementation)
                // For now, just log the action
                System.Diagnostics.Debug.WriteLine("System tray icon created (placeholder)");

                // TODO: Implement actual system tray icon using Hardcodet.NotifyIcon.Wpf or similar
                // This would require adding the NuGet package and implementing proper tray functionality
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create system tray icon: {ex.Message}");
            }
        }

        private void RestoreFromPanicMode()
        {
            if (_isPanicMode)
            {
                TogglePanicMode();
            }
        }

        private void HideFromTaskManager()
        {
            try
            {
                // Hide from task manager by minimizing and hiding from taskbar
                this.WindowState = WindowState.Minimized;
                this.ShowInTaskbar = false;
                this.Visibility = Visibility.Hidden;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error hiding from task manager: {ex.Message}");
            }
        }

        private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            // Navigation completed - can be used for additional initialization
            System.Diagnostics.Debug.WriteLine("WebView navigation completed");

            // Start ESP32 monitoring
            StartESP32Monitoring();
        }

        private async Task HandleESP32Connection(dynamic message)
        {
            try
            {
                string port = message.port ?? "auto";
                bool connected = await _esp32Service.ConnectAsync(port);

                if (connected)
                {
                    await _esp32Service.SendConfigurationAsync(_currentConfig);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ESP32 connection error: {ex.Message}");
                await SendErrorToDiscord("ESP32 Connection Error", ex.Message, ex.StackTrace);
            }
        }

        private async Task HandleWeaponConfigUpdate(dynamic message)
        {
            try
            {
                var config = message.config;

                _currentConfig.WeaponName = config.weapon ?? _currentConfig.WeaponName;
                _currentConfig.Sight = config.sight ?? _currentConfig.Sight;
                _currentConfig.Muzzle = config.muzzle ?? _currentConfig.Muzzle;
                _currentConfig.Barrel = config.barrel ?? _currentConfig.Barrel;
                _currentConfig.RecoilCompensation = config.recoilComp ?? _currentConfig.RecoilCompensation;
                _currentConfig.Humanization = config.humanization ?? _currentConfig.Humanization;
                _currentConfig.Sensitivity = config.sensitivity ?? _currentConfig.Sensitivity;
                _currentConfig.ADSSensitivity = config.adsSensitivity ?? _currentConfig.ADSSensitivity;
                _currentConfig.FOV = config.fov ?? _currentConfig.FOV;
                _currentConfig.HorizontalMultiplier = config.horizontal ?? _currentConfig.HorizontalMultiplier;
                _currentConfig.VerticalMultiplier = config.vertical ?? _currentConfig.VerticalMultiplier;
                _currentConfig.Smoothing = config.smoothing ?? _currentConfig.Smoothing;

                // Send updated configuration to ESP32
                await _esp32Service.SendConfigurationAsync(_currentConfig);

                // Update recoil pattern if weapon changed
                var weapon = WeaponData.GetWeaponByName(_currentConfig.WeaponName);
                if (weapon != null)
                {
                    await _esp32Service.SendRecoilPatternAsync(weapon, _currentConfig.Sensitivity, _currentConfig.RecoilCompensation);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Weapon config update error: {ex.Message}");
                await SendErrorToDiscord("Weapon Config Error", ex.Message, ex.StackTrace);
            }
        }

        private async Task HandleFeatureToggle(dynamic message)
        {
            try
            {
                string feature = message.feature;
                bool enabled = message.enabled;

                switch (feature)
                {
                    case "cursorCheck":
                        _currentConfig.CursorCheck = enabled;
                        break;
                    case "rapidFire":
                        _currentConfig.RapidFire = enabled;
                        break;
                    case "antiAFK":
                        _currentConfig.AntiAFK = enabled;
                        break;
                }

                await _esp32Service.SendConfigurationAsync(_currentConfig);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Feature toggle error: {ex.Message}");
                await SendErrorToDiscord("Feature Toggle Error", ex.Message, ex.StackTrace);
            }
        }

        private async Task SendScriptStatusToESP32()
        {
            try
            {
                await _esp32Service.SendCommandAsync(new ESP32Command
                {
                    Type = "script_status",
                    Data = new { active = _isScriptActive }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Script status error: {ex.Message}");
            }
        }

        private void OnESP32ConnectionChanged(object sender, bool connected)
        {
            try
            {
                // Send status update to UI
                var statusMessage = new
                {
                    action = "esp32StatusUpdate",
                    connected = connected
                };

                var json = JsonConvert.SerializeObject(statusMessage);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ESP32 status update error: {ex.Message}");
            }
        }

        private async void OnESP32Error(object sender, string error)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"ESP32 Error: {error}");
                await SendErrorToDiscord("ESP32 Error", error, "");

                // Send error notification to UI
                var errorMessage = new
                {
                    action = "showNotification",
                    message = $"ESP32 Error: {error}",
                    type = "error"
                };

                var json = JsonConvert.SerializeObject(errorMessage);
                webView.CoreWebView2.PostWebMessageAsString(json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ESP32 error handling failed: {ex.Message}");
            }
        }

        private void StartESP32Monitoring()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ESP32 service initialized and ready");
                // ESP32 service is now handled through the ESP32Service class
                // Real-time monitoring is handled by the service's event handlers
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to start ESP32 monitoring: {ex.Message}");
                _ = SendErrorToDiscord("ESP32 Monitoring Error", ex.Message, ex.StackTrace);
            }
        }
    }
}
