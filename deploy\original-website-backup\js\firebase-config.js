// Firebase Configuration for Octane Admin Panel
const firebaseConfig = {
    apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
    authDomain: "authenticator-678a2.firebaseapp.com",
    projectId: "authenticator-678a2",
    storageBucket: "authenticator-678a2.firebasestorage.app",
    messagingSenderId: "738745478695",
    appId: "1:738745478695:web:authenticator-app"
};

// Initialize Firebase
let app, auth;
try {
    app = firebase.initializeApp(firebaseConfig);
    auth = firebase.auth();
    console.log('✅ Firebase initialized successfully');
} catch (error) {
    console.error('❌ Firebase initialization error:', error);
}

// Firebase Auth State Management
class FirebaseAuth {
    constructor() {
        this.currentUser = null;
        this.onAuthStateChanged = this.onAuthStateChanged.bind(this);

        if (auth) {
            auth.onAuthStateChanged(this.onAuthStateChanged);
        } else {
            console.error('Firebase auth not initialized');
            setTimeout(() => this.showLoginForm(), 100);
        }
    }

    onAuthStateChanged(user) {
        this.currentUser = user;
        if (user) {
            console.log('User signed in:', user.email);
            this.showAdminPanel();
        } else {
            console.log('User signed out');
            this.showLoginForm();
        }
    }

    async signInWithEmail(email, password) {
        try {
            await auth.signInWithEmailAndPassword(email, password);
            return { success: true };
        } catch (error) {
            return { 
                success: false, 
                error: this.getErrorMessage(error.code) 
            };
        }
    }

    async signOut() {
        try {
            await auth.signOut();
            return { success: true };
        } catch (error) {
            return { 
                success: false, 
                error: 'Failed to sign out' 
            };
        }
    }

    getErrorMessage(errorCode) {
        switch (errorCode) {
            case 'auth/user-not-found':
                return 'No user found with this email address.';
            case 'auth/wrong-password':
                return 'Incorrect password.';
            case 'auth/invalid-email':
                return 'Invalid email address.';
            case 'auth/user-disabled':
                return 'This account has been disabled.';
            case 'auth/too-many-requests':
                return 'Too many failed login attempts. Please try again later.';
            case 'auth/invalid-credential':
                return 'Invalid email or password.';
            default:
                return 'Authentication failed. Please try again.';
        }
    }

    showLoginForm() {
        document.getElementById('loginForm').classList.remove('hidden');
        document.getElementById('adminPanel').classList.remove('active');
    }

    showAdminPanel() {
        document.getElementById('loginForm').classList.add('hidden');
        document.getElementById('adminPanel').classList.add('active');

        // Load admin panel data
        if (window.adminPanel) {
            window.adminPanel.loadLicenses();
            window.adminPanel.loadDiscordStatus();
        }
    }
}

// Initialize Firebase Auth
window.firebaseAuth = new FirebaseAuth();
