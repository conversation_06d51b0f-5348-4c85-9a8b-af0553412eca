// Reminders JavaScript
let reminders = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeReminders();
});

function initializeReminders() {
    // Bind event listeners
    document.getElementById('createReminderBtn').addEventListener('click', createReminder);
    document.getElementById('priorityFilter').addEventListener('change', filterReminders);
    document.getElementById('categoryFilter').addEventListener('change', filterReminders);
    
    // Load reminders from localStorage
    loadReminders();
}

function loadReminders() {
    try {
        const stored = localStorage.getItem('octane_reminders');
        reminders = stored ? JSON.parse(stored) : [];
        renderReminders();
    } catch (error) {
        console.error('Error loading reminders:', error);
        reminders = [];
        renderReminders();
    }
}

function saveReminders() {
    try {
        localStorage.setItem('octane_reminders', JSON.stringify(reminders));
    } catch (error) {
        console.error('Error saving reminders:', error);
        OctaneUtils.showAlert('Error saving reminders', 'danger');
    }
}

function createReminder() {
    const title = document.getElementById('reminderTitle').value.trim();
    const description = document.getElementById('reminderDescription').value.trim();
    const priority = document.getElementById('reminderPriority').value;
    const category = document.getElementById('reminderCategory').value;
    
    if (!title) {
        OctaneUtils.showAlert('Please enter a reminder title', 'warning');
        return;
    }
    
    const reminder = {
        id: Date.now().toString(),
        title,
        description,
        priority,
        category,
        created: new Date().toISOString(),
        completed: false
    };
    
    reminders.unshift(reminder);
    saveReminders();
    renderReminders();
    
    // Clear form
    document.getElementById('reminderTitle').value = '';
    document.getElementById('reminderDescription').value = '';
    
    OctaneUtils.showAlert('✅ Reminder created successfully', 'success');
}

function useTemplate(templateType) {
    const templates = {
        release: {
            title: 'Compile in Release Mode',
            description: 'Remember to switch from Debug to Release mode before building the final version. Check all build configurations and ensure optimizations are enabled.',
            priority: 'high',
            category: 'deployment'
        },
        debug: {
            title: 'Disable Debug Features',
            description: 'Remove or disable all debug features, console logs, and development-only code before deployment to production.',
            priority: 'high',
            category: 'deployment'
        },
        backup: {
            title: 'Create Backup',
            description: 'Create a full backup of the current working version before making major changes or updates.',
            priority: 'medium',
            category: 'maintenance'
        },
        test: {
            title: 'Run Full Test Suite',
            description: 'Execute all unit tests, integration tests, and manual testing procedures before releasing.',
            priority: 'high',
            category: 'testing'
        },
        security: {
            title: 'Security Review',
            description: 'Review all security settings, authentication mechanisms, and access controls.',
            priority: 'critical',
            category: 'security'
        },
        docs: {
            title: 'Update Documentation',
            description: 'Update all relevant documentation including API docs, user guides, and changelog.',
            priority: 'medium',
            category: 'general'
        }
    };
    
    const template = templates[templateType];
    if (template) {
        document.getElementById('reminderTitle').value = template.title;
        document.getElementById('reminderDescription').value = template.description;
        document.getElementById('reminderPriority').value = template.priority;
        document.getElementById('reminderCategory').value = template.category;
        
        OctaneUtils.showAlert('📝 Template loaded', 'info');
    }
}

function renderReminders() {
    const activeList = document.getElementById('remindersList');
    const completedList = document.getElementById('completedRemindersList');
    
    const activeReminders = reminders.filter(r => !r.completed);
    const completedReminders = reminders.filter(r => r.completed);
    
    // Render active reminders
    if (activeReminders.length === 0) {
        activeList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-clipboard-list"></i>
                <p>No active reminders</p>
                <p>Create a new reminder to get started!</p>
            </div>
        `;
    } else {
        activeList.innerHTML = activeReminders.map(reminder => renderReminderItem(reminder)).join('');
    }
    
    // Render completed reminders
    if (completedReminders.length === 0) {
        completedList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-check-circle"></i>
                <p>No completed reminders yet</p>
            </div>
        `;
    } else {
        completedList.innerHTML = completedReminders.slice(0, 10).map(reminder => renderReminderItem(reminder, true)).join('');
    }
}

function renderReminderItem(reminder, isCompleted = false) {
    const priorityEmoji = {
        low: '🟢',
        medium: '🟡',
        high: '🔴',
        critical: '🚨'
    };
    
    const categoryEmoji = {
        development: '💻',
        deployment: '🚀',
        testing: '🧪',
        security: '🛡️',
        maintenance: '🔧',
        general: '📋'
    };
    
    return `
        <div class="reminder-item ${isCompleted ? 'completed' : ''}" data-id="${reminder.id}">
            <div class="reminder-header">
                <div>
                    <div class="reminder-title">${reminder.title}</div>
                    <div class="reminder-meta">
                        <span class="reminder-priority ${reminder.priority}">
                            ${priorityEmoji[reminder.priority]} ${reminder.priority.toUpperCase()}
                        </span>
                        <span class="reminder-category">
                            ${categoryEmoji[reminder.category]} ${reminder.category}
                        </span>
                        <span>Created: ${OctaneUtils.formatDate(reminder.created)}</span>
                    </div>
                </div>
            </div>
            ${reminder.description ? `<div class="reminder-description">${reminder.description}</div>` : ''}
            <div class="reminder-actions">
                ${!isCompleted ? `
                    <button class="btn btn-sm btn-success" onclick="completeReminder('${reminder.id}')">
                        <i class="fas fa-check"></i> Complete
                    </button>
                    <button class="btn btn-sm btn-secondary" onclick="editReminder('${reminder.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                ` : `
                    <button class="btn btn-sm btn-warning" onclick="uncompleteReminder('${reminder.id}')">
                        <i class="fas fa-undo"></i> Restore
                    </button>
                `}
                <button class="btn btn-sm btn-danger" onclick="deleteReminder('${reminder.id}')">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `;
}

function completeReminder(id) {
    const reminder = reminders.find(r => r.id === id);
    if (reminder) {
        reminder.completed = true;
        reminder.completedAt = new Date().toISOString();
        saveReminders();
        renderReminders();
        OctaneUtils.showAlert('✅ Reminder completed', 'success');
    }
}

function uncompleteReminder(id) {
    const reminder = reminders.find(r => r.id === id);
    if (reminder) {
        reminder.completed = false;
        delete reminder.completedAt;
        saveReminders();
        renderReminders();
        OctaneUtils.showAlert('🔄 Reminder restored', 'info');
    }
}

function editReminder(id) {
    const reminder = reminders.find(r => r.id === id);
    if (reminder) {
        document.getElementById('reminderTitle').value = reminder.title;
        document.getElementById('reminderDescription').value = reminder.description;
        document.getElementById('reminderPriority').value = reminder.priority;
        document.getElementById('reminderCategory').value = reminder.category;
        
        // Remove the old reminder
        deleteReminder(id, false);
        
        OctaneUtils.showAlert('📝 Reminder loaded for editing', 'info');
    }
}

function deleteReminder(id, showAlert = true) {
    if (showAlert && !confirm('Are you sure you want to delete this reminder?')) return;
    
    reminders = reminders.filter(r => r.id !== id);
    saveReminders();
    renderReminders();
    
    if (showAlert) {
        OctaneUtils.showAlert('🗑️ Reminder deleted', 'info');
    }
}

function filterReminders() {
    const priorityFilter = document.getElementById('priorityFilter').value;
    const categoryFilter = document.getElementById('categoryFilter').value;
    
    const reminderItems = document.querySelectorAll('#remindersList .reminder-item');
    
    reminderItems.forEach(item => {
        const priority = item.querySelector('.reminder-priority').textContent.toLowerCase().includes(priorityFilter) || priorityFilter === 'all';
        const category = item.querySelector('.reminder-category').textContent.toLowerCase().includes(categoryFilter) || categoryFilter === 'all';
        
        item.style.display = (priority && category) ? 'block' : 'none';
    });
}

function refreshReminders() {
    loadReminders();
    OctaneUtils.showAlert('🔄 Reminders refreshed', 'info');
}

function clearCompleted() {
    if (!confirm('Are you sure you want to delete all completed reminders?')) return;
    
    const completedCount = reminders.filter(r => r.completed).length;
    reminders = reminders.filter(r => !r.completed);
    saveReminders();
    renderReminders();
    
    OctaneUtils.showAlert(`🗑️ Deleted ${completedCount} completed reminders`, 'info');
}
