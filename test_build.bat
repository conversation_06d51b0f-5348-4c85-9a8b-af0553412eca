@echo off
setlocal enabledelayedexpansion

echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🧪 OCTANE BUILD TESTER 🧪                      ║
echo ║                      by Octane Team                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

echo 🔍 Starting comprehensive build testing...
echo.

REM Test 1: Check build system availability
echo ═══════════════════════════════════════════════════════════════
echo TEST 1: Build System Availability
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

set ESP_IDF_AVAILABLE=0
set PLATFORMIO_AVAILABLE=0

where idf.py >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set ESP_IDF_AVAILABLE=1
    echo ✅ ESP-IDF found
) else (
    echo ❌ ESP-IDF not found
)

where pio >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    set PLATFORMIO_AVAILABLE=1
    echo ✅ PlatformIO found
) else (
    echo ❌ PlatformIO not found
)

if %ESP_IDF_AVAILABLE% EQU 1 (
    set /a PASSED_TESTS+=1
    echo ✅ TEST 1 PASSED: At least one build system available
) else if %PLATFORMIO_AVAILABLE% EQU 1 (
    set /a PASSED_TESTS+=1
    echo ✅ TEST 1 PASSED: At least one build system available
) else (
    set /a FAILED_TESTS+=1
    echo ❌ TEST 1 FAILED: No build systems available
)

echo.

REM Test 2: Source file validation
echo ═══════════════════════════════════════════════════════════════
echo TEST 2: Source File Validation
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

set SOURCE_FILES_OK=1

REM Check main firmware file
if exist "esp32-firmware\main\main.c" (
    echo ✅ main.c found
    
    REM Check for required includes
    findstr "#include.*tinyusb" esp32-firmware\main\main.c >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ TinyUSB includes found
    ) else (
        echo ❌ TinyUSB includes missing
        set SOURCE_FILES_OK=0
    )
    
    REM Check for version defines
    findstr "FIRMWARE_VERSION_STRING" esp32-firmware\main\main.c >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ Version information found
    ) else (
        echo ❌ Version information missing
        set SOURCE_FILES_OK=0
    )
    
    REM Check for HID mouse functionality
    findstr "hid_mouse_report_t" esp32-firmware\main\main.c >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ HID mouse structures found
    ) else (
        echo ❌ HID mouse structures missing
        set SOURCE_FILES_OK=0
    )
    
) else (
    echo ❌ main.c not found
    set SOURCE_FILES_OK=0
)

REM Check CMakeLists.txt
if exist "esp32-firmware\CMakeLists.txt" (
    echo ✅ CMakeLists.txt found
) else (
    echo ❌ CMakeLists.txt missing
    set SOURCE_FILES_OK=0
)

REM Check sdkconfig.defaults
if exist "esp32-firmware\sdkconfig.defaults" (
    echo ✅ sdkconfig.defaults found
    
    findstr "CONFIG_TINYUSB_ENABLED" esp32-firmware\sdkconfig.defaults >nul
    if %ERRORLEVEL% EQU 0 (
        echo ✅ TinyUSB configuration found
    ) else (
        echo ❌ TinyUSB configuration missing
        set SOURCE_FILES_OK=0
    )
) else (
    echo ❌ sdkconfig.defaults missing
    set SOURCE_FILES_OK=0
)

if %SOURCE_FILES_OK% EQU 1 (
    set /a PASSED_TESTS+=1
    echo ✅ TEST 2 PASSED: All source files valid
) else (
    set /a FAILED_TESTS+=1
    echo ❌ TEST 2 FAILED: Source file issues detected
)

echo.

REM Test 3: ESP-IDF Build Test (if available)
if %ESP_IDF_AVAILABLE% EQU 1 (
    echo ═══════════════════════════════════════════════════════════════
    echo TEST 3: ESP-IDF Build Test
    echo ═══════════════════════════════════════════════════════════════
    
    set /a TOTAL_TESTS+=1
    
    cd esp32-firmware
    
    echo 🔧 Setting ESP32-S2 target...
    idf.py set-target esp32s2 >nul 2>&1
    
    echo 🔨 Building firmware...
    idf.py build >build_test.log 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ ESP-IDF build successful
        
        REM Check output files
        if exist "build\octane_hid_firmware.bin" (
            for %%F in ("build\octane_hid_firmware.bin") do (
                if %%~zF GTR 50000 (
                    echo ✅ Firmware binary valid size: %%~zF bytes
                    set /a PASSED_TESTS+=1
                    echo ✅ TEST 3 PASSED: ESP-IDF build successful
                ) else (
                    echo ❌ Firmware binary too small: %%~zF bytes
                    set /a FAILED_TESTS+=1
                    echo ❌ TEST 3 FAILED: Firmware binary invalid
                )
            )
        ) else (
            echo ❌ Firmware binary not generated
            set /a FAILED_TESTS+=1
            echo ❌ TEST 3 FAILED: No firmware output
        )
    ) else (
        echo ❌ ESP-IDF build failed
        echo 📋 Build log (last 10 lines):
        tail -10 build_test.log 2>nul || (
            echo Build log not available
        )
        set /a FAILED_TESTS+=1
        echo ❌ TEST 3 FAILED: ESP-IDF build error
    )
    
    cd ..
    echo.
)

REM Test 4: PlatformIO Build Test (if available)
if %PLATFORMIO_AVAILABLE% EQU 1 (
    echo ═══════════════════════════════════════════════════════════════
    echo TEST 4: PlatformIO Build Test
    echo ═══════════════════════════════════════════════════════════════
    
    set /a TOTAL_TESTS+=1
    
    cd esp32-firmware
    
    echo 🔨 Building with PlatformIO...
    pio run -e esp32-s2-saola-1 >pio_test.log 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ PlatformIO build successful
        
        REM Check output files
        if exist ".pio\build\esp32-s2-saola-1\firmware.bin" (
            for %%F in (".pio\build\esp32-s2-saola-1\firmware.bin") do (
                if %%~zF GTR 50000 (
                    echo ✅ PlatformIO firmware valid size: %%~zF bytes
                    set /a PASSED_TESTS+=1
                    echo ✅ TEST 4 PASSED: PlatformIO build successful
                ) else (
                    echo ❌ PlatformIO firmware too small: %%~zF bytes
                    set /a FAILED_TESTS+=1
                    echo ❌ TEST 4 FAILED: PlatformIO firmware invalid
                )
            )
        ) else (
            echo ❌ PlatformIO firmware not generated
            set /a FAILED_TESTS+=1
            echo ❌ TEST 4 FAILED: No PlatformIO output
        )
    ) else (
        echo ❌ PlatformIO build failed
        echo 📋 Build log (last 10 lines):
        tail -10 pio_test.log 2>nul || (
            echo Build log not available
        )
        set /a FAILED_TESTS+=1
        echo ❌ TEST 4 FAILED: PlatformIO build error
    )
    
    cd ..
    echo.
)

REM Test 5: C# Flasher Build Test
echo ═══════════════════════════════════════════════════════════════
echo TEST 5: C# Flasher Build Test
echo ═══════════════════════════════════════════════════════════════

set /a TOTAL_TESTS+=1

where dotnet >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ .NET SDK found
    
    cd OctaneFlasher
    
    echo 🔨 Building C# flasher...
    dotnet build -c Release >flasher_test.log 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ C# flasher build successful
        set /a PASSED_TESTS+=1
        echo ✅ TEST 5 PASSED: C# flasher build successful
    ) else (
        echo ❌ C# flasher build failed
        echo 📋 Build log (last 10 lines):
        tail -10 flasher_test.log 2>nul || (
            echo Build log not available
        )
        set /a FAILED_TESTS+=1
        echo ❌ TEST 5 FAILED: C# flasher build error
    )
    
    cd ..
) else (
    echo ❌ .NET SDK not found
    set /a FAILED_TESTS+=1
    echo ❌ TEST 5 FAILED: .NET SDK not available
)

echo.

REM Test Summary
echo ═══════════════════════════════════════════════════════════════
echo TEST SUMMARY
echo ═══════════════════════════════════════════════════════════════

echo Total tests: %TOTAL_TESTS%
echo Passed: %PASSED_TESTS%
echo Failed: %FAILED_TESTS%

set /a SUCCESS_RATE=%PASSED_TESTS%*100/%TOTAL_TESTS%
echo Success rate: %SUCCESS_RATE%%%

echo.

if %FAILED_TESTS% EQU 0 (
    echo 🎉 ALL TESTS PASSED!
    echo    Build system is working correctly
    echo    Ready for production builds
    exit /b 0
) else (
    echo ❌ SOME TESTS FAILED
    echo    Please fix the issues before proceeding
    echo    Check build logs for detailed error information
    exit /b 1
)

pause
