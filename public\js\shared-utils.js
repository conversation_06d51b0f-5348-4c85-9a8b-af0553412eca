// Octane Admin Panel - Shared Utilities
class OctaneUtils {
    // Show notification/alert
    static showAlert(message, type = 'info', duration = 4000) {
        const alertContainer = document.getElementById('alertContainer') || document.body;
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            min-width: 300px;
            max-width: 500px;
            animation: slideIn 0.3s ease;
        `;
        alert.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 15px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: bold;
        `;
        closeBtn.onclick = () => alert.remove();
        alert.appendChild(closeBtn);

        alertContainer.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, duration);
    }

    // Copy text to clipboard
    static async copyToClipboard(text, successMessage = 'Copied to clipboard!') {
        try {
            await navigator.clipboard.writeText(text);
            this.showAlert(successMessage, 'success');
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                this.showAlert(successMessage, 'success');
            } catch (err) {
                this.showAlert('Failed to copy to clipboard', 'danger');
            }

            document.body.removeChild(textArea);
        }
    }

    // Format date
    static formatDate(dateString, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };

        const finalOptions = { ...defaultOptions, ...options };
        return new Date(dateString).toLocaleDateString('en-US', finalOptions);
    }

    // Format duration
    static formatDuration(duration) {
        const durations = {
            '1day': '1 Day',
            '1week': '1 Week',
            '1month': '1 Month',
            '3months': '3 Months',
            '6months': '6 Months',
            '1year': '1 Year',
            'lifetime': 'Lifetime'
        };
        return durations[duration] || duration;
    }

    // API request helper
    static async apiRequest(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Debounce function
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Get current time formatted
    static getCurrentTime() {
        return new Date().toLocaleString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    // Validate email
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

// Firebase Authentication Helper
class OctaneAuth {
    static isAuthenticated() {
        // Check if Firebase user is authenticated
        return firebase.auth().currentUser !== null;
    }

    static async requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = '/';
            return false;
        }
        return true;
    }

    static async logout() {
        try {
            await firebase.auth().signOut();
            OctaneUtils.showAlert('Logged out successfully', 'success');
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } catch (error) {
            OctaneUtils.showAlert('Error logging out: ' + error.message, 'danger');
        }
    }

    static getCurrentUser() {
        return firebase.auth().currentUser;
    }
}

// Initialize shared functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add logout functionality to logout buttons
    const logoutBtns = document.querySelectorAll('.logout-btn, [data-action="logout"]');
    logoutBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            OctaneAuth.logout();
        });
    });

    // Add current time display if element exists
    const timeDisplay = document.getElementById('currentTime');
    if (timeDisplay) {
        const updateTime = () => {
            timeDisplay.textContent = OctaneUtils.getCurrentTime();
        };
        updateTime();
        setInterval(updateTime, 1000);
    }
});

// Export for use in other scripts
window.OctaneUtils = OctaneUtils;
window.OctaneAuth = OctaneAuth;
