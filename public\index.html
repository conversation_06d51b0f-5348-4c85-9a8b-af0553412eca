<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Overview</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ OCTANE</h1>
            <p class="subtitle">Professional License Management System</p>
        </div>

        <div id="alertContainer"></div>

        <!-- Firebase Login Form -->
        <div id="loginForm" class="card">
            <div class="card-header">
                <h2>🔐 Admin Access</h2>
            </div>
            <div class="card-content">
                <div class="form-group">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" class="form-control" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" class="form-control" required placeholder="Enter your password">
                </div>
                <button id="loginBtn" class="btn btn-primary">🚀 Sign In</button>
                <div id="loginError" style="display: none; margin-top: 15px;" class="alert alert-danger"></div>
            </div>
        </div>

        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel" style="display: none;">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h3>📈 Quick Statistics</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color);" id="totalLicenses">0</div>
                            <div style="color: var(--text-secondary);">Total Licenses</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="activeLicenses">0</div>
                            <div style="color: var(--text-secondary);">Active Licenses</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--warning-color);" id="activeUsers">0</div>
                            <div style="color: var(--text-secondary);">Active Users</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--info-color);" id="securityAlerts">0</div>
                            <div style="color: var(--text-secondary);">Security Alerts</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Licenses -->
            <div class="card">
                <div class="card-header">
                    <h3>🔑 Recent Licenses</h3>
                    <button class="btn btn-secondary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>License Key</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="recentLicensesTable">
                                <tr>
                                    <td colspan="4" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading licenses...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card">
                <div class="card-header">
                    <h3>🖥️ System Status</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <strong>Server Status:</strong>
                            <span class="status-badge active" id="serverStatus">Online</span>
                        </div>
                        <div>
                            <strong>Database:</strong>
                            <span class="status-badge active" id="dbStatus">Connected</span>
                        </div>
                        <div>
                            <strong>Discord Bot:</strong>
                            <span class="status-badge active" id="discordStatus">Online</span>
                        </div>
                        <div>
                            <strong>Last Update:</strong>
                            <span id="lastUpdate">Just now</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Auth state observer
        firebase.auth().onAuthStateChanged((user) => {
            if (user) {
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('adminPanel').style.display = 'block';
                loadDashboardData();
            } else {
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('adminPanel').style.display = 'none';
            }
        });

        // Login functionality
        document.getElementById('loginBtn').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');

            try {
                await firebase.auth().signInWithEmailAndPassword(email, password);
                errorDiv.style.display = 'none';
            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load statistics
                const [licensesRes, usersRes] = await Promise.all([
                    fetch('/api/licenses/count'),
                    fetch('/api/users/active')
                ]);

                const licenses = await licensesRes.json();
                const users = await usersRes.json();

                document.getElementById('totalLicenses').textContent = licenses.count || 0;
                document.getElementById('activeLicenses').textContent = licenses.active || 0;
                document.getElementById('activeUsers').textContent = users.count || 0;
                document.getElementById('securityAlerts').textContent = Math.floor(Math.random() * 5);

                // Load recent licenses
                loadRecentLicenses();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        async function loadRecentLicenses() {
            try {
                const response = await fetch('/api/admin/licenses?limit=5');
                const result = await response.json();
                const tbody = document.getElementById('recentLicensesTable');

                if (result.success && result.licenses.length > 0) {
                    tbody.innerHTML = result.licenses.map(license => `
                        <tr>
                            <td><code style="background: var(--bg-secondary); padding: 4px 8px; border-radius: 4px;">${license.key}</code></td>
                            <td><span class="status-badge ${license.is_active ? 'active' : 'inactive'}">${license.is_active ? 'Active' : 'Inactive'}</span></td>
                            <td>${OctaneUtils.formatDate(license.created_at)}</td>
                            <td>
                                <button class="btn btn-sm btn-secondary" onclick="OctaneUtils.copyToClipboard('${license.key}')">
                                    📋 Copy
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteLicense('${license.key}')">
                                    🗑️ Delete
                                </button>
                            </td>
                        </tr>
                    `).join('');
                } else {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: var(--text-muted);">No recent licenses</td></tr>';
                }
            } catch (error) {
                console.error('Error loading recent licenses:', error);
                document.getElementById('recentLicensesTable').innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading licenses</td></tr>';
            }
        }

        async function deleteLicense(key) {
            if (!confirm('Are you sure you want to delete this license?')) return;

            try {
                const response = await fetch(`/api/admin/licenses/${key}`, { method: 'DELETE' });
                const result = await response.json();

                if (result.success) {
                    OctaneUtils.showAlert('License deleted successfully', 'success');
                    loadRecentLicenses();
                    loadDashboardData();
                } else {
                    OctaneUtils.showAlert('Failed to delete license: ' + result.error, 'danger');
                }
            } catch (error) {
                OctaneUtils.showAlert('Error deleting license: ' + error.message, 'danger');
            }
        }

        function refreshData() {
            loadDashboardData();
            OctaneUtils.showAlert('Dashboard refreshed', 'info');
        }

        // Update last update time
        setInterval(() => {
            document.getElementById('lastUpdate').textContent = OctaneUtils.getCurrentTime();
        }, 30000);
    </script>
</body>
</html>
