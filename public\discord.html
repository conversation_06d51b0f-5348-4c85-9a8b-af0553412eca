<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Discord Management</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>

            <!-- Bot Status -->
            <div class="card">
                <div class="card-header">
                    <h3>🤖 Bot Status</h3>
                    <button class="btn btn-secondary" onclick="refreshBotStatus()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="botStatus">🔄</div>
                            <div style="color: var(--text-secondary);">Bot Status</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 1.2rem; font-weight: bold; color: var(--info-color);" id="botUptime">Loading...</div>
                            <div style="color: var(--text-secondary);">Uptime</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--warning-color);" id="guildCount">0</div>
                            <div style="color: var(--text-secondary);">Servers</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color);" id="commandsToday">0</div>
                            <div style="color: var(--text-secondary);">Commands Today</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bot Management -->
            <div class="card">
                <div class="card-header">
                    <h3>🔧 Bot Management</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <h4>🎮 Testing & Diagnostics</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-primary" onclick="testBot()">
                                    <i class="fas fa-play"></i> Test Bot Connection
                                </button>
                                <button class="btn btn-secondary" onclick="testCommands()">
                                    <i class="fas fa-terminal"></i> Test Commands
                                </button>
                                <button class="btn btn-info" onclick="checkPermissions()">
                                    <i class="fas fa-shield-alt"></i> Check Permissions
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>📊 Reports & Logs</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-success" onclick="generateDailyReport()">
                                    <i class="fas fa-chart-bar"></i> Generate Daily Report
                                </button>
                                <button class="btn btn-secondary" onclick="toggleBotLogs()">
                                    <i class="fas fa-file-alt"></i> View Bot Logs
                                </button>
                                <button class="btn btn-info" onclick="exportStats()">
                                    <i class="fas fa-download"></i> Export Statistics
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>⚙️ Configuration</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-warning" onclick="updateBotToken()">
                                    <i class="fas fa-key"></i> Update Bot Token
                                </button>
                                <button class="btn btn-secondary" onclick="configureWebhooks()">
                                    <i class="fas fa-webhook"></i> Configure Webhooks
                                </button>
                                <button class="btn btn-danger" onclick="restartBot()">
                                    <i class="fas fa-redo"></i> Restart Bot
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Configuration -->
            <div class="card">
                <div class="card-header">
                    <h3>📋 Current Configuration</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <h4>🔗 Webhooks</h4>
                            <div style="background: var(--bg-secondary); padding: 15px; border-radius: var(--border-radius);" id="webhooksList">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Loading webhooks...
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4>🎯 Bot Commands</h4>
                            <div style="background: var(--bg-secondary); padding: 15px; border-radius: var(--border-radius);">
                                <div style="margin-bottom: 8px;"><code>/stats</code> - System statistics</div>
                                <div style="margin-bottom: 8px;"><code>/logs</code> - View recent logs</div>
                                <div style="margin-bottom: 8px;"><code>/users</code> - Active user count</div>
                                <div style="margin-bottom: 8px;"><code>/licenses</code> - License statistics</div>
                                <div style="margin-bottom: 8px;"><code>/emergency</code> - Emergency disable auth</div>
                                <div><code>/restart</code> - Restart services</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bot Activity -->
            <div class="card">
                <div class="card-header">
                    <h3>📊 Recent Bot Activity</h3>
                </div>
                <div class="card-content">
                    <div id="botActivity" style="max-height: 400px; overflow-y: auto;">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading bot activity...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bot Logs Panel (Hidden by default) -->
            <div class="card" id="botLogsPanel" style="display: none;">
                <div class="card-header">
                    <h3>📋 Discord Bot Logs</h3>
                    <button class="btn btn-sm btn-secondary" onclick="refreshBotLogs()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div id="botLogsContainer" style="max-height: 400px; overflow-y: auto; background: var(--bg-dark); padding: 15px; border-radius: var(--border-radius); font-family: monospace;">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading bot logs...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                loadDiscordData();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // Discord management functions
        function loadDiscordData() {
            refreshBotStatus();
            loadBotActivity();
            loadWebhooks();
        }

        function loadWebhooks() {
            fetch('/api/discord/webhooks')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('webhooksList');

                    if (result.success && result.webhooks) {
                        container.innerHTML = Object.entries(result.webhooks).map(([name, url]) => `
                            <div style="margin-bottom: 10px;">
                                <strong>${name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}:</strong>
                                <div style="font-family: monospace; font-size: 0.8rem; color: var(--text-muted); word-break: break-all;">
                                    ${url ? url.substring(0, 20) + '...' : 'Not configured'}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="color: var(--text-muted);">No webhooks configured</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading webhooks:', error);
                    document.getElementById('webhooksList').innerHTML = '<div style="color: var(--danger-color);">Error loading webhooks</div>';
                });
        }

        function refreshBotStatus() {
            fetch('/api/discord/status')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        document.getElementById('botStatus').textContent = result.online ? '✅ Online' : '❌ Offline';
                        document.getElementById('botUptime').textContent = result.uptime || 'Unknown';
                        document.getElementById('guildCount').textContent = result.guilds || 0;
                        document.getElementById('commandsToday').textContent = result.commandsToday || 0;
                    } else {
                        document.getElementById('botStatus').textContent = '❌ Error';
                    }
                })
                .catch(error => {
                    console.error('Error loading bot status:', error);
                    document.getElementById('botStatus').textContent = '❌ Error';
                });
        }

        function loadBotActivity() {
            fetch('/api/discord/activity')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('botActivity');

                    if (result.success && result.activities.length > 0) {
                        container.innerHTML = result.activities.map(activity => `
                            <div style="padding: 10px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${activity.type}</strong> - ${activity.description}
                                    <div style="font-size: 0.8rem; color: var(--text-muted);">${activity.details}</div>
                                </div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                    ${OctaneUtils.formatDate(activity.timestamp)}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-muted);">No recent bot activity</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading bot activity:', error);
                    document.getElementById('botActivity').innerHTML = '<div style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading activity</div>';
                });
        }

        function testBot() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            btn.disabled = true;

            fetch('/api/discord/test', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ Bot test successful!', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Bot test failed: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error testing bot: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function testCommands() {
            OctaneUtils.showAlert('🧪 Testing all bot commands...', 'info');

            fetch('/api/discord/test-commands', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ All commands tested successfully!', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Some commands failed: ' + result.error, 'warning');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error testing commands: ' + error.message, 'danger');
                });
        }

        function checkPermissions() {
            fetch('/api/discord/permissions')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const permissions = result.permissions;
                        const message = `Bot Permissions:\n${permissions.map(p => `✅ ${p}`).join('\n')}`;
                        alert(message);
                    } else {
                        OctaneUtils.showAlert('❌ Error checking permissions: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error checking permissions: ' + error.message, 'danger');
                });
        }

        function generateDailyReport() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
            btn.disabled = true;

            fetch('/api/discord/daily-report', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ Daily report sent to Discord!', 'success');
                    } else {
                        OctaneUtils.showAlert('❌ Failed to generate report: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error generating report: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function toggleBotLogs() {
            const panel = document.getElementById('botLogsPanel');
            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                loadBotLogs();
            } else {
                panel.style.display = 'none';
            }
        }

        function loadBotLogs() {
            fetch('/api/discord/logs')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('botLogsContainer');

                    if (result.success && result.logs.length > 0) {
                        container.innerHTML = result.logs.map(log => `
                            <div style="margin-bottom: 5px; color: ${getLogColor(log.level)};">
                                <span style="color: var(--text-muted);">[${new Date(log.timestamp).toLocaleString()}]</span>
                                <span style="color: ${getLogColor(log.level)}; font-weight: bold;">[${log.level.toUpperCase()}]</span>
                                ${log.message}
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="color: var(--text-muted);">No recent bot logs</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading bot logs:', error);
                    document.getElementById('botLogsContainer').innerHTML = '<div style="color: var(--danger-color);">Error loading bot logs</div>';
                });
        }

        function refreshBotLogs() {
            loadBotLogs();
            OctaneUtils.showAlert('📋 Bot logs refreshed', 'info');
        }

        function getLogColor(level) {
            switch(level.toLowerCase()) {
                case 'error': return 'var(--danger-color)';
                case 'warning': return 'var(--warning-color)';
                case 'info': return 'var(--info-color)';
                default: return 'var(--text-primary)';
            }
        }

        function exportStats() {
            fetch('/api/discord/export-stats')
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `discord-stats-${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    window.URL.revokeObjectURL(url);
                    OctaneUtils.showAlert('📊 Statistics exported!', 'success');
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error exporting stats: ' + error.message, 'danger');
                });
        }

        function updateBotToken() {
            const newToken = prompt('Enter new Discord bot token:');
            if (!newToken) return;

            if (!confirm('Update bot token? This will restart the bot.')) return;

            fetch('/api/discord/update-token', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ token: newToken })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert('✅ Bot token updated successfully!', 'success');
                    setTimeout(refreshBotStatus, 3000);
                } else {
                    OctaneUtils.showAlert('❌ Failed to update token: ' + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert('❌ Error updating token: ' + error.message, 'danger');
            });
        }

        function configureWebhooks() {
            OctaneUtils.showAlert('🔗 Webhook configuration feature coming soon...', 'info');
        }

        function restartBot() {
            if (!confirm('Restart the Discord bot? This may cause temporary downtime.')) return;

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Restarting...';
            btn.disabled = true;

            fetch('/api/discord/restart', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ Bot restarted successfully!', 'success');
                        setTimeout(refreshBotStatus, 5000);
                    } else {
                        OctaneUtils.showAlert('❌ Failed to restart bot: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error restarting bot: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }
    </script>
</body>
</html>
