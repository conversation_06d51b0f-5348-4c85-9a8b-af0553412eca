﻿#pragma checksum "..\..\..\..\..\Views\SecureLoginWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "24786C7BE8AB22C6D81D606F476D948CAF9E1C47"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.Views {
    
    
    /// <summary>
    /// SecureLoginWindow
    /// </summary>
    public partial class SecureLoginWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LicenseKeyTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox HardwareIdTextBox;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusTextBlock;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar AuthProgressBar;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AuthenticateButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;component/views/secureloginwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 27 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
            ((System.Windows.Controls.Grid)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Header_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 40 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.LicenseKeyTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 77 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
            this.LicenseKeyTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.LicenseKeyTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.HardwareIdTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.StatusTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AuthProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 8:
            this.AuthenticateButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\..\Views\SecureLoginWindow.xaml"
            this.AuthenticateButton.Click += new System.Windows.RoutedEventHandler(this.AuthenticateButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

