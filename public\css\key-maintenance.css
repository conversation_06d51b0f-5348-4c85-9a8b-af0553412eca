/* Key Maintenance Specific Styles */

.header-stats {
    display: flex;
    gap: 30px;
    align-items: center;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* License table specific styles */
.table tbody tr:hover {
    background: rgba(255, 102, 0, 0.05);
}

.table code {
    background: var(--bg-secondary);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.85rem;
    color: var(--primary-color);
    border: 1px solid var(--border-color);
}

.license-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Search and filter styles */
.search-filter-bar {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-filter-bar .form-control {
    max-width: 300px;
}

/* Quick actions grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
}

.quick-action-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.quick-action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quick-action-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.quick-action-card h4 {
    color: var(--text-primary);
    margin-bottom: 5px;
}

.quick-action-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* License creation form */
.license-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: end;
}

/* Duration selector */
.duration-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.duration-btn {
    padding: 8px 16px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.duration-btn:hover {
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.duration-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* License status indicators */
.license-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.license-status.active {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.license-status.expired {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.license-status.suspended {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

/* HWID display */
.hwid-display {
    font-family: 'JetBrains Mono', monospace;
    font-size: 0.8rem;
    color: var(--text-muted);
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hwid-display.bound {
    color: var(--warning-color);
}

/* Bulk actions */
.bulk-actions {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.bulk-actions .selected-count {
    margin-left: auto;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* License key copy button */
.copy-key-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 2px 5px;
    border-radius: 3px;
    transition: var(--transition);
}

.copy-key-btn:hover {
    background: rgba(255, 102, 0, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.pagination button:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination .current-page {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 100;
}

.loading-spinner {
    color: var(--primary-color);
    font-size: 2rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .header-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .license-form-grid {
        grid-template-columns: 1fr;
    }
    
    .duration-selector {
        justify-content: center;
    }
    
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bulk-actions .selected-count {
        margin-left: 0;
        text-align: center;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .license-actions {
        flex-direction: column;
    }
}

/* Animation for new license creation */
@keyframes licenseCreated {
    0% {
        background: rgba(255, 102, 0, 0.3);
        transform: scale(1.02);
    }
    100% {
        background: transparent;
        transform: scale(1);
    }
}

.license-row.newly-created {
    animation: licenseCreated 2s ease-out;
}

/* Export modal styles */
.export-modal {
    max-width: 600px;
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.export-option {
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.export-option:hover {
    border-color: var(--primary-color);
    background: rgba(255, 102, 0, 0.05);
}

.export-option.selected {
    border-color: var(--primary-color);
    background: rgba(255, 102, 0, 0.1);
}

.export-option i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.export-option h4 {
    color: var(--text-primary);
    margin-bottom: 5px;
}

.export-option p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}
