﻿#pragma checksum "..\..\..\..\..\Views\RustMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3DFC52970312F61653A2E1E6AFF90652B1829481"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using RecoilController.UI.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.Views {
    
    
    /// <summary>
    /// RustMainWindow
    /// </summary>
    public partial class RustMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 153 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MainToggle;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartStopButton;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmergencyStop;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal RecoilController.UI.Controls.WeaponSelectionControl WeaponSelection;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentWeaponText;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShotsCountText;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTimeText;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RecoilToggle;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip RecoilStatusChip;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HipfireToggle;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip HipfireStatusChip;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton HumanisationToggle;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip HumanisationStatusChip;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SensitivityValue;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalSlider;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HorizontalValue;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FovSlider;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FovValue;
        
        #line default
        #line hidden
        
        
        #line 335 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalSlider;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VerticalValue;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal RecoilController.UI.Controls.RecoilSettingsControl RecoilSettings;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalShotsText;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccuracyText;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgRecoilText;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecoilMovementText;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionDurationText;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecurityStatusText;
        
        #line default
        #line hidden
        
        
        #line 427 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetStatsButton;
        
        #line default
        #line hidden
        
        
        #line 429 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportStatsButton;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 433 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoadConfigButton;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal RecoilController.UI.Controls.RecoilSettingsControl SettingsPanel;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip LicenseStatusChip;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HardwareIdText;
        
        #line default
        #line hidden
        
        
        #line 476 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.Chip SecurityStatusChip;
        
        #line default
        #line hidden
        
        
        #line 481 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AboutButton;
        
        #line default
        #line hidden
        
        
        #line 483 "..\..\..\..\..\Views\RustMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;V1.0.0.0;component/views/rustmainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\RustMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.MainToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 3:
            this.StartStopButton = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.EmergencyStop = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.WeaponSelection = ((RecoilController.UI.Controls.WeaponSelectionControl)(target));
            return;
            case 6:
            this.CurrentWeaponText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ShotsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.SessionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.RecoilToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 10:
            this.RecoilStatusChip = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 11:
            this.HipfireToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 12:
            this.HipfireStatusChip = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 13:
            this.HumanisationToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 14:
            this.HumanisationStatusChip = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 15:
            this.SensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.SensitivityValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.HorizontalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 18:
            this.HorizontalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.FovSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 20:
            this.FovValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.VerticalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 22:
            this.VerticalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.RecoilSettings = ((RecoilController.UI.Controls.RecoilSettingsControl)(target));
            return;
            case 24:
            this.TotalShotsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.AccuracyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.AvgRecoilText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.RecoilMovementText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.SessionDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.SecurityStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.ResetStatsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 31:
            this.ExportStatsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 32:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 33:
            this.LoadConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 34:
            this.SettingsPanel = ((RecoilController.UI.Controls.RecoilSettingsControl)(target));
            return;
            case 35:
            this.LicenseStatusChip = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 36:
            this.HardwareIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.SecurityStatusChip = ((MaterialDesignThemes.Wpf.Chip)(target));
            return;
            case 38:
            this.AboutButton = ((System.Windows.Controls.Button)(target));
            return;
            case 39:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

