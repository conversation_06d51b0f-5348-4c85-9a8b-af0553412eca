@echo off
setlocal enabledelayedexpansion

echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🚀 OCTANE DISTRIBUTION CREATOR 🚀              ║
echo ║                      by Octane Team                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Get version from command line or prompt
set VERSION=%1
if "%VERSION%"=="" (
    set /p VERSION="Enter version (e.g., 2.0.0): "
)

if "%VERSION%"=="" (
    echo ❌ Version is required
    pause
    exit /b 1
)

echo 🎯 Creating distribution packages for version %VERSION%
echo.

REM Create distribution directory
set DIST_DIR=dist\Octane-Flasher-v%VERSION%
if exist "%DIST_DIR%" (
    echo 🧹 Cleaning existing distribution directory...
    rmdir /s /q "%DIST_DIR%"
)

mkdir "%DIST_DIR%" 2>nul
mkdir "%DIST_DIR%\docs" 2>nul
mkdir "%DIST_DIR%\source" 2>nul
mkdir "%DIST_DIR%\tools" 2>nul

echo ✅ Distribution directory created: %DIST_DIR%
echo.

REM Validate required files
echo 🔍 Validating required files...

set REQUIRED_FILES=OctaneFlasher\OctaneFlasher.exe OctaneFlasher\octane_auth_firmware.bin OctaneFlasher\bootloader.bin OctaneFlasher\partitions.bin

set ALL_FILES_PRESENT=1
for %%F in (%REQUIRED_FILES%) do (
    if exist "%%F" (
        for %%S in ("%%F") do (
            echo    ✅ %%~nxF ^(%%~zS bytes^)
        )
    ) else (
        echo    ❌ %%F - MISSING
        set ALL_FILES_PRESENT=0
    )
)

if %ALL_FILES_PRESENT% EQU 0 (
    echo.
    echo ❌ Required files are missing. Please build firmware and flasher first.
    echo.
    echo 🔧 Build steps:
    echo    1. cd esp32-firmware
    echo    2. build.bat
    echo    3. cd ..\OctaneFlasher
    echo    4. dotnet publish -c Release
    echo.
    pause
    exit /b 1
)

echo ✅ All required files present
echo.

REM Copy core files
echo 📁 Copying core files...

copy "OctaneFlasher\OctaneFlasher.exe" "%DIST_DIR%\" >nul
copy "OctaneFlasher\octane_auth_firmware.bin" "%DIST_DIR%\" >nul
copy "OctaneFlasher\bootloader.bin" "%DIST_DIR%\" >nul
copy "OctaneFlasher\partitions.bin" "%DIST_DIR%\" >nul

REM Copy optional files if they exist
if exist "OctaneFlasher\esptool.exe" (
    copy "OctaneFlasher\esptool.exe" "%DIST_DIR%\" >nul
    echo    ✅ esptool.exe
)

echo    ✅ Core files copied
echo.

REM Create README.txt
echo 📝 Creating README.txt...

(
echo Octane ESP32-S2 HID Mouse Flasher v%VERSION%
echo =============================================
echo.
echo QUICK START:
echo 1. Put your ESP32-S2 in download mode:
echo    - Hold BOOT button
echo    - Plug in USB while holding BOOT
echo    - Release BOOT button
echo.
echo 2. Run OctaneFlasher.exe
echo 3. Enter your license key
echo 4. Follow the on-screen instructions
echo.
echo REQUIREMENTS:
echo - Windows 10/11
echo - ESP32-S2 board
echo - USB cable
echo - Valid Octane license key
echo.
echo FILES INCLUDED:
echo - OctaneFlasher.exe          ^(Main flasher application^)
echo - octane_auth_firmware.bin   ^(ESP32-S2 HID mouse firmware^)
echo - bootloader.bin             ^(ESP32-S2 bootloader^)
echo - partitions.bin             ^(Partition table^)
echo - esptool.exe                ^(ESP32 flashing tool - optional^)
echo.
echo TROUBLESHOOTING:
echo - If ESP32 not detected: Check download mode and USB connection
echo - If flashing fails: Try different USB port or cable
echo - If license fails: Check internet connection and key validity
echo.
echo SUPPORT:
echo For technical support, contact the Octane team.
echo.
echo Copyright ^(c^) 2024 Octane Team. All rights reserved.
) > "%DIST_DIR%\README.txt"

echo    ✅ README.txt created
echo.

REM Create CHANGELOG.txt
echo 📝 Creating CHANGELOG.txt...

(
echo Octane ESP32-S2 HID Mouse Flasher - Changelog
echo ==============================================
echo.
echo Version %VERSION% ^(%DATE%^)
echo ------------------------
echo - ESP32-S2 HID Mouse firmware v2.0
echo - Improved USB HID mouse emulation
echo - Enhanced serial command interface
echo - Command queue system for smooth movement
echo - LED status indicators ^(flashing/steady^)
echo - Error reporting via serial to desktop app
echo - Better esptool integration
echo - Enhanced user experience with progress indicators
echo - Comprehensive firmware validation
echo - Support for both ESP-IDF and PlatformIO builds
echo.
echo Features:
echo - USB HID Mouse device emulation
echo - Serial communication at 921600 baud
echo - Mouse movement commands ^(M10,5^)
echo - Click commands ^(CLICK_LEFT_DOWN/UP^)
echo - Status commands ^(PING/PONG, STATUS^)
echo - LED GPIO15 status indication
echo - Automatic COM port detection
echo - Pre/post-flash verification
echo - License key authentication
echo - Discord webhook error reporting
echo.
echo Technical Specifications:
echo - Target: ESP32-S2 Mini ^(4MB Flash, 2MB PSRAM^)
echo - Framework: ESP-IDF v4.4+
echo - USB: TinyUSB HID implementation
echo - Serial: 921600 baud, 8N1
echo - LED: GPIO15 status indicator
echo - Flash Layout: 0x1000 bootloader, 0x8000 partitions, 0x10000 firmware
echo.
) > "%DIST_DIR%\CHANGELOG.txt"

echo    ✅ CHANGELOG.txt created
echo.

REM Copy documentation if it exists
if exist "esp32-firmware\BUILD_SYSTEM_README.md" (
    copy "esp32-firmware\BUILD_SYSTEM_README.md" "%DIST_DIR%\docs\" >nul
    echo    ✅ Build system documentation copied
)

if exist "esp32-firmware\COMPILATION_INSTRUCTIONS.md" (
    copy "esp32-firmware\COMPILATION_INSTRUCTIONS.md" "%DIST_DIR%\docs\" >nul
    echo    ✅ Compilation instructions copied
)

if exist "DISTRIBUTION_README.md" (
    copy "DISTRIBUTION_README.md" "%DIST_DIR%\docs\" >nul
    echo    ✅ Distribution documentation copied
)

REM Calculate total size
echo.
echo 📊 Distribution Statistics:
echo ===========================

set TOTAL_SIZE=0
for /r "%DIST_DIR%" %%F in (*) do (
    set /a TOTAL_SIZE+=%%~zF
)

echo Total files: 
dir /s /b "%DIST_DIR%\*" | find /c /v ""

echo Total size: %TOTAL_SIZE% bytes

REM Convert to MB
set /a TOTAL_MB=%TOTAL_SIZE%/1048576
echo Total size: %TOTAL_MB% MB

echo.

REM Create ZIP archive if 7zip is available
where 7z >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 📦 Creating ZIP archive...
    7z a -tzip "dist\Octane-Flasher-v%VERSION%.zip" "%DIST_DIR%\*" >nul
    if %ERRORLEVEL% EQU 0 (
        echo    ✅ ZIP archive created: dist\Octane-Flasher-v%VERSION%.zip
    ) else (
        echo    ❌ Failed to create ZIP archive
    )
) else (
    echo ⚠️ 7-Zip not found - ZIP archive not created
    echo    Install 7-Zip to automatically create archives
)

echo.
echo 🎉 Distribution package created successfully!
echo.
echo 📁 Distribution location: %DIST_DIR%
echo 📦 Archive location: dist\Octane-Flasher-v%VERSION%.zip
echo.
echo 📋 Next steps:
echo    1. Test the distribution on a clean system
echo    2. Verify all files work correctly
echo    3. Upload to distribution channels
echo    4. Update download links
echo.

pause
