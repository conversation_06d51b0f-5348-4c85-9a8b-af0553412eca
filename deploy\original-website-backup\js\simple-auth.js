// Simple Authentication System for Octane Admin Panel
class SimpleAuth {
    constructor() {
        this.currentUser = null;
        this.checkAuthState();
    }

    checkAuthState() {
        const savedUser = localStorage.getItem('octane_admin_user');
        if (savedUser) {
            try {
                this.currentUser = JSON.parse(savedUser);
                this.showAdminPanel();
            } catch (e) {
                this.logout();
            }
        } else {
            this.showLoginForm();
        }
    }

    async login(email, password) {
        // Simple hardcoded authentication for admin panel
        const validCredentials = [
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'admin123' },
            { email: 'admin', password: 'admin123' },
            { email: '<EMAIL>', password: 'admin123' }
        ];

        const user = validCredentials.find(u => u.email === email && u.password === password);
        
        if (user) {
            this.currentUser = {
                email: user.email,
                uid: 'admin-' + Date.now(),
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('octane_admin_user', JSON.stringify(this.currentUser));
            this.showAdminPanel();
            return true;
        } else {
            throw new Error('Invalid credentials');
        }
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('octane_admin_user');
        this.showLoginForm();
    }

    showLoginForm() {
        const loginHTML = `
            <div class="login-container">
                <div class="login-form">
                    <div class="login-header">
                        <h2>⚡ OCTANE</h2>
                        <p>Professional License Management System</p>
                    </div>
                    <form id="loginForm">
                        <div class="form-group">
                            <label for="email">Email:</label>
                            <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="password">Password:</label>
                            <input type="password" id="password" name="password" required placeholder="Enter password">
                        </div>
                        <button type="submit" class="login-btn">🚀 Login to Admin Panel</button>
                        <div id="loginError" class="error-message" style="display: none;"></div>
                    </form>
                    <div class="login-footer">
                        <p>Secure Admin Access</p>
                    </div>
                </div>
            </div>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                }
                
                .login-container {
                    width: 100%;
                    max-width: 400px;
                    padding: 20px;
                }
                
                .login-form {
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 20px;
                    padding: 40px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
                    backdrop-filter: blur(10px);
                }
                
                .login-header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                
                .login-header h2 {
                    color: #667eea;
                    font-size: 32px;
                    margin: 0 0 10px 0;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                
                .login-header p {
                    color: #666;
                    margin: 0;
                    font-size: 14px;
                }
                
                .form-group {
                    margin-bottom: 20px;
                }
                
                .form-group label {
                    display: block;
                    margin-bottom: 8px;
                    font-weight: bold;
                    color: #333;
                }
                
                .form-group input {
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #e0e0e0;
                    border-radius: 8px;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    box-sizing: border-box;
                }
                
                .form-group input:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
                
                .login-btn {
                    width: 100%;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 15px;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .login-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                }
                
                .error-message {
                    background: #ffebee;
                    color: #c62828;
                    padding: 10px;
                    border-radius: 6px;
                    margin-top: 15px;
                    text-align: center;
                    border: 1px solid #ffcdd2;
                }
                
                .login-footer {
                    text-align: center;
                    margin-top: 30px;
                    color: #666;
                    font-size: 12px;
                }
            </style>
        `;

        document.body.innerHTML = loginHTML;

        // Add login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('loginError');

            try {
                await this.login(email, password);
            } catch (error) {
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';
            }
        });
    }

    showAdminPanel() {
        // Reload the page to show the admin panel
        if (window.location.pathname === '/admin' || window.location.pathname === '/') {
            window.location.reload();
        } else {
            window.location.href = '/admin';
        }
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    getCurrentUser() {
        return this.currentUser;
    }
}

// Initialize authentication
const auth = new SimpleAuth();

// Export for use in other scripts
window.octaneAuth = auth;

// Auto-logout after 24 hours
if (auth.currentUser) {
    const loginTime = new Date(auth.currentUser.loginTime);
    const now = new Date();
    const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
    
    if (hoursDiff > 24) {
        auth.logout();
    }
}

console.log('✅ Simple authentication system loaded');
console.log('User signed in:', auth.currentUser?.email || 'Not signed in');
