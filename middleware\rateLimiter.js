const rateLimit = require('express-rate-limit');

// Rate limiter for authentication endpoints
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window per IP
    message: {
        success: false,
        message: 'Too many authentication attempts. Please try again in 15 minutes.',
        error: 'RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Skip successful requests
    skipSuccessfulRequests: true,
    // Custom key generator to include user agent
    keyGenerator: (req) => {
        return req.ip + ':' + (req.get('User-Agent') || 'unknown');
    }
});

// Rate limiter for admin endpoints
const adminLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 20, // 20 requests per window
    message: {
        success: false,
        message: 'Too many admin requests. Please slow down.',
        error: 'ADMIN_RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false
});

// Rate limiter for general API endpoints
const apiLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 30, // 30 requests per minute
    message: {
        success: false,
        message: 'Too many requests. Please slow down.',
        error: 'API_RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false
});

// Strict rate limiter for security events
const securityLimiter = rateLimit({
    windowMs: 10 * 60 * 1000, // 10 minutes
    max: 10, // 10 requests per window
    message: {
        success: false,
        message: 'Security endpoint rate limited.',
        error: 'SECURITY_RATE_LIMITED'
    },
    standardHeaders: true,
    legacyHeaders: false
});

module.exports = {
    authLimiter,
    adminLimiter,
    apiLimiter,
    securityLimiter
};
