const express = require('express');
const router = express.Router();

// Helper functions
function isValidLicense<PERSON>ey(key) {
    return /^[A-Z0-9]{3}-[A-Z0-9]{3}-[A-Z0-9]{3}$/.test(key);
}

function isValidHardwareId(hwid) {
    return hwid && typeof hwid === 'string' && hwid.length >= 10 && hwid.length <= 100;
}

function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const segments = [];
    
    for (let i = 0; i < 3; i++) {
        let segment = '';
        for (let j = 0; j < 3; j++) {
            segment += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        segments.push(segment);
    }
    
    return segments.join('-');
}

function calculateExpiryDate(duration) {
    const now = new Date();
    
    switch (duration) {
        case '1hour':
            return new Date(now.getTime() + 60 * 60 * 1000);
        case '1day':
            return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        case '1week':
            return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        case '1month':
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        case '3months':
            return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        case '6months':
            return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
        case '1year':
            return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        case 'lifetime':
            return null;
        default:
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
}

// License routes
router.get('/count', (req, res) => {
    const db = req.app.locals.db;
    db.get(
        `SELECT COUNT(*) as count FROM licenses`,
        [],
        (err, row) => {
            if (err) {
                console.error('License count error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Database error'
                });
            }
            res.json({
                success: true,
                count: row.count
            });
        }
    );
});

// Admin license management routes
router.get('/admin/licenses', (req, res) => {
    const db = req.app.locals.db;
    db.all(
        `SELECT key, duration, is_active, expires_at, hardware_id, notes, created_at 
         FROM licenses ORDER BY created_at DESC`,
        [],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Database error'
                });
            }
            res.json({
                success: true,
                licenses: rows
            });
        }
    );
});

router.post('/admin/licenses', (req, res) => {
    const { duration, notes } = req.body;
    const db = req.app.locals.db;
    
    if (!duration) {
        return res.status(400).json({
            success: false,
            error: 'Duration is required'
        });
    }

    const licenseKey = generateLicenseKey();
    const expiryDate = calculateExpiryDate(duration);
    
    db.run(
        `INSERT INTO licenses (key, duration, is_active, expires_at, notes, created_at) 
         VALUES (?, ?, 1, ?, ?, datetime('now'))`,
        [licenseKey, duration, expiryDate ? expiryDate.toISOString() : null, notes || ''],
        function(err) {
            if (err) {
                console.error('Create license error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to create license'
                });
            }
            
            console.log(`✅ License created: ${licenseKey} (${duration})`);
            res.json({
                success: true,
                message: 'License created successfully',
                license: {
                    key: licenseKey,
                    duration: duration,
                    expires_at: expiryDate ? expiryDate.toISOString() : null,
                    notes: notes || ''
                }
            });
        }
    );
});

router.delete('/admin/licenses/:key', (req, res) => {
    const { key } = req.params;
    const db = req.app.locals.db;
    
    if (!isValidLicenseKey(key)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid license key format'
        });
    }

    db.run(
        `DELETE FROM licenses WHERE key = ?`,
        [key],
        function(err) {
            if (err) {
                console.error('Delete license error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete license'
                });
            }
            
            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'License not found'
                });
            }
            
            console.log(`🗑️ License deleted: ${key}`);
            res.json({
                success: true,
                message: 'License deleted successfully'
            });
        }
    );
});

router.post('/admin/licenses/:key/reset-hwid', (req, res) => {
    const { key } = req.params;
    const db = req.app.locals.db;
    
    if (!isValidLicenseKey(key)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid license key format'
        });
    }

    db.run(
        `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
        [key],
        function(err) {
            if (err) {
                console.error('Reset HWID error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to reset HWID'
                });
            }
            
            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'License not found'
                });
            }
            
            console.log(`🔄 HWID reset for license: ${key}`);
            res.json({
                success: true,
                message: 'HWID reset successfully'
            });
        }
    );
});

router.delete('/admin/licenses/cleanup-expired', (req, res) => {
    const db = req.app.locals.db;
    
    db.run(
        `DELETE FROM licenses WHERE expires_at IS NOT NULL AND expires_at < datetime('now')`,
        [],
        function(err) {
            if (err) {
                console.error('Cleanup expired licenses error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to cleanup expired licenses'
                });
            }
            
            console.log(`🧹 Cleaned up ${this.changes} expired licenses`);
            res.json({
                success: true,
                message: `Cleaned up ${this.changes} expired licenses`,
                deleted: this.changes
            });
        }
    );
});

module.exports = router;
