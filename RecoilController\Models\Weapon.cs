using System;
using System.Collections.Generic;

namespace RecoilController.Models
{
    /// <summary>
    /// Represents a weapon with its recoil pattern and properties
    /// </summary>
    public class Weapon
    {
        public string Name { get; set; }
        public int Id { get; set; }
        public uint Delay { get; set; } // Delay between shots in milliseconds
        public uint MaxBulletCount { get; set; }
        public float HipfireScale { get; set; }
        public bool IsAutomatic { get; set; }
        public List<Vector2> RecoilPattern { get; set; }

        public Weapon()
        {
            RecoilPattern = new List<Vector2>();
        }

        public Weapon(string name, int id, uint delay, uint maxBulletCount, float hipfireScale, bool isAutomatic, List<Vector2> recoilPattern)
        {
            Name = name;
            Id = id;
            Delay = delay;
            MaxBulletCount = maxBulletCount;
            HipfireScale = hipfireScale;
            IsAutomatic = isAutomatic;
            RecoilPattern = recoilPattern ?? new List<Vector2>();
        }

        /// <summary>
        /// Converts raw recoil data to pixel movement based on sensitivity and FOV
        /// </summary>
        public Vector2 ToPixelMovement(Vector2 rawRecoil, float sensitivity, float adsSensitivity, float fieldOfView)
        {
            // Based on the C++ template conversion formula
            float conversionFactor = -0.03f * (sensitivity * adsSensitivity * 3.0f) * (fieldOfView / 100.0f);
            
            return new Vector2(
                (float)Math.Round(rawRecoil.X / conversionFactor),
                (float)Math.Round(rawRecoil.Y / conversionFactor)
            );
        }

        /// <summary>
        /// Gets the recoil movement for a specific bullet index
        /// </summary>
        public Vector2 GetRecoilMovement(int bulletIndex, float sensitivity, float adsSensitivity, float fieldOfView, bool isCrouching = false)
        {
            if (bulletIndex < 0 || bulletIndex >= RecoilPattern.Count)
                return Vector2.Zero;

            var rawRecoil = RecoilPattern[bulletIndex];
            var pixelMovement = ToPixelMovement(rawRecoil, sensitivity, adsSensitivity, fieldOfView);

            // Apply crouch modifier (reduces recoil by 50%)
            if (isCrouching)
                pixelMovement *= 0.5f;

            return pixelMovement;
        }

        public override string ToString()
        {
            return $"{Name} (ID: {Id}, Bullets: {MaxBulletCount}, Delay: {Delay}ms)";
        }
    }
}
