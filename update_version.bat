@echo off
setlocal enabledelayedexpansion

echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🔄 OCTANE VERSION UPDATER 🔄                   ║
echo ║                      by Octane Team                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Get new version from command line or prompt
set NEW_VERSION=%1
if "%NEW_VERSION%"=="" (
    echo 📋 Current version information:
    echo.
    
    REM Try to extract current version from firmware
    if exist "esp32-firmware\main\main.c" (
        for /f "tokens=3 delims= " %%a in ('findstr "FIRMWARE_VERSION_STRING" esp32-firmware\main\main.c') do (
            set CURRENT_FW_VERSION=%%a
            set CURRENT_FW_VERSION=!CURRENT_FW_VERSION:"=!
        )
        echo    Firmware: !CURRENT_FW_VERSION!
    )
    
    REM Try to extract current version from flasher
    if exist "OctaneFlasher\Program.cs" (
        for /f "tokens=5 delims= " %%a in ('findstr "FLASHER_VERSION" OctaneFlasher\Program.cs') do (
            set CURRENT_FLASHER_VERSION=%%a
            set CURRENT_FLASHER_VERSION=!CURRENT_FLASHER_VERSION:"=!
            set CURRENT_FLASHER_VERSION=!CURRENT_FLASHER_VERSION:;=!
        )
        echo    Flasher: !CURRENT_FLASHER_VERSION!
    )
    
    echo.
    set /p NEW_VERSION="Enter new version (e.g., 2.1.0): "
)

if "%NEW_VERSION%"=="" (
    echo ❌ Version is required
    pause
    exit /b 1
)

REM Validate version format (basic check)
echo %NEW_VERSION% | findstr /r "^[0-9]\+\.[0-9]\+\.[0-9]\+$" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Invalid version format. Use MAJOR.MINOR.PATCH (e.g., 2.1.0)
    pause
    exit /b 1
)

echo 🎯 Updating to version %NEW_VERSION%
echo.

REM Parse version components
for /f "tokens=1,2,3 delims=." %%a in ("%NEW_VERSION%") do (
    set VERSION_MAJOR=%%a
    set VERSION_MINOR=%%b
    set VERSION_PATCH=%%c
)

echo 📝 Updating firmware version...

REM Update firmware version in main.c
if exist "esp32-firmware\main\main.c" (
    powershell -Command "(Get-Content 'esp32-firmware\main\main.c') -replace '#define FIRMWARE_VERSION_MAJOR [0-9]+', '#define FIRMWARE_VERSION_MAJOR %VERSION_MAJOR%' | Set-Content 'esp32-firmware\main\main.c'"
    powershell -Command "(Get-Content 'esp32-firmware\main\main.c') -replace '#define FIRMWARE_VERSION_MINOR [0-9]+', '#define FIRMWARE_VERSION_MINOR %VERSION_MINOR%' | Set-Content 'esp32-firmware\main\main.c'"
    powershell -Command "(Get-Content 'esp32-firmware\main\main.c') -replace '#define FIRMWARE_VERSION_PATCH [0-9]+', '#define FIRMWARE_VERSION_PATCH %VERSION_PATCH%' | Set-Content 'esp32-firmware\main\main.c'"
    powershell -Command "(Get-Content 'esp32-firmware\main\main.c') -replace '#define FIRMWARE_VERSION_STRING \"[^\"]*\"', '#define FIRMWARE_VERSION_STRING \"%NEW_VERSION%\"' | Set-Content 'esp32-firmware\main\main.c'"
    echo    ✅ Firmware version updated
) else (
    echo    ❌ Firmware main.c not found
)

echo 📝 Updating flasher version...

REM Update flasher version in Program.cs
if exist "OctaneFlasher\Program.cs" (
    powershell -Command "(Get-Content 'OctaneFlasher\Program.cs') -replace 'FLASHER_VERSION = \"[^\"]*\"', 'FLASHER_VERSION = \"%NEW_VERSION%\"' | Set-Content 'OctaneFlasher\Program.cs'"
    powershell -Command "(Get-Content 'OctaneFlasher\Program.cs') -replace 'EXPECTED_FIRMWARE_VERSION = \"[^\"]*\"', 'EXPECTED_FIRMWARE_VERSION = \"%NEW_VERSION%\"' | Set-Content 'OctaneFlasher\Program.cs'"
    powershell -Command "(Get-Content 'OctaneFlasher\Program.cs') -replace 'FIRMWARE_VERSION_MAJOR = [0-9]+', 'FIRMWARE_VERSION_MAJOR = %VERSION_MAJOR%' | Set-Content 'OctaneFlasher\Program.cs'"
    powershell -Command "(Get-Content 'OctaneFlasher\Program.cs') -replace 'FIRMWARE_VERSION_MINOR = [0-9]+', 'FIRMWARE_VERSION_MINOR = %VERSION_MINOR%' | Set-Content 'OctaneFlasher\Program.cs'"
    powershell -Command "(Get-Content 'OctaneFlasher\Program.cs') -replace 'FIRMWARE_VERSION_PATCH = [0-9]+', 'FIRMWARE_VERSION_PATCH = %VERSION_PATCH%' | Set-Content 'OctaneFlasher\Program.cs'"
    echo    ✅ Flasher version updated
) else (
    echo    ❌ Flasher Program.cs not found
)

echo 📝 Updating project files...

REM Update C# project version if it exists
if exist "OctaneFlasher\OctaneFlasher.csproj" (
    powershell -Command "(Get-Content 'OctaneFlasher\OctaneFlasher.csproj') -replace '<Version>[^<]*</Version>', '<Version>%NEW_VERSION%</Version>' | Set-Content 'OctaneFlasher\OctaneFlasher.csproj'"
    powershell -Command "(Get-Content 'OctaneFlasher\OctaneFlasher.csproj') -replace '<AssemblyVersion>[^<]*</AssemblyVersion>', '<AssemblyVersion>%NEW_VERSION%</AssemblyVersion>' | Set-Content 'OctaneFlasher\OctaneFlasher.csproj'"
    powershell -Command "(Get-Content 'OctaneFlasher\OctaneFlasher.csproj') -replace '<FileVersion>[^<]*</FileVersion>', '<FileVersion>%NEW_VERSION%</FileVersion>' | Set-Content 'OctaneFlasher\OctaneFlasher.csproj'"
    echo    ✅ C# project version updated
)

REM Update PlatformIO version if it exists
if exist "esp32-firmware\platformio.ini" (
    powershell -Command "(Get-Content 'esp32-firmware\platformio.ini') -replace 'FIRMWARE_VERSION.*\"[^\"]*\"', 'FIRMWARE_VERSION \\\"%NEW_VERSION%\\\"' | Set-Content 'esp32-firmware\platformio.ini'"
    echo    ✅ PlatformIO version updated
)

echo.
echo 📊 Version Update Summary:
echo =========================
echo    New version: %NEW_VERSION%
echo    Major: %VERSION_MAJOR%
echo    Minor: %VERSION_MINOR%
echo    Patch: %VERSION_PATCH%
echo.

echo 🔍 Verifying updates...

REM Verify firmware update
findstr "FIRMWARE_VERSION_STRING \"%NEW_VERSION%\"" esp32-firmware\main\main.c >nul
if %ERRORLEVEL% EQU 0 (
    echo    ✅ Firmware version verified
) else (
    echo    ❌ Firmware version verification failed
)

REM Verify flasher update
findstr "FLASHER_VERSION = \"%NEW_VERSION%\"" OctaneFlasher\Program.cs >nul
if %ERRORLEVEL% EQU 0 (
    echo    ✅ Flasher version verified
) else (
    echo    ❌ Flasher version verification failed
)

echo.
echo 🎉 Version update complete!
echo.
echo 📋 Next steps:
echo    1. Test firmware compilation: cd esp32-firmware ^&^& build.bat
echo    2. Test flasher compilation: cd OctaneFlasher ^&^& dotnet build
echo    3. Test version compatibility
echo    4. Update documentation if needed
echo    5. Create git tag: git tag -a v%NEW_VERSION% -m "Release v%NEW_VERSION%"
echo    6. Build distribution: create_distribution.bat %NEW_VERSION%
echo.

REM Ask if user wants to build now
set /p BUILD_NOW="Build firmware and flasher now? (Y/N): "
if /i "%BUILD_NOW%"=="Y" (
    echo.
    echo 🔨 Building firmware...
    cd esp32-firmware
    call build.bat
    cd ..
    
    echo.
    echo 🔨 Building flasher...
    cd OctaneFlasher
    dotnet build -c Release
    cd ..
    
    echo.
    echo ✅ Build complete!
)

echo.
pause
