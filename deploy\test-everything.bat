@echo off
echo.
echo ⚡ OCTANE AUTHENTICATION SYSTEM - COMPREHENSIVE TESTS
echo ===================================================
echo.

set VPS_IP=*************
set SSH_KEY=%~dp0octane_key

echo 🔍 STEP 1: Server Health Check
echo ==============================
echo Testing API health endpoint...
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -s http://localhost:3000/api/health"
echo.

echo 🔍 STEP 2: Discord Bot Status Check
echo ==================================
echo Waiting for Discord bot to connect...
timeout /t 5 /nobreak > nul
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && sudo -u octane pm2 logs octane-auth --lines 10 | grep -E 'Discord|authentication|logged in'"
echo.

echo 🔍 STEP 3: License Creation Test
echo ===============================
echo Creating test license via API...
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -X POST http://localhost:3000/api/admin/licenses -H 'Content-Type: application/json' -H 'Authorization: Bearer firebase-auth' -d '{\"duration\":\"1hour\",\"notes\":\"Automated test license\"}'"
echo.

echo 🔍 STEP 4: License List & Extraction
echo ===================================
echo Fetching all licenses and extracting test license key...
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -s http://localhost:3000/api/admin/licenses -H 'Authorization: Bearer firebase-auth'" > temp_licenses.json
echo License list retrieved.

echo 🔍 STEP 5: Parse License Key
echo ===========================
echo Extracting the test license key...
for /f "tokens=*" %%i in ('powershell -command "& {$json = Get-Content temp_licenses.json | ConvertFrom-Json; $testLicense = $json.licenses | Where-Object {$_.notes -eq 'Automated test license'} | Select-Object -First 1; if($testLicense) {$testLicense.key} else {'NOT_FOUND'}}"') do set TEST_LICENSE_KEY=%%i
echo Test License Key: %TEST_LICENSE_KEY%

echo 🔍 STEP 6: License Validation Test
echo =================================
if "%TEST_LICENSE_KEY%" NEQ "NOT_FOUND" (
    echo Testing license validation...
    ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -X POST http://localhost:3000/api/validate -H 'Content-Type: application/json' -d '{\"license_key\":\"%TEST_LICENSE_KEY%\",\"hardware_id\":\"TEST-HWID-12345\"}'"
    echo.
) else (
    echo ❌ No test license found to validate
)

echo 🔍 STEP 7: Discord Integration Test
echo ==================================
echo Testing Discord bot functionality...
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -X POST http://localhost:3000/api/admin/test-discord -H 'Content-Type: application/json' -H 'Authorization: Bearer firebase-auth'"
echo.

echo 🔍 STEP 8: License Cleanup
echo =========================
if "%TEST_LICENSE_KEY%" NEQ "NOT_FOUND" (
    echo Deleting test license: %TEST_LICENSE_KEY%
    ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && node -e 'const db=require(\"./models/database\"); db.initialize().then(()=>db.deleteLicense(\"%TEST_LICENSE_KEY%\")).then(()=>console.log(\"✅ Test license deleted\")).catch(e=>console.log(\"❌ Delete failed:\",e.message))'"
    echo.
) else (
    echo ⚠️ No test license to clean up
)

echo 🔍 STEP 9: Final Status Check
echo ============================
echo Checking final server status...
ssh -i "%SSH_KEY%" root@%VPS_IP% "sudo -u octane pm2 status"
echo.

echo 🔍 STEP 10: Discord Commands Check
echo =================================
echo Checking if Discord slash commands are registered...
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && sudo -u octane pm2 logs octane-auth --lines 20 | grep -E 'Registered|commands|slash'"
echo.

del temp_licenses.json 2>nul

echo.
echo ✅ COMPREHENSIVE TESTING COMPLETE!
echo =================================
echo.
echo 📊 RESULTS SUMMARY:
echo - Server Health: Check output above
echo - Discord Bot: Check connection status above
echo - License Creation: Check if test license was created
echo - License Validation: Check if validation worked
echo - License Cleanup: Check if test license was deleted
echo.
echo 🌐 Admin Panel: http://%VPS_IP%/admin
echo 📧 Login: <EMAIL> / TestPassword123!
echo.
echo 🎮 Discord Commands (if bot is online):
echo   /create-license - Create new license
echo   /reset-hwid - Reset hardware ID
echo   /list-licenses - View all licenses
echo   /check-license - Check specific license
echo.
pause
