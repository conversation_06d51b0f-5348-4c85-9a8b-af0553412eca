<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reminders - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-bell"></i> System Reminders</h1>
                <p>Manage maintenance tasks and system reminders</p>
            </div>

            <div class="dashboard-grid">
                <!-- Create Reminder -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus"></i> Create Reminder</h3>
                    </div>
                    <div class="card-content">
                        <form id="reminder-form">
                            <div class="form-group">
                                <label for="reminder-title">Title:</label>
                                <input type="text" id="reminder-title" required placeholder="Enter reminder title">
                            </div>
                            <div class="form-group">
                                <label for="reminder-description">Description:</label>
                                <textarea id="reminder-description" rows="3" placeholder="Enter reminder description"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="reminder-type">Type:</label>
                                <select id="reminder-type" required>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="security">Security Check</option>
                                    <option value="backup">Backup</option>
                                    <option value="update">System Update</option>
                                    <option value="license">License Review</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="reminder-priority">Priority:</label>
                                <select id="reminder-priority" required>
                                    <option value="low">Low</option>
                                    <option value="medium" selected>Medium</option>
                                    <option value="high">High</option>
                                    <option value="critical">Critical</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="reminder-date">Due Date:</label>
                                <input type="datetime-local" id="reminder-date" required>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-bell"></i> Create Reminder
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Upcoming Reminders -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> Upcoming</h3>
                    </div>
                    <div class="card-content">
                        <div id="upcoming-reminders" class="reminders-list">
                            <div class="reminder-item high">
                                <div class="reminder-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="reminder-content">
                                    <div class="reminder-title">Security Audit</div>
                                    <div class="reminder-time">Due in 2 days</div>
                                </div>
                            </div>
                            <div class="reminder-item medium">
                                <div class="reminder-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="reminder-content">
                                    <div class="reminder-title">Database Backup</div>
                                    <div class="reminder-time">Due in 5 days</div>
                                </div>
                            </div>
                            <div class="reminder-item low">
                                <div class="reminder-icon">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div class="reminder-content">
                                    <div class="reminder-title">License Review</div>
                                    <div class="reminder-time">Due in 1 week</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Overdue Reminders -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-exclamation-triangle"></i> Overdue</h3>
                    </div>
                    <div class="card-content">
                        <div id="overdue-reminders" class="reminders-list">
                            <div class="reminder-item critical overdue">
                                <div class="reminder-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="reminder-content">
                                    <div class="reminder-title">Server Maintenance</div>
                                    <div class="reminder-time">Overdue by 1 day</div>
                                </div>
                                <div class="reminder-actions">
                                    <button class="btn btn-sm btn-success" onclick="completeReminder(1)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- All Reminders -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-list"></i> All Reminders</h3>
                        <div class="card-actions">
                            <select id="filter-status">
                                <option value="">All Status</option>
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="overdue">Overdue</option>
                            </select>
                            <select id="filter-type">
                                <option value="">All Types</option>
                                <option value="maintenance">Maintenance</option>
                                <option value="security">Security</option>
                                <option value="backup">Backup</option>
                                <option value="update">Update</option>
                                <option value="license">License</option>
                            </select>
                            <button id="refresh-reminders" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="table-container">
                            <table id="reminders-table">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Priority</th>
                                        <th>Due Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="reminders-tbody">
                                    <tr>
                                        <td>Security Audit</td>
                                        <td><span class="type-badge security">Security</span></td>
                                        <td><span class="priority-badge high">High</span></td>
                                        <td>2025-07-28 09:00</td>
                                        <td><span class="status-badge pending">Pending</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="completeReminder(2)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="editReminder(2)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteReminder(2)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Database Backup</td>
                                        <td><span class="type-badge backup">Backup</span></td>
                                        <td><span class="priority-badge medium">Medium</span></td>
                                        <td>2025-07-31 02:00</td>
                                        <td><span class="status-badge pending">Pending</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="completeReminder(3)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="editReminder(3)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteReminder(3)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Server Maintenance</td>
                                        <td><span class="type-badge maintenance">Maintenance</span></td>
                                        <td><span class="priority-badge critical">Critical</span></td>
                                        <td>2025-07-25 01:00</td>
                                        <td><span class="status-badge overdue">Overdue</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="completeReminder(1)">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" onclick="editReminder(1)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="deleteReminder(1)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Schedule -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-alt"></i> Maintenance Schedule</h3>
                    </div>
                    <div class="card-content">
                        <div class="schedule-grid">
                            <div class="schedule-item">
                                <div class="schedule-day">Daily</div>
                                <div class="schedule-tasks">
                                    <div class="task-item">Log rotation</div>
                                    <div class="task-item">Health checks</div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-day">Weekly</div>
                                <div class="schedule-tasks">
                                    <div class="task-item">Database backup</div>
                                    <div class="task-item">Security scan</div>
                                    <div class="task-item">Performance review</div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-day">Monthly</div>
                                <div class="schedule-tasks">
                                    <div class="task-item">License audit</div>
                                    <div class="task-item">System updates</div>
                                    <div class="task-item">Capacity planning</div>
                                </div>
                            </div>
                            <div class="schedule-item">
                                <div class="schedule-day">Quarterly</div>
                                <div class="schedule-tasks">
                                    <div class="task-item">Security audit</div>
                                    <div class="task-item">Disaster recovery test</div>
                                    <div class="task-item">Infrastructure review</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/admin-modern.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default date to tomorrow
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('reminder-date').value = tomorrow.toISOString().slice(0, 16);
            
            // Form submission
            document.getElementById('reminder-form').addEventListener('submit', createReminder);
            
            // Filter controls
            document.getElementById('filter-status').addEventListener('change', filterReminders);
            document.getElementById('filter-type').addEventListener('change', filterReminders);
            document.getElementById('refresh-reminders').addEventListener('click', loadReminders);
            
            loadReminders();
        });

        async function createReminder(event) {
            event.preventDefault();
            
            const reminder = {
                title: document.getElementById('reminder-title').value,
                description: document.getElementById('reminder-description').value,
                type: document.getElementById('reminder-type').value,
                priority: document.getElementById('reminder-priority').value,
                dueDate: document.getElementById('reminder-date').value
            };
            
            try {
                const response = await fetch('/api/reminders', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(reminder)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Reminder created successfully', 'success');
                    document.getElementById('reminder-form').reset();
                    loadReminders();
                } else {
                    showNotification('Failed to create reminder: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error creating reminder: ' + error.message, 'error');
            }
        }

        async function loadReminders() {
            // This would load reminders from the API
            // For now, we'll use the static data already in the HTML
            showNotification('Reminders refreshed', 'success');
        }

        function filterReminders() {
            const statusFilter = document.getElementById('filter-status').value;
            const typeFilter = document.getElementById('filter-type').value;
            
            // Implementation for filtering reminders
            showNotification('Filters applied', 'info');
        }

        async function completeReminder(id) {
            try {
                const response = await fetch(`/api/reminders/${id}/complete`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Reminder marked as completed', 'success');
                    loadReminders();
                } else {
                    showNotification('Failed to complete reminder: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error completing reminder: ' + error.message, 'error');
            }
        }

        function editReminder(id) {
            showNotification(`Editing reminder ${id}`, 'info');
            // Implementation for editing reminders
        }

        async function deleteReminder(id) {
            if (!confirm('Are you sure you want to delete this reminder?')) return;
            
            try {
                const response = await fetch(`/api/reminders/${id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Reminder deleted successfully', 'success');
                    loadReminders();
                } else {
                    showNotification('Failed to delete reminder: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Error deleting reminder: ' + error.message, 'error');
            }
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 600;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#00ff88' : type === 'error' ? '#ff4757' : '#4a90e2'};
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>
</body>
</html>
