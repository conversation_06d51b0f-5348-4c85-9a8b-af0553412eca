using System;
using System.Threading.Tasks;
using System.Windows;
using RecoilController.Services;
using RecoilController.ViewModels;
using RecoilController.Security;
using RecoilController.Models;
using RecoilController.Views;

namespace RecoilController
{
    public partial class App : Application
    {
        private ConfigurationService _configService;
        private DeviceService _deviceService;
        private AuthenticationService _authService;
        private EnhancedSecurityManager? _securityManager;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // Initialize security system FIRST (only in release mode)
#if !DEBUG
                await AdvancedSecurityManager.InitializeAsync();
#endif

                base.OnStartup(e);

                // Set up global exception handling
                AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
                DispatcherUnhandledException += OnDispatcherUnhandledException;

                // Set application icon (⚡ lightning bolt for Octane)
                // This sets the taskbar icon for all windows
                Current.MainWindow = null; // Prevent automatic main window creation

                // Initialize services
                _configService = new ConfigurationService();
                _deviceService = new DeviceService();
                _authService = new AuthenticationService();

                // Initialize enhanced security manager only in release mode
#if !DEBUG
                _securityManager = new EnhancedSecurityManager();
                _securityManager.SecurityAlertTriggered += OnSecurityAlertTriggered;
#endif

                // Show main window directly (skip login for now)
                ShowMainWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to start application: {ex.Message}", "Startup Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                Environment.Exit(1);
            }
        }



        private void ShowMainWindow()
        {
            try
            {
                // Use modern HTML-based main window
                var mainWindow = new Views.ModernMainWindow();

                // Set the new main window before closing the old one
                var oldWindow = MainWindow;
                MainWindow = mainWindow;

                // Show the new window first
                mainWindow.Show();

                // Then close the old window if it exists
                if (oldWindow != null && oldWindow != mainWindow)
                {
                    oldWindow.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to show main window: {ex.Message}", "Window Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                // Cleanup security manager
                _securityManager?.Dispose();

                // Disable security system only in release mode
#if !DEBUG
                await AdvancedSecurityManager.DisableAsync();
#endif

                // Clean up resources
                _deviceService?.Dispose();
            }
            catch
            {
                // Ignore errors during shutdown
            }

            base.OnExit(e);
        }

        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var exception = e.ExceptionObject as Exception;
            MessageBox.Show($"Unhandled exception: {exception?.Message}", "Critical Error",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            MessageBox.Show($"UI exception: {e.Exception.Message}", "UI Error",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }

        private void OnSecurityAlertTriggered(object? sender, SecurityAlert alert)
        {
            // Log security alert
            System.Diagnostics.Debug.WriteLine($"Security Alert: {alert.AlertType} - {alert.Description}");

            // Log security alert silently (no popup to avoid alerting user)
            // Security threats are logged to Discord and admin panel only
        }


    }
}
