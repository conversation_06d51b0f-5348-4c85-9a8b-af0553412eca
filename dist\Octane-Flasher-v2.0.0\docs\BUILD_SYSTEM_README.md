# ESP32-S2 HID Mouse Firmware Build System

This directory contains the complete build system for the Octane ESP32-S2 HID Mouse firmware with support for both ESP-IDF and PlatformIO build systems.

## 🚀 Quick Start

### Option 1: ESP-IDF (Recommended)
```bash
# Run the automated build script
build_firmware.bat

# Or manually:
idf.py set-target esp32s2
idf.py build
```

### Option 2: PlatformIO (Alternative)
```bash
# Run the PlatformIO build script
build_platformio.bat

# Or manually:
pio run -e esp32-s2-saola-1
```

## 📁 Project Structure

```
esp32-firmware/
├── main/                          # Main source code
│   ├── main.c                     # HID mouse firmware
│   └── CMakeLists.txt            # ESP-IDF component config
├── scripts/                       # Build automation scripts
│   ├── pre_build.py              # Pre-build validation
│   └── post_build.py             # Post-build file copying
├── CMakeLists.txt                # ESP-IDF project config
├── platformio.ini                # PlatformIO configuration
├── sdkconfig.defaults            # ESP-IDF default config
├── build_firmware.bat            # ESP-IDF build script
├── build_platformio.bat          # PlatformIO build script
└── COMPILATION_INSTRUCTIONS.md   # Detailed instructions
```

## 🔧 Build Systems Comparison

| Feature | ESP-IDF | PlatformIO |
|---------|---------|------------|
| **Setup Complexity** | Medium | Easy |
| **IDE Integration** | VS Code + Extension | Built-in IDE |
| **Build Speed** | Fast | Medium |
| **Debugging** | Excellent | Good |
| **Library Management** | Manual | Automatic |
| **Recommended For** | Production | Development |

## 🛠️ ESP-IDF Build System

### Prerequisites
1. Install ESP-IDF v4.4 or later
2. Run ESP-IDF environment setup

### Configuration
- **Target**: ESP32-S2
- **Flash Size**: 4MB
- **Flash Mode**: DIO
- **Flash Frequency**: 40MHz
- **PSRAM**: Enabled (2MB)

### Build Commands
```bash
# Set target (first time only)
idf.py set-target esp32s2

# Configure (optional)
idf.py menuconfig

# Build
idf.py build

# Flash
idf.py -p COM10 flash

# Monitor
idf.py -p COM10 monitor
```

## 🎯 PlatformIO Build System

### Prerequisites
1. Install Python 3.7+
2. Install PlatformIO: `pip install platformio`

### Available Environments
- `esp32-s2-saola-1`: Standard build
- `esp32-s2-debug`: Debug build with verbose logging
- `esp32-s2-release`: Optimized release build
- `esp32-s2-mini`: For Lolin S2 Mini boards
- `esp32-s2-kaluga`: For ESP32-S2-Kaluga-1 boards

### Build Commands
```bash
# Build for specific environment
pio run -e esp32-s2-saola-1

# Upload to device
pio run -e esp32-s2-saola-1 -t upload

# Monitor serial output
pio device monitor -e esp32-s2-saola-1

# Clean build
pio run -e esp32-s2-saola-1 -t clean
```

## 📦 Output Files

After successful build, the following files are generated:

| File | Size | Description |
|------|------|-------------|
| `bootloader.bin` | ~25KB | ESP32-S2 bootloader |
| `partitions.bin` | ~3KB | Partition table |
| `octane_auth_firmware.bin` | ~200KB | Main HID mouse firmware |

These files are automatically copied to the `../OctaneFlasher/` directory for use with the C# flasher application.

## 🔍 Build Validation

The build system includes automatic validation:

### Pre-build Checks
- ✅ Environment validation
- ✅ TinyUSB component availability
- ✅ USB descriptor configuration
- ✅ Build information setup

### Post-build Checks
- ✅ File size validation
- ✅ Checksum generation
- ✅ Automatic file copying
- ✅ Build report generation

## 🐛 Troubleshooting

### Common Issues

**ESP-IDF not found**
```bash
# Windows
%USERPROFILE%\esp\esp-idf\export.bat

# Linux/Mac
source ~/esp/esp-idf/export.sh
```

**PlatformIO not found**
```bash
pip install platformio
# Or install PlatformIO IDE
```

**TinyUSB errors**
- Ensure ESP-IDF v4.4+ is used
- Check `sdkconfig.defaults` for TinyUSB settings

**Build fails**
```bash
# Clean and rebuild
idf.py clean
idf.py build

# Or for PlatformIO
pio run -t clean
pio run
```

### Build Logs
Check these locations for detailed logs:
- ESP-IDF: `build/build.log`
- PlatformIO: `.pio/build/*/build.log`

## 🚀 Advanced Usage

### Custom Board Configuration
Edit `platformio.ini` to add new board configurations:

```ini
[env:custom-board]
extends = env:esp32-s2-saola-1
board = your_custom_board
build_flags = 
    ${env:esp32-s2-saola-1.build_flags}
    -DCUSTOM_DEFINE=1
```

### Debug Build
For debugging with verbose output:

```bash
# ESP-IDF
idf.py build -DCONFIG_LOG_DEFAULT_LEVEL_DEBUG=1

# PlatformIO
pio run -e esp32-s2-debug
```

### Release Build
For optimized production build:

```bash
# PlatformIO
pio run -e esp32-s2-release
```

## 📊 Performance Metrics

| Metric | Value |
|--------|-------|
| **Build Time** | 30-60 seconds |
| **Flash Time** | 10-20 seconds |
| **Firmware Size** | ~200KB |
| **RAM Usage** | ~50KB |
| **Boot Time** | <2 seconds |

## 🔗 Integration

The build system integrates with:
- **OctaneFlasher**: Automatic file copying
- **Git**: Version tracking in builds
- **CI/CD**: Scriptable build process
- **IDE**: VS Code and PlatformIO IDE support

## 📝 Notes

- Always use the provided build scripts for consistency
- Firmware files are automatically validated for size and integrity
- Build reports are generated in `BUILD_REPORT.md`
- Both build systems produce identical firmware binaries
