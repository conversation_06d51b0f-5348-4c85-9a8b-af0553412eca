using System;

namespace RecoilController.Views.Components
{
    public static class MainWindowStructure
    {
        public static string GetHtmlStructure()
        {
            return @"
<body>
    <div class='container'>
        <div class='title-bar'>
            <div class='title-text'>Octane Recoil Scripts v4.2.1</div>
        </div>

        <main class='main-content'>
            <nav class='tab-nav'>
                <button class='tab-btn active' data-tab='main'>Main</button>
                <button class='tab-btn' data-tab='settings'>Settings</button>
                <button class='tab-btn' data-tab='misc'>Miscellaneous</button>
                <button class='tab-btn' data-tab='keybinds'>Keybinds</button>
                <button class='tab-btn' data-tab='loadouts'>Loadouts</button>
            </nav>

            <div class='content-area'>
                <div id='main-tab' class='tab-content active'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Weapon Selection</h3>
                            <div class='form-row'>
                                <label>Primary Weapon:</label>
                                <select id='primary-weapon'>
                                    <option value='ak47'>AK-47</option>
                                    <option value='lr300'>LR-300</option>
                                    <option value='mp5a4'>MP5A4</option>
                                    <option value='thompson'>Thompson</option>
                                    <option value='custom-smg'>Custom SMG</option>
                                    <option value='m249'>M249</option>
                                    <option value='l96'>L96</option>
                                    <option value='bolt-action'>Bolt Action Rifle</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Sight:</label>
                                <select id='sight'>
                                    <option value='none'>Iron Sights</option>
                                    <option value='holo'>Holographic</option>
                                    <option value='red-dot'>Red Dot</option>
                                    <option value='acog'>ACOG</option>
                                    <option value='scope'>8x Scope</option>
                                    <option value='simple'>Simple Sight</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Muzzle:</label>
                                <select id='muzzle'>
                                    <option value='none'>None</option>
                                    <option value='compensator'>Compensator</option>
                                    <option value='flash-hider'>Flash Hider</option>
                                    <option value='muzzle-brake'>Muzzle Brake</option>
                                    <option value='silencer'>Silencer</option>
                                    <option value='muzzle-boost'>Muzzle Boost</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Barrel:</label>
                                <select id='barrel'>
                                    <option value='none'>None</option>
                                    <option value='extended'>Extended Barrel</option>
                                    <option value='heavy'>Heavy Barrel</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class='section-card'>
                            <h3>Quick Settings</h3>
                            <div class='form-row'>
                                <label>Recoil Compensation: <span id='recoil-value'>85%</span></label>
                                <input type='range' id='recoil-comp' min='0' max='100' value='85'>
                            </div>
                            <div class='form-row'>
                                <label>Humanization: <span id='humanization-value'>15%</span></label>
                                <input type='range' id='humanization' min='0' max='50' value='15'>
                            </div>
                            <div class='form-row'>
                                <label>Hip Fire Control:</label>
                                <input type='checkbox' id='hit-fire-control' checked>
                            </div>
                            <div class='form-row'>
                                <label>Script Status:</label>
                                <button id='toggle-script' class='status-btn active'>ENABLED</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id='settings-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Sensitivity Settings</h3>
                            <div class='form-row'>
                                <label>Mouse Sensitivity: <span id='sens-value'>2.50</span></label>
                                <input type='range' id='sensitivity' min='0.1' max='10' step='0.01' value='2.5'>
                            </div>
                            <div class='form-row'>
                                <label>ADS Sensitivity: <span id='ads-value'>1.00</span></label>
                                <input type='range' id='ads-sensitivity' min='0.1' max='3' step='0.01' value='1.0'>
                            </div>
                            <div class='form-row'>
                                <label>Field of View: <span id='fov-value'>90°</span></label>
                                <input type='range' id='fov' min='60' max='120' step='1' value='90'>
                            </div>
                        </div>
                        
                        <div class='section-card'>
                            <h3>Advanced Settings</h3>
                            <div class='form-row'>
                                <label>Horizontal Multiplier: <span id='horizontal-value'>100%</span></label>
                                <input type='range' id='horizontal' min='50' max='150' step='1' value='100'>
                            </div>
                            <div class='form-row'>
                                <label>Vertical Multiplier: <span id='vertical-value'>100%</span></label>
                                <input type='range' id='vertical' min='50' max='150' step='1' value='100'>
                            </div>
                            <div class='form-row'>
                                <label>Smoothing: <span id='smoothing-value'>50%</span></label>
                                <input type='range' id='smoothing' min='0' max='100' step='1' value='50'>
                            </div>
                        </div>
                    </div>
                </div>

                <div id='misc-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Game Features</h3>
                            <div class='form-row'>
                                <label>Cursor Check:</label>
                                <input type='checkbox' id='cursor-check'>
                            </div>
                            <div class='form-row'>
                                <label>Rapid Fire:</label>
                                <input type='checkbox' id='rapid-fire'>
                            </div>
                            <div class='form-row'>
                                <label>Anti AFK:</label>
                                <input type='checkbox' id='anti-afk'>
                            </div>
                            <div class='form-row'>
                                <label>AFK Interval: <span id='afk-interval-value'>30s</span></label>
                                <input type='range' id='afk-interval' min='10' max='300' step='5' value='30'>
                            </div>
                        </div>
                        
                        <div class='section-card'>
                            <h3>ESP32 Settings</h3>
                            <div class='form-row'>
                                <label>Device Status:</label>
                                <span id='esp32-status' style='color: #ff4757;'>Disconnected</span>
                            </div>
                            <div class='form-row'>
                                <label>Connection Port:</label>
                                <select id='esp32-port'>
                                    <option value='auto'>Auto Detect</option>
                                    <option value='COM3'>COM3</option>
                                    <option value='COM4'>COM4</option>
                                    <option value='COM5'>COM5</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <button id='connect-esp32' class='status-btn'>Connect ESP32</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div id='keybinds-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Weapon Keybinds</h3>
                            <div class='form-row'>
                                <label>Primary Weapon:</label>
                                <input type='text' id='primary-key' value='1' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>Secondary Weapon:</label>
                                <input type='text' id='secondary-key' value='2' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>Melee Weapon:</label>
                                <input type='text' id='melee-key' value='3' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>Grenade:</label>
                                <input type='text' id='grenade-key' value='4' readonly onclick='captureKey(this)'>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Script Controls</h3>
                            <div class='form-row'>
                                <label>Toggle Script:</label>
                                <input type='text' id='toggle-key' value='F1' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>Panic Mode:</label>
                                <input type='text' id='panic-key' value='F12' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>ESP32 Connect:</label>
                                <input type='text' id='esp32-key' value='F2' readonly onclick='captureKey(this)'>
                            </div>
                            <div class='form-row'>
                                <label>Reload Config:</label>
                                <input type='text' id='reload-key' value='F5' readonly onclick='captureKey(this)'>
                            </div>
                        </div>
                    </div>
                </div>

                <div id='loadouts-tab' class='tab-content'>
                    <div class='section-grid'>
                        <div class='section-card'>
                            <h3>Create Loadout</h3>
                            <div class='form-row'>
                                <label>Loadout Name:</label>
                                <input type='text' id='loadout-name' placeholder='Enter loadout name'>
                            </div>
                            <div class='form-row'>
                                <label>Weapon:</label>
                                <select id='loadout-weapon'>
                                    <option value='ak47'>AK-47</option>
                                    <option value='m4a4'>M4A4</option>
                                    <option value='m4a1s'>M4A1-S</option>
                                    <option value='awp'>AWP</option>
                                    <option value='mp9'>MP9</option>
                                    <option value='ump45'>UMP-45</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Attachments:</label>
                                <select id='loadout-sight'>
                                    <option value='none'>Iron Sights</option>
                                    <option value='holo'>Holographic</option>
                                    <option value='red-dot'>Red Dot</option>
                                    <option value='acog'>ACOG</option>
                                </select>
                            </div>
                            <div class='form-row'>
                                <label>Keybind:</label>
                                <input type='text' id='loadout-key' readonly onclick='captureKey(this)' placeholder='Click to set key'>
                            </div>
                            <div class='form-row'>
                                <button id='save-loadout' class='status-btn'>Save Loadout</button>
                            </div>
                        </div>

                        <div class='section-card'>
                            <h3>Saved Loadouts</h3>
                            <div id='loadouts-list'>
                                <div class='loadout-item'>
                                    <span class='loadout-name'>Default AK-47</span>
                                    <span class='loadout-key'>F6</span>
                                    <button class='loadout-delete'>×</button>
                                </div>
                                <div class='loadout-item'>
                                    <span class='loadout-name'>Sniper Setup</span>
                                    <span class='loadout-key'>F7</span>
                                    <button class='loadout-delete'>×</button>
                                </div>
                                <div class='loadout-item'>
                                    <span class='loadout-name'>SMG Rush</span>
                                    <span class='loadout-key'>F8</span>
                                    <button class='loadout-delete'>×</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>";
        }
    }
}
