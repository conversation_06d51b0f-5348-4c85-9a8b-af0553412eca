using System;
using System.IO.Ports;
using System.Threading.Tasks;
using System.Text.Json;
using RecoilController.Data;
using RecoilController.Models;

namespace RecoilController.Services
{
    public class ESP32Service
    {
        private SerialPort _serialPort;
        private bool _isConnected = false;
        private string _currentPort = "";

        public event EventHandler<bool> ConnectionStatusChanged;
        public event EventHandler<string> ErrorOccurred;

        public bool IsConnected => _isConnected;
        public string CurrentPort => _currentPort;

        public async Task<bool> ConnectAsync(string portName = "auto")
        {
            try
            {
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                if (portName == "auto")
                {
                    portName = await DetectESP32PortAsync();
                    if (string.IsNullOrEmpty(portName))
                    {
                        ErrorOccurred?.Invoke(this, "No ESP32 device detected");
                        return false;
                    }
                }

                _serialPort = new SerialPort(portName, 115200, Parity.None, 8, StopBits.One);
                _serialPort.DataReceived += OnDataReceived;
                _serialPort.ErrorReceived += OnErrorReceived;

                _serialPort.Open();
                _currentPort = portName;
                _isConnected = true;

                // Send initialization command
                await SendCommandInternalAsync(new ESP32Command
                {
                    Type = "init",
                    Data = new { version = "4.2.1", app = "OctaneRecoilScripts" }
                });

                ConnectionStatusChanged?.Invoke(this, true);
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Connection failed: {ex.Message}");
                return false;
            }
        }

        public async Task DisconnectAsync()
        {
            try
            {
                if (_serialPort?.IsOpen == true)
                {
                    _serialPort.Close();
                }
                _serialPort?.Dispose();
                _serialPort = null;
                _isConnected = false;
                _currentPort = "";

                ConnectionStatusChanged?.Invoke(this, false);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Disconnect error: {ex.Message}");
            }
        }

        public async Task SendMouseMovementAsync(float deltaX, float deltaY)
        {
            if (!_isConnected) return;

            try
            {
                await SendCommandInternalAsync(new ESP32Command
                {
                    Type = "mouse_move",
                    Data = new { x = deltaX, y = deltaY }
                });
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Mouse movement error: {ex.Message}");
            }
        }

        public async Task SendRecoilPatternAsync(Weapon weapon, float sensitivity, float compensation)
        {
            if (!_isConnected) return;

            try
            {
                var recoilData = new
                {
                    weapon = weapon.Name,
                    delay = weapon.Delay,
                    pattern = weapon.RecoilPattern,
                    sensitivity = sensitivity,
                    compensation = compensation
                };

                await SendCommandInternalAsync(new ESP32Command
                {
                    Type = "recoil_pattern",
                    Data = recoilData
                });
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Recoil pattern error: {ex.Message}");
            }
        }

        public async Task SendConfigurationAsync(WeaponConfiguration config)
        {
            if (!_isConnected) return;

            try
            {
                await SendCommandInternalAsync(new ESP32Command
                {
                    Type = "config_update",
                    Data = config
                });
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Configuration error: {ex.Message}");
            }
        }

        public async Task SendCommandAsync(ESP32Command command)
        {
            await SendCommandInternalAsync(command);
        }

        private async Task SendCommandInternalAsync(ESP32Command command)
        {
            if (!_isConnected || _serialPort == null) return;

            try
            {
                var json = JsonSerializer.Serialize(command);
                var data = System.Text.Encoding.UTF8.GetBytes(json + "\n");
                
                await Task.Run(() => _serialPort.Write(data, 0, data.Length));
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Send command error: {ex.Message}");
            }
        }

        private async Task<string> DetectESP32PortAsync()
        {
            try
            {
                var ports = SerialPort.GetPortNames();
                
                foreach (var port in ports)
                {
                    try
                    {
                        using (var testPort = new SerialPort(port, 115200))
                        {
                            testPort.Open();
                            testPort.WriteLine("ping");
                            
                            await Task.Delay(100);
                            
                            if (testPort.BytesToRead > 0)
                            {
                                var response = testPort.ReadExisting();
                                if (response.Contains("ESP32") || response.Contains("octane"))
                                {
                                    return port;
                                }
                            }
                        }
                    }
                    catch
                    {
                        // Continue to next port
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Port detection error: {ex.Message}");
            }

            return null;
        }

        private void OnDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                var data = _serialPort.ReadExisting();
                // Handle incoming data from ESP32
                ProcessIncomingData(data);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Data receive error: {ex.Message}");
            }
        }

        private void OnErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            ErrorOccurred?.Invoke(this, $"Serial error: {e.EventType}");
        }

        private void ProcessIncomingData(string data)
        {
            // Process responses from ESP32
            // This could include status updates, error messages, etc.
            if (data.Contains("error"))
            {
                ErrorOccurred?.Invoke(this, $"ESP32 error: {data}");
            }
        }

        public void Dispose()
        {
            DisconnectAsync().Wait();
        }
    }

    public class ESP32Command
    {
        public string Type { get; set; }
        public object Data { get; set; }
    }

    public class WeaponConfiguration
    {
        public string WeaponName { get; set; }
        public string Sight { get; set; }
        public string Muzzle { get; set; }
        public string Barrel { get; set; }
        public float RecoilCompensation { get; set; }
        public float Humanization { get; set; }
        public float Sensitivity { get; set; }
        public float ADSSensitivity { get; set; }
        public int FOV { get; set; }
        public float HorizontalMultiplier { get; set; }
        public float VerticalMultiplier { get; set; }
        public float Smoothing { get; set; }
        public bool CursorCheck { get; set; }
        public bool RapidFire { get; set; }
        public bool AntiAFK { get; set; }
    }
}
