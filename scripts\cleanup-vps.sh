#!/bin/bash

# VPS Cleanup and Optimization Script
# Designed for 1GB RAM VPS optimization

echo "🧹 OCTANE VPS CLEANUP & OPTIMIZATION"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

CLEANED_SIZE=0

# Function to log cleanup actions
log_cleanup() {
    local action="$1"
    local size="$2"
    echo -e "${GREEN}✅ $action${NC}"
    if [ ! -z "$size" ]; then
        echo -e "   Freed: ${YELLOW}$size${NC}"
        CLEANED_SIZE=$((CLEANED_SIZE + size))
    fi
}

echo -e "${YELLOW}1. SYSTEM PACKAGE CLEANUP${NC}"
echo "-------------------------"

# Clean package cache
apt-get clean
log_cleanup "Cleaned APT package cache"

# Remove orphaned packages
apt-get autoremove -y
log_cleanup "Removed orphaned packages"

# Clean old kernels (keep current + 1)
apt-get autoclean
log_cleanup "Cleaned old package files"

echo -e "\n${YELLOW}2. LOG FILE CLEANUP${NC}"
echo "-------------------"

# Clean system logs older than 7 days
journalctl --vacuum-time=7d
log_cleanup "Cleaned system logs (kept 7 days)"

# Clean PM2 logs
if [ -d "/opt/octane-auth/.pm2/logs" ]; then
    find /opt/octane-auth/.pm2/logs -name "*.log" -mtime +7 -delete
    log_cleanup "Cleaned old PM2 logs"
fi

# Truncate large log files
for logfile in /var/log/*.log; do
    if [ -f "$logfile" ] && [ $(stat -f%z "$logfile" 2>/dev/null || stat -c%s "$logfile") -gt 10485760 ]; then
        > "$logfile"
        log_cleanup "Truncated large log file: $(basename $logfile)"
    fi
done

echo -e "\n${YELLOW}3. TEMPORARY FILE CLEANUP${NC}"
echo "--------------------------"

# Clean /tmp
find /tmp -type f -atime +7 -delete 2>/dev/null
log_cleanup "Cleaned old temporary files"

# Clean /var/tmp
find /var/tmp -type f -atime +7 -delete 2>/dev/null
log_cleanup "Cleaned old variable temporary files"

echo -e "\n${YELLOW}4. NODE.JS OPTIMIZATION${NC}"
echo "------------------------"

# Clean npm cache
if command -v npm &> /dev/null; then
    npm cache clean --force
    log_cleanup "Cleaned NPM cache"
fi

# Remove node_modules in non-production directories
find /opt -name "node_modules" -path "*/test/*" -o -path "*/tests/*" -o -path "*/dev/*" | xargs rm -rf 2>/dev/null
log_cleanup "Removed development node_modules"

echo -e "\n${YELLOW}5. MONGODB OPTIMIZATION${NC}"
echo "------------------------"

# Compact MongoDB collections (if running)
if systemctl is-active --quiet mongod; then
    echo "MongoDB is running - performing maintenance..."
    
    # Repair and compact database
    mongosh octane-auth --eval "db.runCommand({compact: 'licenses'})" --quiet 2>/dev/null
    mongosh octane-auth --eval "db.runCommand({compact: 'security_events'})" --quiet 2>/dev/null
    log_cleanup "Compacted MongoDB collections"
fi

echo -e "\n${YELLOW}6. MEMORY OPTIMIZATION${NC}"
echo "-----------------------"

# Clear page cache, dentries and inodes
sync
echo 3 > /proc/sys/vm/drop_caches
log_cleanup "Cleared system caches"

# Optimize swappiness for low memory
echo 10 > /proc/sys/vm/swappiness
log_cleanup "Optimized swappiness for low memory"

echo -e "\n${YELLOW}7. DISK SPACE ANALYSIS${NC}"
echo "-----------------------"

# Show largest directories
echo "Largest directories:"
du -h /opt /var /usr/local 2>/dev/null | sort -hr | head -10

echo -e "\n${YELLOW}8. PROCESS OPTIMIZATION${NC}"
echo "------------------------"

# Restart PM2 to clear memory leaks
if command -v pm2 &> /dev/null; then
    sudo -u octane pm2 restart octane-auth
    log_cleanup "Restarted PM2 processes"
fi

# Kill any zombie processes
ps aux | awk '$8 ~ /^Z/ { print $2 }' | xargs kill -9 2>/dev/null
log_cleanup "Cleaned zombie processes"

echo -e "\n${YELLOW}9. SECURITY CLEANUP${NC}"
echo "-------------------"

# Remove old SSH logs
find /var/log -name "auth.log*" -mtime +30 -delete 2>/dev/null
log_cleanup "Cleaned old SSH logs"

# Clear bash history for security
> /root/.bash_history
log_cleanup "Cleared root bash history"

echo -e "\n${YELLOW}10. FINAL SYSTEM STATUS${NC}"
echo "------------------------"

# Show current system status
echo "Current system status:"
echo "Memory usage:"
free -h
echo ""
echo "Disk usage:"
df -h /
echo ""
echo "Load average:"
uptime

echo -e "\n${GREEN}🎉 CLEANUP COMPLETED!${NC}"
echo "======================"
echo -e "Total space optimized: ${YELLOW}${CLEANED_SIZE}MB${NC}"
echo -e "System is now optimized for ${YELLOW}1GB RAM${NC} operation"

# Create optimization flag
echo "$(date): VPS optimized" > /opt/octane-auth/last-optimization.log
