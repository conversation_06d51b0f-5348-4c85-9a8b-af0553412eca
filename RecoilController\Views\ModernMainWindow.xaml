<Window x:Class="RecoilController.Views.ModernMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        Title="Octane Recoil Controller - Professional Edition" 
        Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="Black"
        WindowStyle="None"
        AllowsTransparency="True"
        Icon="pack://application:,,,/icon.ico">
    
    <Grid>
        <!-- Custom Title Bar -->
        <Grid Height="30" VerticalAlignment="Top" Background="#1a1d2a" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                <Ellipse Width="16" Height="16" Fill="#8E54E9" Margin="0,0,8,0"/>
                <TextBlock Text="Octane Recoil Controller" 
                           Foreground="White" VerticalAlignment="Center" FontWeight="Medium"/>
            </StackPanel>
            
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="MinimizeButton" Content="─" Width="30" Height="30" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="MinimizeButton_Click" FontSize="12"/>
                <Button x:Name="MaximizeButton" Content="□" Width="30" Height="30" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="MaximizeButton_Click" FontSize="12"/>
                <Button x:Name="CloseButton" Content="×" Width="30" Height="30" 
                        Background="Transparent" Foreground="White" BorderThickness="0"
                        Click="CloseButton_Click" FontSize="16"/>
            </StackPanel>
        </Grid>
        
        <!-- WebView2 Control -->
        <wv2:WebView2 x:Name="webView" 
                      Margin="0,30,0,0"
                      NavigationCompleted="WebView_NavigationCompleted"/>
    </Grid>
</Window>
