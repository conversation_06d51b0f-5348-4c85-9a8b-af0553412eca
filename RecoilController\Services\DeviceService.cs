using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using RecoilController.Models;

namespace RecoilController.Services
{
    /// <summary>
    /// Service for communicating with the ESP32 device
    /// </summary>
    public class DeviceService : IDisposable
    {
        private SerialPort _serialPort;
        private readonly object _lockObject = new object();
        private CancellationTokenSource _cancellationTokenSource;
        private Task _monitoringTask;

        public event EventHandler<DeviceInfo> DeviceStatusChanged;
        public event EventHandler<DeviceResponsePacket> ResponseReceived;

        public DeviceInfo CurrentDevice { get; private set; } = new DeviceInfo();

        public DeviceService()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            StartDeviceMonitoring();
        }

        /// <summary>
        /// Scans for available COM ports that might have an ESP32
        /// </summary>
        public List<string> ScanForDevices()
        {
            try
            {
                var availablePorts = SerialPort.GetPortNames().ToList();
                return availablePorts;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error scanning for devices: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Attempts to connect to the ESP32 on the specified port
        /// </summary>
        public async Task<bool> ConnectToDevice(string portName)
        {
            try
            {
                lock (_lockObject)
                {
                    // Disconnect existing connection
                    DisconnectDevice();

                    _serialPort = new SerialPort(portName, 115200, Parity.None, 8, StopBits.One)
                    {
                        ReadTimeout = 1000,
                        WriteTimeout = 1000
                    };

                    _serialPort.DataReceived += OnDataReceived;
                    _serialPort.Open();

                    CurrentDevice.PortName = portName;
                    CurrentDevice.Status = DeviceStatus.Connected;
                    CurrentDevice.LastSeen = DateTime.Now;
                    CurrentDevice.ErrorMessage = null;
                }

                // Send ping to verify connection
                await SendCommand(new DeviceCommandPacket(DeviceCommand.Ping));
                
                // Request firmware version
                await SendCommand(new DeviceCommandPacket(DeviceCommand.GetVersion));

                OnDeviceStatusChanged();
                return true;
            }
            catch (Exception ex)
            {
                CurrentDevice.Status = DeviceStatus.Error;
                CurrentDevice.ErrorMessage = ex.Message;
                OnDeviceStatusChanged();
                return false;
            }
        }

        /// <summary>
        /// Disconnects from the current device
        /// </summary>
        public void DisconnectDevice()
        {
            lock (_lockObject)
            {
                if (_serialPort?.IsOpen == true)
                {
                    _serialPort.DataReceived -= OnDataReceived;
                    _serialPort.Close();
                }
                _serialPort?.Dispose();
                _serialPort = null;

                CurrentDevice.Status = DeviceStatus.Disconnected;
                CurrentDevice.PortName = null;
                CurrentDevice.ErrorMessage = null;
            }

            OnDeviceStatusChanged();
        }

        /// <summary>
        /// Sends a command to the ESP32 device
        /// </summary>
        public async Task<bool> SendCommand(DeviceCommandPacket command)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_serialPort?.IsOpen != true)
                        return false;

                    var data = command.ToBytes();
                    _serialPort.Write(data, 0, data.Length);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending command: {ex.Message}");
                CurrentDevice.Status = DeviceStatus.Error;
                CurrentDevice.ErrorMessage = ex.Message;
                OnDeviceStatusChanged();
                return false;
            }
        }

        /// <summary>
        /// Sends license key to the device
        /// </summary>
        public async Task<bool> SetLicenseKey(string licenseKey)
        {
            return await SendTextCommand($"license {licenseKey}");
        }

        /// <summary>
        /// Sends weapon selection to the device
        /// </summary>
        public async Task<bool> SetWeapon(int weaponId)
        {
            return await SendTextCommand($"weapon {weaponId}");
        }

        /// <summary>
        /// Sets sensitivity on the device
        /// </summary>
        public async Task<bool> SetSensitivity(float sensitivity)
        {
            return await SendTextCommand($"sensitivity {sensitivity:F2}");
        }

        /// <summary>
        /// Sets scope multiplier on the device
        /// </summary>
        public async Task<bool> SetScopeMultiplier(float multiplier)
        {
            return await SendTextCommand($"scope {multiplier:F2}");
        }

        /// <summary>
        /// Starts recoil control on the device
        /// </summary>
        public async Task<bool> StartRecoil()
        {
            return await SendTextCommand("start");
        }

        /// <summary>
        /// Stops recoil control on the device
        /// </summary>
        public async Task<bool> StopRecoil()
        {
            return await SendTextCommand("stop");
        }

        /// <summary>
        /// Tests mouse movement on the device
        /// </summary>
        public async Task<bool> TestMouse()
        {
            return await SendTextCommand("test");
        }

        /// <summary>
        /// Gets device status
        /// </summary>
        public async Task<bool> GetStatus()
        {
            return await SendTextCommand("status");
        }

        /// <summary>
        /// Gets available weapons list
        /// </summary>
        public async Task<bool> GetWeapons()
        {
            return await SendTextCommand("weapons");
        }

        /// <summary>
        /// Sends a text command to the device
        /// </summary>
        private async Task<bool> SendTextCommand(string command)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_serialPort?.IsOpen != true)
                        return false;

                    var data = System.Text.Encoding.UTF8.GetBytes(command + "\n");
                    _serialPort.Write(data, 0, data.Length);
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending text command: {ex.Message}");
                CurrentDevice.Status = DeviceStatus.Error;
                CurrentDevice.ErrorMessage = ex.Message;
                OnDeviceStatusChanged();
                return false;
            }
        }

        private void OnDataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                lock (_lockObject)
                {
                    if (_serialPort?.IsOpen != true)
                        return;

                    // Read text response from HID firmware
                    string response = _serialPort.ReadExisting();
                    if (string.IsNullOrEmpty(response))
                        return;

                    // Process each line of response
                    var lines = response.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var line in lines)
                    {
                        ProcessResponseLine(line.Trim());
                    }

                    CurrentDevice.LastSeen = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing received data: {ex.Message}");
            }
        }

        private void ProcessResponseLine(string line)
        {
            if (string.IsNullOrEmpty(line))
                return;

            // Parse different types of responses
            if (line.Contains("Hardware ID:"))
            {
                CurrentDevice.HardwareId = line.Split(':')[1].Trim();
            }
            else if (line.Contains("License Key:"))
            {
                // Don't store the actual license key for security
                CurrentDevice.HasLicense = !line.Contains("Not Set");
            }
            else if (line.Contains("Auth Status:"))
            {
                CurrentDevice.IsAuthenticated = line.Contains("✅ Valid");
            }
            else if (line.Contains("Current Weapon:"))
            {
                var weaponInfo = line.Split(':')[1].Trim();
                CurrentDevice.CurrentWeapon = weaponInfo.Split('(')[0].Trim();
            }
            else if (line.Contains("USB HID:"))
            {
                CurrentDevice.HidConnected = line.Contains("Connected");
            }
            else if (line.Contains("✅") || line.Contains("❌"))
            {
                // Status message - could trigger UI updates
                var responsePacket = new DeviceResponsePacket(DeviceResponse.StatusMessage,
                    System.Text.Encoding.UTF8.GetBytes(line));
                ResponseReceived?.Invoke(this, responsePacket);
            }

            // Always update status
            OnDeviceStatusChanged();
        }

        private void StartDeviceMonitoring()
        {
            _monitoringTask = Task.Run(async () =>
            {
                while (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    try
                    {
                        // Auto-detect device if not connected
                        if (CurrentDevice.Status == DeviceStatus.Disconnected)
                        {
                            await TryAutoDetectDevice();
                        }
                        
                        await Task.Delay(2000, _cancellationTokenSource.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error in device monitoring: {ex.Message}");
                    }
                }
            });
        }

        private async Task TryAutoDetectDevice()
        {
            var ports = ScanForDevices();
            foreach (var port in ports)
            {
                if (await ConnectToDevice(port))
                {
                    break;
                }
                await Task.Delay(100); // Small delay between attempts
            }
        }

        private void OnDeviceStatusChanged()
        {
            DeviceStatusChanged?.Invoke(this, CurrentDevice);
        }

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _monitoringTask?.Wait(1000);
            DisconnectDevice();
            _cancellationTokenSource?.Dispose();
        }
    }
}
