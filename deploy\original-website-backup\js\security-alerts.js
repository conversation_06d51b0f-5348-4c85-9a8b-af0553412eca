// Security Alerts Dashboard JavaScript
class SecurityAlertsDashboard {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.totalPages = 1;
        this.alerts = [];
        this.filteredAlerts = [];
        this.filters = {
            severity: '',
            type: '',
            date: '',
            search: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadAlerts();
        this.loadStats();
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            this.loadAlerts();
            this.loadStats();
        }, 30000);
    }
    
    setupEventListeners() {
        // Filter controls
        document.getElementById('applyFiltersBtn').addEventListener('click', () => this.applyFilters());
        document.getElementById('clearFiltersBtn').addEventListener('click', () => this.clearFilters());
        
        // Action buttons
        document.getElementById('exportLogsBtn').addEventListener('click', () => this.exportLogs());
        document.getElementById('clearLogsBtn').addEventListener('click', () => this.clearLogs());
        document.getElementById('testAlertBtn').addEventListener('click', () => this.testAlert());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refresh());
        
        // Pagination
        document.getElementById('prevPage').addEventListener('click', () => this.previousPage());
        document.getElementById('nextPage').addEventListener('click', () => this.nextPage());
        
        // Enter key for search
        document.getElementById('searchFilter').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });
    }
    
    async loadStats() {
        try {
            const response = await fetch('/api/security/stats');
            const stats = await response.json();
            
            if (stats.success !== false) {
                document.getElementById('totalAlerts').textContent = stats.total || 0;
                document.getElementById('criticalAlerts').textContent = stats.critical || 0;
                document.getElementById('highAlerts').textContent = stats.high || 0;
                document.getElementById('mediumAlerts').textContent = stats.medium || 0;
                document.getElementById('lowAlerts').textContent = stats.low || 0;
                document.getElementById('todayAlerts').textContent = stats.active || 0;
            }
        } catch (error) {
            console.error('Failed to load stats:', error);
        }
    }
    
    async loadAlerts() {
        try {
            const response = await fetch('/api/security/events');
            const data = await response.json();
            
            if (data.success !== false) {
                this.alerts = data.events || data || [];
                this.applyFilters();
                this.updateLastAlertTime();
            } else {
                this.showError('Failed to load security alerts');
            }
        } catch (error) {
            console.error('Failed to load alerts:', error);
            this.showError('Failed to connect to server');
        }
    }
    
    applyFilters() {
        this.filters.severity = document.getElementById('severityFilter').value;
        this.filters.type = document.getElementById('typeFilter').value;
        this.filters.date = document.getElementById('dateFilter').value;
        this.filters.search = document.getElementById('searchFilter').value.toLowerCase();
        
        this.filteredAlerts = this.alerts.filter(alert => {
            // Severity filter
            if (this.filters.severity && alert.severity !== this.filters.severity) {
                return false;
            }
            
            // Type filter
            if (this.filters.type && alert.type !== this.filters.type) {
                return false;
            }
            
            // Date filter
            if (this.filters.date) {
                const alertDate = new Date(alert.timestamp).toISOString().split('T')[0];
                if (alertDate !== this.filters.date) {
                    return false;
                }
            }
            
            // Search filter
            if (this.filters.search) {
                const searchText = `${alert.username} ${alert.hardwareId} ${alert.ipAddress} ${alert.description}`.toLowerCase();
                if (!searchText.includes(this.filters.search)) {
                    return false;
                }
            }
            
            return true;
        });
        
        this.currentPage = 1;
        this.updatePagination();
        this.renderAlerts();
    }
    
    clearFilters() {
        document.getElementById('severityFilter').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('dateFilter').value = '';
        document.getElementById('searchFilter').value = '';
        this.applyFilters();
    }
    
    updatePagination() {
        this.totalPages = Math.ceil(this.filteredAlerts.length / this.pageSize);
        
        const pagination = document.getElementById('pagination');
        const pageInfo = document.getElementById('pageInfo');
        const prevBtn = document.getElementById('prevPage');
        const nextBtn = document.getElementById('nextPage');
        
        if (this.totalPages > 1) {
            pagination.style.display = 'flex';
            pageInfo.textContent = `Page ${this.currentPage} of ${this.totalPages}`;
            prevBtn.disabled = this.currentPage === 1;
            nextBtn.disabled = this.currentPage === this.totalPages;
        } else {
            pagination.style.display = 'none';
        }
    }
    
    renderAlerts() {
        const container = document.getElementById('alertsTableContainer');
        
        if (this.filteredAlerts.length === 0) {
            container.innerHTML = '<div class="no-data">No security alerts found</div>';
            return;
        }
        
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageAlerts = this.filteredAlerts.slice(startIndex, endIndex);
        
        let html = `
            <table class="alert-table">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Severity</th>
                        <th>Type</th>
                        <th>User</th>
                        <th>IP Address</th>
                        <th>Description</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        pageAlerts.forEach(alert => {
            const timestamp = new Date(alert.timestamp).toLocaleString();
            const hardwareId = alert.hardwareId ? alert.hardwareId.substring(0, 8) + '...' : 'N/A';
            
            html += `
                <tr>
                    <td>${timestamp}</td>
                    <td><span class="severity-badge severity-${alert.severity}">${alert.severity}</span></td>
                    <td>${alert.type}</td>
                    <td>${alert.username || 'Unknown'}</td>
                    <td>${alert.ipAddress || 'Unknown'}</td>
                    <td>${alert.description}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn-small btn-details" onclick="dashboard.toggleDetails('${alert._id}')">Details</button>
                            <button class="btn-small btn-ban" onclick="dashboard.banUser('${alert.hardwareId}', '${alert.username}')">Ban</button>
                            <button class="btn-small btn-allow" onclick="dashboard.allowUser('${alert.hardwareId}', '${alert.username}')">Allow</button>
                        </div>
                    </td>
                </tr>
                <tr id="details-${alert._id}" class="alert-details">
                    <td colspan="7">
                        ${this.renderAlertDetails(alert)}
                    </td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        container.innerHTML = html;
    }
    
    renderAlertDetails(alert) {
        let html = `
            <div><strong>Alert ID:</strong> ${alert._id}</div>
            <div><strong>Hardware ID:</strong> ${alert.hardwareId || 'N/A'}</div>
            <div><strong>User Agent:</strong> ${alert.userAgent || 'N/A'}</div>
        `;
        
        if (alert.metadata && Object.keys(alert.metadata).length > 0) {
            html += '<div class="metadata-grid">';
            
            Object.entries(alert.metadata).forEach(([key, value]) => {
                if (value && typeof value === 'object') {
                    // Handle nested objects
                    Object.entries(value).forEach(([subKey, subValue]) => {
                        html += `
                            <div class="metadata-item">
                                <div class="metadata-label">${key}.${subKey}</div>
                                <div class="metadata-value">${subValue}</div>
                            </div>
                        `;
                    });
                } else {
                    html += `
                        <div class="metadata-item">
                            <div class="metadata-label">${key}</div>
                            <div class="metadata-value">${value}</div>
                        </div>
                    `;
                }
            });
            
            html += '</div>';
        }
        
        return html;
    }
    
    toggleDetails(alertId) {
        const detailsRow = document.getElementById(`details-${alertId}`);
        detailsRow.classList.toggle('show');
    }
    
    async banUser(hardwareId, username) {
        if (!confirm(`Ban user ${username}? This will block their hardware ID: ${hardwareId?.substring(0, 16)}...`)) {
            return;
        }
        
        try {
            const response = await fetch('/api/security/ban-user', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ hardwareId, username, reason: 'Security violation' })
            });
            
            const result = await response.json();
            if (result.success) {
                this.showSuccess(`User ${username} has been banned`);
                this.loadAlerts();
            } else {
                this.showError(result.error || 'Failed to ban user');
            }
        } catch (error) {
            this.showError('Failed to ban user');
        }
    }
    
    async allowUser(hardwareId, username) {
        if (!confirm(`Allow user ${username}? This will unblock their hardware ID.`)) {
            return;
        }
        
        try {
            const response = await fetch('/api/security/allow-user', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ hardwareId, username })
            });
            
            const result = await response.json();
            if (result.success) {
                this.showSuccess(`User ${username} has been allowed`);
                this.loadAlerts();
            } else {
                this.showError(result.error || 'Failed to allow user');
            }
        } catch (error) {
            this.showError('Failed to allow user');
        }
    }
    
    async exportLogs() {
        try {
            const response = await fetch('/api/security/export-log');
            const blob = await response.blob();
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            this.showSuccess('Security logs exported successfully');
        } catch (error) {
            this.showError('Failed to export logs');
        }
    }
    
    async clearLogs() {
        if (!confirm('Are you sure you want to clear ALL security logs? This action cannot be undone.')) {
            return;
        }
        
        try {
            const response = await fetch('/api/security/clear-log', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('Security logs cleared successfully');
                this.loadAlerts();
                this.loadStats();
            } else {
                this.showError(result.error || 'Failed to clear logs');
            }
        } catch (error) {
            this.showError('Failed to clear logs');
        }
    }
    
    async testAlert() {
        try {
            const response = await fetch('/api/security/log-event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    type: 'test_alert',
                    severity: 'low',
                    description: 'Test security alert from admin dashboard',
                    username: 'admin',
                    hardwareId: 'TEST_HARDWARE_ID',
                    ipAddress: '127.0.0.1'
                })
            });
            
            const result = await response.json();
            if (result.success) {
                this.showSuccess('Test alert created successfully');
                this.loadAlerts();
                this.loadStats();
            } else {
                this.showError(result.error || 'Failed to create test alert');
            }
        } catch (error) {
            this.showError('Failed to create test alert');
        }
    }
    
    updateLastAlertTime() {
        if (this.alerts.length > 0) {
            const lastAlert = this.alerts[0];
            const lastTime = new Date(lastAlert.timestamp).toLocaleString();
            document.getElementById('lastAlertTime').textContent = lastTime;
        }
    }
    
    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.updatePagination();
            this.renderAlerts();
        }
    }
    
    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.updatePagination();
            this.renderAlerts();
        }
    }
    
    refresh() {
        this.loadAlerts();
        this.loadStats();
        this.showSuccess('Data refreshed');
    }
    
    showSuccess(message) {
        this.showAlert(message, 'success');
    }
    
    showError(message) {
        this.showAlert(message, 'error');
    }
    
    showAlert(message, type) {
        const container = document.getElementById('alertContainer');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <span>${message}</span>
            <button onclick="this.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 1.2em; cursor: pointer; float: right;">&times;</button>
        `;
        
        container.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new SecurityAlertsDashboard();
});
