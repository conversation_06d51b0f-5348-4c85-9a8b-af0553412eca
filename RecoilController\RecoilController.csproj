<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <Nullable>enable</Nullable>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>

    <StartupObject>RecoilController.App</StartupObject>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <TrimMode>partial</TrimMode>
    <PublishTrimmed>false</PublishTrimmed>
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <DefineConstants>RELEASE;SECURITY_ENABLED</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.IO.Ports" Version="7.0.0" />
    <PackageReference Include="ModernWpfUI" Version="0.9.6" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.122" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="System.Security.Cryptography.ProtectedData" Version="7.0.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.8" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2210.55" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
    <Resource Include="icon.ico" />
    <EmbeddedResource Include="Data\**\*" />
    <None Update="UI\octane.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="bin\**" />
    <Compile Remove="obj\**" />
    <EmbeddedResource Remove="bin\**" />
    <EmbeddedResource Remove="obj\**" />
    <None Remove="bin\**" />
    <None Remove="obj\**" />
    <Page Remove="bin\**" />
    <Page Remove="obj\**" />
  </ItemGroup>



</Project>
