﻿#pragma checksum "..\..\..\..\..\Views\SimpleMainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0C6336A7A92D280B7D5F59F6F10287F1441F8F88"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using ModernWpf;
using ModernWpf.Controls;
using ModernWpf.Controls.Primitives;
using ModernWpf.DesignTime;
using ModernWpf.Markup;
using ModernWpf.Media.Animation;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RecoilController.Views {
    
    
    /// <summary>
    /// SimpleMainWindow
    /// </summary>
    public partial class SimpleMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 57 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartStopButton;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmergencyStop;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WeaponCategoryCombo;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WeaponCombo;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeaponInfo;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RecoilToggle;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HipfireToggle;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HumanisationToggle;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RandomisationToggle;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RapidfireToggle;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CursorCheckToggle;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider SensitivitySlider;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SensitivityValue;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FovSlider;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FovValue;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HorizontalSlider;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HorizontalValue;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VerticalSlider;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VerticalValue;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AntiAfkToggle;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AfkIntervalText;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CrouchKeyText;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CodelockToggle;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodelockCodeText;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnterCodeButton;
        
        #line default
        #line hidden
        
        
        #line 242 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShotsCountText;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionTimeText;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentWeaponText;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecurityStatusText;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecoilMovementText;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgRecoilText;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetStatsButton;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LicenseStatusText;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HardwareIdText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestWebhookButton;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AboutButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RecoilController;V1.0.0.0;component/views/simplemainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\SimpleMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.StartStopButton = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.EmergencyStop = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.WeaponCategoryCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.WeaponCombo = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.WeaponInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.RecoilToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.HipfireToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.HumanisationToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.RandomisationToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.RapidfireToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.CursorCheckToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            this.SensitivitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 14:
            this.SensitivityValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.FovSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.FovValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.HorizontalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 18:
            this.HorizontalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.VerticalSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 20:
            this.VerticalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.AntiAfkToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.AfkIntervalText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.CrouchKeyText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.CodelockToggle = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.CodelockCodeText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 26:
            this.EnterCodeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 27:
            this.ShotsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.SessionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.CurrentWeaponText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.SecurityStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.RecoilMovementText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.AvgRecoilText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ResetStatsButton = ((System.Windows.Controls.Button)(target));
            return;
            case 34:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            return;
            case 35:
            this.LicenseStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.HardwareIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.TestWebhookButton = ((System.Windows.Controls.Button)(target));
            return;
            case 38:
            this.AboutButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

