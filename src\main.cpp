/*
 * ESP32-S2 HID Mouse Firmware - Octane Recoil Control System
 * Version: 2.0.0
 * Target: ESP32-S2 Mini (Lolin S2 Mini)
 *
 * Features:
 * - USB HID Mouse emulation
 * - Serial communication (115200 baud)
 * - JSON and simple text command support
 * - Command queue system (64 commands)
 * - LED status indication (GPIO15)
 * - Error handling and recovery
 * - Recoil compensation support
 */

#include <Arduino.h>
#include <USB.h>
#include <USBHIDMouse.h>
#include <ArduinoJson.h>
#include <freertos/FreeRTOS.h>
#include <freertos/queue.h>
#include <esp_system.h>

// ===== CONFIGURATION =====
#define FIRMWARE_VERSION "2.0.0"
#define HARDWARE_VERSION "ESP32-S2"
#define SERIAL_BAUD 115200
#define LED_PIN 15
#define QUEUE_SIZE 64
#define COMMAND_BUFFER_SIZE 512
#define JSON_BUFFER_SIZE 1024

// ===== COMMAND TYPES =====
enum CommandType {
    CMD_MOUSE_MOVE = 0,
    CMD_CLICK_DOWN = 1,
    CMD_CLICK_UP = 2,
    CMD_STATUS = 3
};

// ===== LED STATE DEFINITIONS =====
enum LEDState {
    LED_BOOT = 0,
    LED_WAITING = 1,
    LED_CONNECTED = 2,
    LED_ACTIVE = 3,
    LED_ERROR = 4
};

// ===== BUTTON DEFINITIONS =====
#define MOUSE_LEFT   0x01
#define MOUSE_RIGHT  0x02
#define MOUSE_MIDDLE 0x04

// ===== HID COMMAND STRUCTURE =====
struct HIDCommand {
    uint8_t type;           // Command type (CommandType enum)
    int8_t x;              // X movement (-127 to 127)
    int8_t y;              // Y movement (-127 to 127)
    uint8_t buttons;       // Button state
    uint32_t timestamp;    // Command timestamp
};


// ===== GLOBAL OBJECTS =====
USBHIDMouse Mouse;
QueueHandle_t commandQueue;
StaticJsonDocument<JSON_BUFFER_SIZE> jsonDoc;

// ===== GLOBAL VARIABLES =====
volatile LEDState currentLEDState = LED_BOOT;
volatile bool usbReady = false;
volatile bool serialReady = false;
volatile uint32_t bootTime = 0;
volatile uint32_t commandsProcessed = 0;
volatile uint32_t queueOverflows = 0;
volatile uint32_t lastHeartbeat = 0;

// ===== TIMING VARIABLES =====
uint32_t lastLEDUpdate = 0;
uint32_t lastQueueProcess = 0;
uint32_t lastStatusUpdate = 0;
bool ledState = false;

// ===== FUNCTION DECLARATIONS =====
void initializeHardware();
void initializeUSB();
void initializeSerial();
void initializeQueue();
void bootSequence();
void processSerialCommands();
void processCommandQueue();
void updateLEDStatus();
void processJSONCommand(const String& jsonStr);
void processSimpleCommand(const String& command);
bool queueCommand(const HIDCommand& cmd);
void executeHIDCommand(const HIDCommand& cmd);
void sendJSONResponse(const String& status, const String& message = "", JsonObject data = JsonObject());
void sendSimpleResponse(const String& response);
void sendError(const String& message, int code = 400);
void handlePing(bool isJSON = true);
void handleStatus(bool isJSON = true);
void handleMouseMove(int x, int y, bool isJSON = true);
void handleMouseClick(const String& button, const String& action, bool isJSON = true);
void setLEDState(LEDState state);
void debugPrint(const String& message);
uint32_t getUptime();
size_t getFreeHeap();
void printSystemInfo();

// ===== SETUP FUNCTION =====
void setup() {
    // Record boot time
    bootTime = millis();

    // Initialize hardware
    initializeHardware();

    // Initialize serial communication
    initializeSerial();

    // Initialize USB HID
    initializeUSB();

    // Initialize command queue
    initializeQueue();

    // Boot sequence
    bootSequence();

    // Print system information
    printSystemInfo();

    // Set ready state
    setLEDState(LED_WAITING);

    debugPrint("Octane ESP32-S2 HID Mouse v" + String(FIRMWARE_VERSION) + " Ready");
    Serial.println("Octane ESP32-S2 HID Mouse v" + String(FIRMWARE_VERSION) + " Ready");
}

// ===== MAIN LOOP =====
void loop() {
    // Process serial commands
    processSerialCommands();

    // Process command queue
    processCommandQueue();

    // Update LED status
    updateLEDStatus();

    // Heartbeat check
    if (millis() - lastHeartbeat > 30000) { // 30 second heartbeat
        lastHeartbeat = millis();
        debugPrint("Heartbeat - Uptime: " + String(getUptime()) + "s, Commands: " + String(commandsProcessed));
    }

    // Small delay to prevent watchdog issues
    delay(1);
}

// ===== INITIALIZATION FUNCTIONS =====
void initializeHardware() {
    // Initialize LED pin (active-low: HIGH = OFF, LOW = ON)
    pinMode(LED_PIN, OUTPUT);
    digitalWrite(LED_PIN, HIGH); // Start with LED OFF

    // Initialize random seed
    randomSeed(analogRead(0));

    debugPrint("Hardware initialized - LED on GPIO" + String(LED_PIN) + " (active-low)");
}

void initializeSerial() {
    Serial.begin(SERIAL_BAUD);

    // Wait for serial to be ready (max 3 seconds)
    uint32_t startTime = millis();
    while (!Serial && (millis() - startTime < 3000)) {
        delay(10);
    }

    serialReady = true;
    debugPrint("Serial initialized at " + String(SERIAL_BAUD) + " baud");
}

void initializeUSB() {
    // Initialize USB HID Mouse
    Mouse.begin();
    USB.begin();

    // Wait for USB to be ready (max 5 seconds)
    uint32_t startTime = millis();
    while (!USB && (millis() - startTime < 5000)) {
        delay(10);
    }

    usbReady = true;
    debugPrint("USB HID Mouse initialized");
}

void initializeQueue() {
    // Create command queue
    commandQueue = xQueueCreate(QUEUE_SIZE, sizeof(HIDCommand));

    if (commandQueue == NULL) {
        debugPrint("ERROR: Failed to create command queue");
        setLEDState(LED_ERROR);
        return;
    }

    debugPrint("Command queue initialized (size: " + String(QUEUE_SIZE) + ")");
}

void bootSequence() {
    debugPrint("Starting boot sequence on GPIO" + String(LED_PIN));

    // 3 quick flashes to indicate boot (active-low LED: LOW = ON, HIGH = OFF)
    for (int i = 0; i < 3; i++) {
        digitalWrite(LED_PIN, LOW);  // LED ON
        debugPrint("Boot flash " + String(i + 1) + " - LED ON");
        delay(200);
        digitalWrite(LED_PIN, HIGH); // LED OFF
        debugPrint("Boot flash " + String(i + 1) + " - LED OFF");
        delay(200);
    }

    debugPrint("Boot sequence complete - LED set to OFF");
}

// ===== COMMAND PROCESSING FUNCTIONS =====
void processSerialCommands() {
    if (!Serial.available()) return;

    String command = Serial.readStringUntil('\n');
    command.trim();

    if (command.length() == 0) return;

    debugPrint("Received command: " + command);

    // Check if it's a JSON command
    if (command.startsWith("{") && command.endsWith("}")) {
        processJSONCommand(command);
    } else {
        // Process as simple text command
        processSimpleCommand(command);
    }
}

void processJSONCommand(const String& jsonStr) {
    // Clear previous JSON document
    jsonDoc.clear();

    // Parse JSON
    DeserializationError error = deserializeJson(jsonDoc, jsonStr);

    if (error) {
        sendError("Invalid JSON format: " + String(error.c_str()));
        return;
    }

    // Extract command type and data
    String type = jsonDoc["Type"];
    JsonObject data = jsonDoc["Data"];

    if (type.isEmpty()) {
        sendError("Missing 'Type' field in JSON command");
        return;
    }

    // Process different command types
    if (type == "init") {
        String version = data["version"];
        String app = data["app"];

        StaticJsonDocument<256> responseData;
        responseData["firmware"] = FIRMWARE_VERSION;
        responseData["hardware"] = HARDWARE_VERSION;
        responseData["uptime"] = getUptime();

        sendJSONResponse("ready", "Initialization successful", responseData.as<JsonObject>());
        setLEDState(LED_CONNECTED);

    } else if (type == "mouse_move") {
        int x = data["x"];
        int y = data["y"];
        handleMouseMove(x, y, true);

    } else if (type == "mouse_click") {
        String button = data["button"];
        String action = data["action"];
        handleMouseClick(button, action, true);

    } else if (type == "ping") {
        handlePing(true);

    } else if (type == "status") {
        handleStatus(true);

    } else if (type == "queue_status") {
        StaticJsonDocument<256> responseData;
        responseData["queue_size"] = uxQueueMessagesWaiting(commandQueue);
        responseData["queue_max"] = QUEUE_SIZE;
        responseData["queue_free"] = uxQueueSpacesAvailable(commandQueue);

        sendJSONResponse("ok", "Queue status", responseData.as<JsonObject>());

    } else {
        sendError("Unknown command type: " + type, 404);
    }
}

void processSimpleCommand(const String& command) {
    String cmd = command;
    cmd.toUpperCase();

    if (cmd == "PING") {
        handlePing(false);

    } else if (cmd == "STATUS") {
        handleStatus(false);

    } else if (cmd == "VERSION") {
        sendSimpleResponse("VERSION:" + String(FIRMWARE_VERSION));

    } else if (cmd.startsWith("M")) {
        // Parse mouse movement command: M<x>,<y>
        int commaIndex = cmd.indexOf(',');
        if (commaIndex > 1) {
            int x = cmd.substring(1, commaIndex).toInt();
            int y = cmd.substring(commaIndex + 1).toInt();
            handleMouseMove(x, y, false);
        } else {
            sendSimpleResponse("ERROR:Invalid movement format");
        }

    } else if (cmd == "CLICK_LEFT_DOWN") {
        handleMouseClick("left", "down", false);

    } else if (cmd == "CLICK_LEFT_UP") {
        handleMouseClick("left", "up", false);

    } else if (cmd == "CLICK_RIGHT_DOWN") {
        handleMouseClick("right", "down", false);

    } else if (cmd == "CLICK_RIGHT_UP") {
        handleMouseClick("right", "up", false);

    } else if (cmd == "CLICK_MIDDLE_DOWN") {
        handleMouseClick("middle", "down", false);

    } else if (cmd == "CLICK_MIDDLE_UP") {
        handleMouseClick("middle", "up", false);

    } else {
        sendSimpleResponse("ERROR:Unknown command");
    }
}

// ===== COMMAND QUEUE FUNCTIONS =====
void processCommandQueue() {
    // Process commands at 1ms intervals
    if (millis() - lastQueueProcess < 1) return;

    lastQueueProcess = millis();

    HIDCommand cmd;
    if (xQueueReceive(commandQueue, &cmd, 0) == pdTRUE) {
        executeHIDCommand(cmd);
        commandsProcessed++;
        setLEDState(LED_ACTIVE);
    }
}

bool queueCommand(const HIDCommand& cmd) {
    if (xQueueSend(commandQueue, &cmd, 0) == pdTRUE) {
        return true;
    } else {
        queueOverflows++;
        debugPrint("Queue overflow! Commands lost: " + String(queueOverflows));
        return false;
    }
}

void executeHIDCommand(const HIDCommand& cmd) {
    if (!usbReady) {
        debugPrint("USB not ready, skipping command");
        return;
    }

    switch (cmd.type) {
        case CMD_MOUSE_MOVE:
            Mouse.move(cmd.x, cmd.y);
            break;

        case CMD_CLICK_DOWN:
            Mouse.press(cmd.buttons);
            break;

        case CMD_CLICK_UP:
            Mouse.release(cmd.buttons);
            break;

        default:
            debugPrint("Unknown HID command type: " + String(cmd.type));
            break;
    }
}

// ===== COMMAND HANDLERS =====
void handlePing(bool isJSON) {
    if (isJSON) {
        StaticJsonDocument<256> responseData;
        responseData["uptime"] = getUptime();
        responseData["hardware"] = HARDWARE_VERSION;
        sendJSONResponse("pong", "ESP32-S2 Octane HID Mouse", responseData.as<JsonObject>());
    } else {
        sendSimpleResponse("PONG ESP32-S2 Octane HID Mouse v" + String(FIRMWARE_VERSION));
    }
}

void handleStatus(bool isJSON) {
    if (isJSON) {
        StaticJsonDocument<512> responseData;
        responseData["uptime"] = getUptime();
        responseData["queue_size"] = uxQueueMessagesWaiting(commandQueue);
        responseData["free_memory"] = getFreeHeap();
        responseData["usb_ready"] = usbReady;
        responseData["commands_processed"] = commandsProcessed;
        responseData["queue_overflows"] = queueOverflows;
        responseData["firmware_version"] = FIRMWARE_VERSION;
        responseData["hardware_version"] = HARDWARE_VERSION;

        sendJSONResponse("connected", "System status", responseData.as<JsonObject>());
    } else {
        String status = "STATUS:OK,UPTIME:" + String(getUptime()) +
                       ",QUEUE:" + String(uxQueueMessagesWaiting(commandQueue)) +
                       ",USB:" + (usbReady ? "READY" : "NOT_READY") +
                       ",CONN:" + (serialReady ? "YES" : "NO") +
                       ",VER:" + String(FIRMWARE_VERSION);
        sendSimpleResponse(status);
    }
}

void handleMouseMove(int x, int y, bool isJSON) {
    // Clamp values to valid range
    int8_t clampedX = constrain(x, -127, 127);
    int8_t clampedY = constrain(y, -127, 127);

    HIDCommand cmd = {
        CMD_MOUSE_MOVE,
        clampedX,
        clampedY,
        0,
        millis()
    };

    if (queueCommand(cmd)) {
        if (isJSON) {
            sendJSONResponse("moved", "Mouse movement queued");
        } else {
            sendSimpleResponse("MOVED");
        }
    } else {
        if (isJSON) {
            sendError("Queue full", 507);
        } else {
            sendSimpleResponse("ERROR:Queue full");
        }
    }
}

void handleMouseClick(const String& button, const String& action, bool isJSON) {
    uint8_t buttonMask = 0;

    // Determine button mask
    if (button == "left") {
        buttonMask = MOUSE_LEFT;
    } else if (button == "right") {
        buttonMask = MOUSE_RIGHT;
    } else if (button == "middle") {
        buttonMask = MOUSE_MIDDLE;
    } else {
        if (isJSON) {
            sendError("Invalid button: " + button);
        } else {
            sendSimpleResponse("ERROR:Invalid button");
        }
        return;
    }

    // Determine command type
    CommandType cmdType;
    if (action == "down") {
        cmdType = CMD_CLICK_DOWN;
    } else if (action == "up") {
        cmdType = CMD_CLICK_UP;
    } else {
        if (isJSON) {
            sendError("Invalid action: " + action);
        } else {
            sendSimpleResponse("ERROR:Invalid action");
        }
        return;
    }

    HIDCommand cmd = {
        cmdType,
        0,
        0,
        buttonMask,
        millis()
    };

    if (queueCommand(cmd)) {
        if (isJSON) {
            sendJSONResponse("clicked", "Mouse " + action + " " + button);
        } else {
            if (action == "down") {
                sendSimpleResponse("CLICKED");
            } else {
                sendSimpleResponse("RELEASED");
            }
        }
    } else {
        if (isJSON) {
            sendError("Queue full", 507);
        } else {
            sendSimpleResponse("ERROR:Queue full");
        }
    }
}

// ===== LED CONTROL FUNCTIONS =====
void updateLEDStatus() {
    static uint32_t lastUpdate = 0;
    static bool ledOn = false;
    static LEDState lastState = LED_BOOT;
    uint32_t currentTime = millis();

    // Debug state changes
    if (currentLEDState != lastState) {
        debugPrint("LED state changed from " + String(lastState) + " to " + String(currentLEDState));
        lastState = currentLEDState;
    }

    switch (currentLEDState) {
        case LED_BOOT:
            // Handled in bootSequence()
            break;

        case LED_WAITING:
            // Blink every 500ms (active-low: LOW = ON, HIGH = OFF)
            if (currentTime - lastUpdate >= 500) {
                ledOn = !ledOn;
                digitalWrite(LED_PIN, ledOn ? LOW : HIGH); // Inverted for active-low
                debugPrint("LED WAITING - GPIO" + String(LED_PIN) + " set to " + (ledOn ? "LOW (ON)" : "HIGH (OFF)"));
                lastUpdate = currentTime;
            }
            break;

        case LED_CONNECTED:
            // Keep LED OFF (HIGH = OFF on active-low LED)
            digitalWrite(LED_PIN, HIGH);
            break;

        case LED_ACTIVE:
            // Keep LED OFF (HIGH = OFF on active-low LED)
            digitalWrite(LED_PIN, HIGH);
            if (currentTime - lastUpdate >= 50) {
                setLEDState(LED_CONNECTED);
            }
            break;

        case LED_ERROR:
            // Flash rapidly every 200ms (active-low: LOW = ON, HIGH = OFF)
            if (currentTime - lastUpdate >= 200) {
                ledOn = !ledOn;
                digitalWrite(LED_PIN, ledOn ? LOW : HIGH); // Inverted for active-low
                debugPrint("LED ERROR - GPIO" + String(LED_PIN) + " set to " + (ledOn ? "LOW (ON)" : "HIGH (OFF)"));
                lastUpdate = currentTime;
            }
            break;
    }
}

void setLEDState(LEDState state) {
    if (currentLEDState != state) {
        String stateName;
        switch (state) {
            case LED_BOOT: stateName = "BOOT"; break;
            case LED_WAITING: stateName = "WAITING"; break;
            case LED_CONNECTED: stateName = "CONNECTED"; break;
            case LED_ACTIVE: stateName = "ACTIVE"; break;
            case LED_ERROR: stateName = "ERROR"; break;
            default: stateName = "UNKNOWN"; break;
        }

        currentLEDState = state;
        lastLEDUpdate = millis();
        debugPrint("LED state changed to: " + stateName + " (" + String(state) + ") on GPIO" + String(LED_PIN));
        Serial.println("LED State: " + stateName + " on GPIO" + String(LED_PIN));
    }
}

// ===== RESPONSE FUNCTIONS =====
void sendJSONResponse(const String& status, const String& message, JsonObject data) {
    StaticJsonDocument<512> response;
    response["status"] = status;

    if (!message.isEmpty()) {
        response["message"] = message;
    }

    if (!data.isNull()) {
        response["data"] = data;
    }

    String responseStr;
    serializeJson(response, responseStr);
    Serial.println(responseStr);
}

void sendSimpleResponse(const String& response) {
    Serial.println(response);
}

void sendError(const String& message, int code) {
    StaticJsonDocument<256> response;
    response["status"] = "error";
    response["message"] = message;
    response["code"] = code;

    String responseStr;
    serializeJson(response, responseStr);
    Serial.println(responseStr);

    debugPrint("Error: " + message + " (Code: " + String(code) + ")");
}

// ===== UTILITY FUNCTIONS =====
void debugPrint(const String& message) {
    #if CORE_DEBUG_LEVEL > 0
    Serial.println("[DEBUG] " + message);
    #endif
}

uint32_t getUptime() {
    return (millis() - bootTime) / 1000;
}

size_t getFreeHeap() {
    return ESP.getFreeHeap();
}

void printSystemInfo() {
    Serial.println("========================================");
    Serial.println("ESP32-S2 HID Mouse Firmware");
    Serial.println("========================================");
    Serial.println("Version: " + String(FIRMWARE_VERSION));
    Serial.println("Hardware: " + String(HARDWARE_VERSION));
    Serial.println("Build Date: " + String(__DATE__) + " " + String(__TIME__));
    Serial.println("CPU Frequency: " + String(getCpuFrequencyMhz()) + " MHz");
    Serial.println("Flash Size: " + String(ESP.getFlashChipSize() / 1024 / 1024) + " MB");
    Serial.println("Free Heap: " + String(getFreeHeap()) + " bytes");
    Serial.println("PSRAM Size: " + String(ESP.getPsramSize() / 1024) + " KB");
    Serial.println("Free PSRAM: " + String(ESP.getFreePsram() / 1024) + " KB");
    Serial.println("Queue Size: " + String(QUEUE_SIZE) + " commands");
    Serial.println("Serial Baud: " + String(SERIAL_BAUD));
    Serial.println("LED Pin: GPIO" + String(LED_PIN));
    Serial.println("========================================");
}