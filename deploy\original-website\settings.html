<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-cog"></i> Settings</h1>
                <p>Configure system settings, security preferences, and application behavior</p>
            </div>

            <div class="dashboard-grid">
                <!-- General Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-sliders-h"></i> General Settings</h3>
                    </div>
                    <div class="card-content">
                        <form id="general-settings-form">
                            <div class="form-group">
                                <label for="system-name">System Name</label>
                                <input type="text" id="system-name" value="Octane Recoil Scripts">
                            </div>
                            <div class="form-group">
                                <label for="admin-email">Admin Email</label>
                                <input type="email" id="admin-email" value="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="timezone">Timezone</label>
                                <select id="timezone">
                                    <option value="UTC">UTC</option>
                                    <option value="America/New_York">Eastern Time</option>
                                    <option value="America/Chicago">Central Time</option>
                                    <option value="America/Denver">Mountain Time</option>
                                    <option value="America/Los_Angeles">Pacific Time</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="maintenance-mode">
                                    <span class="checkmark"></span>
                                    Enable Maintenance Mode
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save General Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-shield-alt"></i> Security Settings</h3>
                    </div>
                    <div class="card-content">
                        <form id="security-settings-form">
                            <div class="form-group">
                                <label for="max-login-attempts">Max Login Attempts</label>
                                <input type="number" id="max-login-attempts" value="5" min="1" max="10">
                            </div>
                            <div class="form-group">
                                <label for="session-timeout">Session Timeout (minutes)</label>
                                <input type="number" id="session-timeout" value="60" min="5" max="480">
                            </div>
                            <div class="form-group">
                                <label for="rate-limit-window">Rate Limit Window (minutes)</label>
                                <input type="number" id="rate-limit-window" value="15" min="1" max="60">
                            </div>
                            <div class="form-group">
                                <label for="rate-limit-max">Rate Limit Max Requests</label>
                                <input type="number" id="rate-limit-max" value="100" min="10" max="1000">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="enable-2fa">
                                    <span class="checkmark"></span>
                                    Enable Two-Factor Authentication
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="log-security-events" checked>
                                    <span class="checkmark"></span>
                                    Log Security Events
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Security Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- License Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-key"></i> License Settings</h3>
                    </div>
                    <div class="card-content">
                        <form id="license-settings-form">
                            <div class="form-group">
                                <label for="default-duration">Default License Duration</label>
                                <select id="default-duration">
                                    <option value="1day">1 Day</option>
                                    <option value="1week">1 Week</option>
                                    <option value="1month" selected>1 Month</option>
                                    <option value="3months">3 Months</option>
                                    <option value="6months">6 Months</option>
                                    <option value="1year">1 Year</option>
                                    <option value="lifetime">Lifetime</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="max-hwid-resets">Max HWID Resets per License</label>
                                <input type="number" id="max-hwid-resets" value="3" min="0" max="10">
                            </div>
                            <div class="form-group">
                                <label for="license-key-format">License Key Format</label>
                                <select id="license-key-format">
                                    <option value="XXX.XXX.XXX" selected>XXX.XXX.XXX</option>
                                    <option value="XXXXXXXX">XXXXXXXX</option>
                                    <option value="XXXX-XXXX-XXXX">XXXX-XXXX-XXXX</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="auto-expire-licenses" checked>
                                    <span class="checkmark"></span>
                                    Auto-expire Licenses
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="notify-expiring-licenses" checked>
                                    <span class="checkmark"></span>
                                    Notify Expiring Licenses
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save License Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Discord Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fab fa-discord"></i> Discord Settings</h3>
                    </div>
                    <div class="card-content">
                        <form id="discord-settings-form">
                            <div class="form-group">
                                <label for="discord-token">Bot Token</label>
                                <input type="password" id="discord-token" placeholder="Enter Discord bot token">
                            </div>
                            <div class="form-group">
                                <label for="discord-server-id">Server ID</label>
                                <input type="text" id="discord-server-id" placeholder="Discord server ID">
                            </div>
                            <div class="form-group">
                                <label for="discord-channel-id">Channel ID</label>
                                <input type="text" id="discord-channel-id" placeholder="Discord channel ID">
                            </div>
                            <div class="form-group">
                                <label for="discord-admin-id">Admin User ID</label>
                                <input type="text" id="discord-admin-id" placeholder="Discord admin user ID">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="discord-notifications" checked>
                                    <span class="checkmark"></span>
                                    Enable Discord Notifications
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="discord-security-alerts" checked>
                                    <span class="checkmark"></span>
                                    Send Security Alerts to Discord
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Discord Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Database Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-database"></i> Database Settings</h3>
                    </div>
                    <div class="card-content">
                        <form id="database-settings-form">
                            <div class="form-group">
                                <label for="backup-interval">Backup Interval (hours)</label>
                                <input type="number" id="backup-interval" value="24" min="1" max="168">
                            </div>
                            <div class="form-group">
                                <label for="max-backups">Max Backup Files</label>
                                <input type="number" id="max-backups" value="7" min="1" max="30">
                            </div>
                            <div class="form-group">
                                <label for="cleanup-interval">Cleanup Interval (days)</label>
                                <input type="number" id="cleanup-interval" value="30" min="1" max="365">
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="auto-backup" checked>
                                    <span class="checkmark"></span>
                                    Enable Auto Backup
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="auto-cleanup" checked>
                                    <span class="checkmark"></span>
                                    Enable Auto Cleanup
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Database Settings
                            </button>
                        </form>
                    </div>
                </div>

                <!-- System Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-tools"></i> System Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="action-buttons">
                            <button class="btn btn-info" onclick="backupDatabase()">
                                <i class="fas fa-download"></i>
                                Backup Database
                            </button>
                            <button class="btn btn-warning" onclick="restartServices()">
                                <i class="fas fa-redo"></i>
                                Restart Services
                            </button>
                            <button class="btn btn-secondary" onclick="clearCache()">
                                <i class="fas fa-broom"></i>
                                Clear Cache
                            </button>
                            <button class="btn btn-success" onclick="exportSettings()">
                                <i class="fas fa-file-export"></i>
                                Export Settings
                            </button>
                            <button class="btn btn-primary" onclick="importSettings()">
                                <i class="fas fa-file-import"></i>
                                Import Settings
                            </button>
                            <button class="btn btn-danger" onclick="resetToDefaults()">
                                <i class="fas fa-exclamation-triangle"></i>
                                Reset to Defaults
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script>
        // Settings specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
            loadCurrentSettings();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Form submissions
            document.getElementById('general-settings-form').addEventListener('submit', saveGeneralSettings);
            document.getElementById('security-settings-form').addEventListener('submit', saveSecuritySettings);
            document.getElementById('license-settings-form').addEventListener('submit', saveLicenseSettings);
            document.getElementById('discord-settings-form').addEventListener('submit', saveDiscordSettings);
            document.getElementById('database-settings-form').addEventListener('submit', saveDatabaseSettings);
        }

        async function loadCurrentSettings() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/settings');
                if (response.success) {
                    populateSettings(response.settings);
                }
            } catch (error) {
                console.error('Failed to load settings:', error);
                AdminUtils.showNotification('Failed to load current settings', 'error');
            }
        }

        function populateSettings(settings) {
            // General settings
            if (settings.general) {
                document.getElementById('system-name').value = settings.general.systemName || 'Octane Recoil Scripts';
                document.getElementById('admin-email').value = settings.general.adminEmail || '<EMAIL>';
                document.getElementById('timezone').value = settings.general.timezone || 'UTC';
                document.getElementById('maintenance-mode').checked = settings.general.maintenanceMode || false;
            }

            // Security settings
            if (settings.security) {
                document.getElementById('max-login-attempts').value = settings.security.maxLoginAttempts || 5;
                document.getElementById('session-timeout').value = settings.security.sessionTimeout || 60;
                document.getElementById('rate-limit-window').value = settings.security.rateLimitWindow || 15;
                document.getElementById('rate-limit-max').value = settings.security.rateLimitMax || 100;
                document.getElementById('enable-2fa').checked = settings.security.enable2FA || false;
                document.getElementById('log-security-events').checked = settings.security.logSecurityEvents !== false;
            }

            // License settings
            if (settings.license) {
                document.getElementById('default-duration').value = settings.license.defaultDuration || '1month';
                document.getElementById('max-hwid-resets').value = settings.license.maxHwidResets || 3;
                document.getElementById('license-key-format').value = settings.license.keyFormat || 'XXX.XXX.XXX';
                document.getElementById('auto-expire-licenses').checked = settings.license.autoExpire !== false;
                document.getElementById('notify-expiring-licenses').checked = settings.license.notifyExpiring !== false;
            }

            // Discord settings
            if (settings.discord) {
                document.getElementById('discord-token').value = settings.discord.token || '';
                document.getElementById('discord-server-id').value = settings.discord.serverId || '';
                document.getElementById('discord-channel-id').value = settings.discord.channelId || '';
                document.getElementById('discord-admin-id').value = settings.discord.adminId || '';
                document.getElementById('discord-notifications').checked = settings.discord.notifications !== false;
                document.getElementById('discord-security-alerts').checked = settings.discord.securityAlerts !== false;
            }

            // Database settings
            if (settings.database) {
                document.getElementById('backup-interval').value = settings.database.backupInterval || 24;
                document.getElementById('max-backups').value = settings.database.maxBackups || 7;
                document.getElementById('cleanup-interval').value = settings.database.cleanupInterval || 30;
                document.getElementById('auto-backup').checked = settings.database.autoBackup !== false;
                document.getElementById('auto-cleanup').checked = settings.database.autoCleanup !== false;
            }
        }

        async function saveGeneralSettings(event) {
            event.preventDefault();
            
            const settings = {
                systemName: document.getElementById('system-name').value,
                adminEmail: document.getElementById('admin-email').value,
                timezone: document.getElementById('timezone').value,
                maintenanceMode: document.getElementById('maintenance-mode').checked
            };

            await saveSettings('general', settings);
        }

        async function saveSecuritySettings(event) {
            event.preventDefault();
            
            const settings = {
                maxLoginAttempts: parseInt(document.getElementById('max-login-attempts').value),
                sessionTimeout: parseInt(document.getElementById('session-timeout').value),
                rateLimitWindow: parseInt(document.getElementById('rate-limit-window').value),
                rateLimitMax: parseInt(document.getElementById('rate-limit-max').value),
                enable2FA: document.getElementById('enable-2fa').checked,
                logSecurityEvents: document.getElementById('log-security-events').checked
            };

            await saveSettings('security', settings);
        }

        async function saveLicenseSettings(event) {
            event.preventDefault();
            
            const settings = {
                defaultDuration: document.getElementById('default-duration').value,
                maxHwidResets: parseInt(document.getElementById('max-hwid-resets').value),
                keyFormat: document.getElementById('license-key-format').value,
                autoExpire: document.getElementById('auto-expire-licenses').checked,
                notifyExpiring: document.getElementById('notify-expiring-licenses').checked
            };

            await saveSettings('license', settings);
        }

        async function saveDiscordSettings(event) {
            event.preventDefault();
            
            const settings = {
                token: document.getElementById('discord-token').value,
                serverId: document.getElementById('discord-server-id').value,
                channelId: document.getElementById('discord-channel-id').value,
                adminId: document.getElementById('discord-admin-id').value,
                notifications: document.getElementById('discord-notifications').checked,
                securityAlerts: document.getElementById('discord-security-alerts').checked
            };

            await saveSettings('discord', settings);
        }

        async function saveDatabaseSettings(event) {
            event.preventDefault();
            
            const settings = {
                backupInterval: parseInt(document.getElementById('backup-interval').value),
                maxBackups: parseInt(document.getElementById('max-backups').value),
                cleanupInterval: parseInt(document.getElementById('cleanup-interval').value),
                autoBackup: document.getElementById('auto-backup').checked,
                autoCleanup: document.getElementById('auto-cleanup').checked
            };

            await saveSettings('database', settings);
        }

        async function saveSettings(category, settings) {
            try {
                const response = await AdminUtils.apiCall('/api/admin/settings', {
                    method: 'POST',
                    body: JSON.stringify({ category, settings })
                });

                if (response.success) {
                    AdminUtils.showNotification(`${category} settings saved successfully`, 'success');
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification(`Failed to save ${category} settings: ` + error.message, 'error');
            }
        }

        // System action functions
        async function backupDatabase() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/backup-database', {
                    method: 'POST'
                });

                if (response.success) {
                    AdminUtils.showNotification('Database backup created successfully', 'success');
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to backup database: ' + error.message, 'error');
            }
        }

        async function restartServices() {
            if (!confirm('Are you sure you want to restart all services? This may cause temporary downtime.')) return;
            
            try {
                const response = await AdminUtils.apiCall('/api/admin/restart-services', {
                    method: 'POST'
                });

                if (response.success) {
                    AdminUtils.showNotification('Services restart initiated', 'success');
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to restart services: ' + error.message, 'error');
            }
        }

        async function clearCache() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/clear-cache', {
                    method: 'POST'
                });

                if (response.success) {
                    AdminUtils.showNotification('Cache cleared successfully', 'success');
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to clear cache: ' + error.message, 'error');
            }
        }

        function exportSettings() {
            window.open('/api/admin/export-settings', '_blank');
            AdminUtils.showNotification('Settings export started', 'info');
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = async function(event) {
                const file = event.target.files[0];
                if (file) {
                    const formData = new FormData();
                    formData.append('settings', file);
                    
                    try {
                        const response = await fetch('/api/admin/import-settings', {
                            method: 'POST',
                            body: formData
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            AdminUtils.showNotification('Settings imported successfully', 'success');
                            loadCurrentSettings();
                        } else {
                            throw new Error(result.message);
                        }
                    } catch (error) {
                        AdminUtils.showNotification('Failed to import settings: ' + error.message, 'error');
                    }
                }
            };
            input.click();
        }

        async function resetToDefaults() {
            if (!confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) return;
            
            try {
                const response = await AdminUtils.apiCall('/api/admin/reset-settings', {
                    method: 'POST'
                });

                if (response.success) {
                    AdminUtils.showNotification('Settings reset to defaults', 'success');
                    loadCurrentSettings();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to reset settings: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
