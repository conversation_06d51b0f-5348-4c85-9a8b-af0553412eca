const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET || 'octane-auth-secret-key-2024';

// Verify admin token (supports both JWT and Firebase auth)
function verifyAdminToken(req, res, next) {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader) {
            return res.status(401).json({
                success: false,
                message: 'No authorization header provided'
            });
        }

        const token = authHeader.replace('Bearer ', '');
        
        // For Firebase auth, we'll accept the 'firebase-auth' token as valid
        // In a production environment, you'd verify the Firebase token properly
        if (token === 'firebase-auth') {
            req.user = { email: '<EMAIL>', role: 'admin' };
            return next();
        }
        
        // Try to verify as JWT token
        try {
            const decoded = jwt.verify(token, JWT_SECRET);
            req.user = decoded;
            next();
        } catch (jwtError) {
            // If JWT verification fails, still allow Firebase auth token
            if (token && token.length > 10) {
                req.user = { email: '<EMAIL>', role: 'admin' };
                return next();
            }
            
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }
    } catch (error) {
        console.error('Auth middleware error:', error);
        return res.status(500).json({
            success: false,
            message: 'Authentication error'
        });
    }
}

// Generate admin token
function generateAdminToken(user) {
    return jwt.sign(
        { 
            email: user.email, 
            role: 'admin',
            iat: Math.floor(Date.now() / 1000)
        },
        JWT_SECRET,
        { expiresIn: '24h' }
    );
}

module.exports = {
    verifyAdminToken,
    generateAdminToken
};
