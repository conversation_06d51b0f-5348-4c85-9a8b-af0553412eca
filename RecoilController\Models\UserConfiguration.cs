using System;
using Newtonsoft.Json;

namespace RecoilController.Models
{
    /// <summary>
    /// User configuration settings that can be saved/loaded
    /// </summary>
    public class UserConfiguration
    {
        [JsonProperty("configName")]
        public string ConfigName { get; set; } = "Default";

        [JsonProperty("weaponId")]
        public int WeaponId { get; set; } = 1;

        [JsonProperty("weaponName")]
        public string WeaponName { get; set; } = "Assault Rifle";

        [JsonProperty("sensitivity")]
        public float Sensitivity { get; set; } = 1.0f;

        [JsonProperty("adsSensitivity")]
        public float AdsSensitivity { get; set; } = 1.0f;

        [JsonProperty("fieldOfView")]
        public float FieldOfView { get; set; } = 100.0f;

        [JsonProperty("scopeType")]
        public ScopeType ScopeType { get; set; } = ScopeType.IronSights;

        [JsonProperty("recoilControlEnabled")]
        public bool RecoilControlEnabled { get; set; } = true;

        [JsonProperty("crouchModifierEnabled")]
        public bool CrouchModifierEnabled { get; set; } = true;

        [JsonProperty("autoLoadOnStartup")]
        public bool AutoLoadOnStartup { get; set; } = false;

        [JsonProperty("createdDate")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [JsonProperty("lastModified")]
        public DateTime LastModified { get; set; } = DateTime.Now;

        public UserConfiguration()
        {
        }

        public UserConfiguration(string configName)
        {
            ConfigName = configName;
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Creates a deep copy of the configuration
        /// </summary>
        public UserConfiguration Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            return JsonConvert.DeserializeObject<UserConfiguration>(json);
        }

        /// <summary>
        /// Updates the last modified timestamp
        /// </summary>
        public void UpdateModified()
        {
            LastModified = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{ConfigName} - {WeaponName} (Sens: {Sensitivity:F2}, FOV: {FieldOfView:F0})";
        }
    }

    /// <summary>
    /// Types of scopes/sights available
    /// </summary>
    public enum ScopeType
    {
        IronSights = 0,
        RedDot = 1,
        Holographic = 2,
        Scope2x = 3,
        Scope4x = 4,
        Scope8x = 5
    }
}
