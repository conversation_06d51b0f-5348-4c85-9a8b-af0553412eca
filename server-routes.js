const { app, db, sanitizeInput, generateLicenseKey, calculateExpiryDate, logSecurityEvent } = require('./server-main');

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
    });
});

// Main validation endpoint
app.post('/api/validate', (req, res) => {
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    
    try {
        let { licenseKey, hardwareId, applicationVersion, timestamp } = req.body;

        console.log(`[${new Date().toISOString()}] POST /api/validate - IP: ${clientIP}`);
        console.log(`Request body:`, JSON.stringify(req.body, null, 2));

        if (!licenseKey || typeof licenseKey !== 'string') {
            logSecurityEvent(clientIP, 'INVALID_REQUEST', 'Missing license key', 'warning');
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Sanitize inputs
        licenseKey = sanitizeInput(licenseKey);
        hardwareId = sanitizeInput(hardwareId || '');

        // Validate hardware ID format (if provided)
        if (hardwareId && hardwareId !== 'discord-check' && !/^[A-Za-z0-9+/]{12,}$/.test(hardwareId)) {
            logSecurityEvent(clientIP, 'INVALID_HWID', `Invalid hardware ID format: ${hardwareId}`, 'warning');
            return res.status(400).json({
                success: false,
                message: 'Invalid hardware ID format'
            });
        }

        console.log(`🔍 Validating license ${licenseKey} for hardware ${hardwareId}`);

        db.get(
            `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
            [licenseKey],
            (err, row) => {
                if (err) {
                    console.error('Database error:', err);
                    logSecurityEvent(clientIP, 'DATABASE_ERROR', err.message, 'error');
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!row) {
                    logSecurityEvent(clientIP, 'INVALID_LICENSE', `License not found: ${licenseKey}`, 'warning');
                    return res.json({
                        success: false,
                        message: 'Invalid license key'
                    });
                }

                // Check if license is expired
                const now = new Date();
                const expiresAt = new Date(row.expires_at);
                
                if (now > expiresAt) {
                    logSecurityEvent(clientIP, 'EXPIRED_LICENSE', `Expired license: ${licenseKey}`, 'info');
                    return res.json({
                        success: false,
                        message: 'License has expired'
                    });
                }

                // Update hardware ID if provided and different
                if (hardwareId && hardwareId !== 'discord-check' && row.hardware_id !== hardwareId) {
                    db.run(
                        `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                        [hardwareId, licenseKey],
                        (updateErr) => {
                            if (updateErr) {
                                console.error('Hardware ID update error:', updateErr);
                            } else {
                                console.log(`🔄 Updated hardware ID for ${licenseKey}`);
                            }
                        }
                    );
                }

                const remainingTime = Math.floor((expiresAt - now) / 1000);

                logSecurityEvent(clientIP, 'VALID_LICENSE', `Valid license: ${licenseKey}`, 'info');

                console.log(`✅ License ${licenseKey} validated successfully`);
                
                res.json({
                    success: true,
                    message: 'License valid',
                    remainingTime: remainingTime,
                    expiresAt: row.expires_at
                });
            }
        );
    } catch (error) {
        console.error('Validation error:', error);
        logSecurityEvent(clientIP, 'VALIDATION_ERROR', error.message, 'error');
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    const limit = parseInt(req.query.limit) || 10;
    db.all(
        `SELECT * FROM licenses ORDER BY created_at DESC LIMIT ?`,
        [Math.min(limit, 50)],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

app.post('/api/admin/create-license', (req, res) => {
    try {
        const { duration, notes } = req.body;

        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);

        db.run(
            `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
            [key, duration, expiresAt, notes || ''],
            function(err) {
                if (err) {
                    console.error('Create license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to create license'
                    });
                }

                console.log(`📝 License created: ${key} (${duration})`);

                res.json({
                    success: true,
                    message: 'License created successfully',
                    license: {
                        id: this.lastID,
                        key: key,
                        duration: duration,
                        expiresAt: expiresAt,
                        notes: notes || ''
                    }
                });
            }
        );
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

app.post('/api/admin/reset-hwid', (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Reset HWID error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to reset hardware ID'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'License key not found'
                    });
                }

                console.log(`🔄 Hardware ID reset for license: ${licenseKey}`);

                res.json({
                    success: true,
                    message: 'Hardware ID reset successfully'
                });
            }
        );
    } catch (error) {
        console.error('Reset HWID error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Admin routes
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

// Root redirect
app.get('/', (req, res) => {
    res.redirect('/admin');
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});
