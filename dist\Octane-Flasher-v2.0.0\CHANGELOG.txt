Octane ESP32-S2 HID Mouse Flasher - Changelog
==============================================

Version 2.0.0 (27/07/2025)
------------------------
- ESP32-S2 HID Mouse firmware v2.0
- Improved USB HID mouse emulation
- Enhanced serial command interface
- Command queue system for smooth movement
- LED status indicators (flashing/steady)
- Error reporting via serial to desktop app
- Better esptool integration
- Enhanced user experience with progress indicators
- Comprehensive firmware validation
- Support for both ESP-IDF and PlatformIO builds

Features:
- USB HID Mouse device emulation
- Serial communication at 921600 baud
- Mouse movement commands (M10,5)
- Click commands (CLICK_LEFT_DOWN/UP)
- Status commands (PING/PONG, STATUS)
- LED GPIO15 status indication
- Automatic COM port detection
- Pre/post-flash verification
- License key authentication
- Discord webhook error reporting

Technical Specifications:
- Target: ESP32-S2 Mini (4MB Flash, 2MB PSRAM)
- Framework: ESP-IDF v4.4+
- USB: TinyUSB HID implementation
- Serial: 921600 baud, 8N1
- LED: GPIO15 status indicator
- Flash Layout: 0x1000 bootloader, 0x8000 partitions, 0x10000 firmware

