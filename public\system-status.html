<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - System Status</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>

            <!-- System Overview -->
            <div class="card">
                <div class="card-header">
                    <h3>🖥️ System Overview</h3>
                    <button class="btn btn-secondary" onclick="refreshSystemData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="systemUptime">Loading...</div>
                            <div style="color: var(--text-secondary);">System Uptime</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--warning-color);" id="memoryUsage">Loading...</div>
                            <div style="color: var(--text-secondary);">Memory Usage</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--info-color);" id="cpuUsage">Loading...</div>
                            <div style="color: var(--text-secondary);">CPU Usage</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color);" id="diskUsage">Loading...</div>
                            <div style="color: var(--text-secondary);">Disk Usage</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Status -->
            <div class="card">
                <div class="card-header">
                    <h3>🔄 Services Status</h3>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Status</th>
                                    <th>Uptime</th>
                                    <th>Memory</th>
                                    <th>CPU</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="servicesTable">
                                <tr>
                                    <td colspan="6" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading services...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- System Logs -->
            <div class="card">
                <div class="card-header">
                    <h3>📋 Recent System Logs</h3>
                    <div style="display: flex; gap: 10px;">
                        <select id="logLevel" class="form-control" style="max-width: 150px;">
                            <option value="all">All Levels</option>
                            <option value="error">Errors</option>
                            <option value="warning">Warnings</option>
                            <option value="info">Info</option>
                        </select>
                        <button class="btn btn-secondary" onclick="refreshLogs()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div id="systemLogs" style="max-height: 400px; overflow-y: auto; background: var(--bg-dark); padding: 15px; border-radius: var(--border-radius); font-family: monospace;">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading logs...
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Actions -->
            <div class="card">
                <div class="card-header">
                    <h3>⚡ System Actions</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div>
                            <h4>🔄 Service Management</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-success" onclick="restartService('octane-auth')">
                                    <i class="fas fa-redo"></i> Restart Auth Server
                                </button>
                                <button class="btn btn-success" onclick="restartService('discord-bot')">
                                    <i class="fas fa-redo"></i> Restart Discord Bot
                                </button>
                                <button class="btn btn-warning" onclick="restartSystem()">
                                    <i class="fas fa-power-off"></i> Restart System
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>🧹 Maintenance</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-secondary" onclick="clearLogs()">
                                    <i class="fas fa-trash"></i> Clear Old Logs
                                </button>
                                <button class="btn btn-secondary" onclick="cleanupDatabase()">
                                    <i class="fas fa-database"></i> Cleanup Database
                                </button>
                                <button class="btn btn-info" onclick="backupSystem()">
                                    <i class="fas fa-save"></i> Create Backup
                                </button>
                            </div>
                        </div>
                        <div>
                            <h4>📊 Monitoring</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <button class="btn btn-info" onclick="exportSystemReport()">
                                    <i class="fas fa-download"></i> Export Report
                                </button>
                                <button class="btn btn-secondary" onclick="viewDetailedStats()">
                                    <i class="fas fa-chart-line"></i> Detailed Stats
                                </button>
                                <button class="btn btn-primary" onclick="runHealthCheck()">
                                    <i class="fas fa-heartbeat"></i> Health Check
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                loadSystemData();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // System monitoring functions
        function loadSystemData() {
            loadSystemStats();
            loadServices();
            loadSystemLogs();
        }

        function loadSystemStats() {
            fetch('/api/system/stats')
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        document.getElementById('systemUptime').textContent = result.uptime || 'Unknown';
                        document.getElementById('memoryUsage').textContent = result.memory || 'Unknown';
                        document.getElementById('cpuUsage').textContent = result.cpu || 'Unknown';
                        document.getElementById('diskUsage').textContent = result.disk || 'Unknown';
                    }
                })
                .catch(error => {
                    console.error('Error loading system stats:', error);
                    document.getElementById('systemUptime').textContent = 'Error';
                    document.getElementById('memoryUsage').textContent = 'Error';
                    document.getElementById('cpuUsage').textContent = 'Error';
                    document.getElementById('diskUsage').textContent = 'Error';
                });
        }

        function loadServices() {
            fetch('/api/system/services')
                .then(response => response.json())
                .then(result => {
                    const tbody = document.getElementById('servicesTable');

                    if (result.success && result.services.length > 0) {
                        tbody.innerHTML = result.services.map(service => `
                            <tr>
                                <td><strong>${service.name}</strong></td>
                                <td><span class="status-badge ${service.status === 'running' ? 'active' : 'inactive'}">${service.status}</span></td>
                                <td>${service.uptime}</td>
                                <td>${service.memory}</td>
                                <td>${service.cpu}</td>
                                <td>
                                    <div style="display: flex; gap: 5px;">
                                        <button class="btn btn-sm btn-success" onclick="restartService('${service.name}')" title="Restart">
                                            🔄
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="viewServiceLogs('${service.name}')" title="View Logs">
                                            📋
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--text-muted);">No services found</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading services:', error);
                    document.getElementById('servicesTable').innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading services</td></tr>';
                });
        }

        function loadSystemLogs() {
            fetch('/api/system/logs')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('systemLogs');

                    if (result.success && result.logs.length > 0) {
                        container.innerHTML = result.logs.map(log => `
                            <div style="margin-bottom: 5px; color: ${getLogColor(log.level)};">
                                <span style="color: var(--text-muted);">[${log.timestamp}]</span>
                                <span style="color: ${getLogColor(log.level)}; font-weight: bold;">[${log.level.toUpperCase()}]</span>
                                ${log.message}
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="color: var(--text-muted);">No recent logs</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading logs:', error);
                    document.getElementById('systemLogs').innerHTML = '<div style="color: var(--danger-color);">Error loading logs</div>';
                });
        }

        function getLogColor(level) {
            switch(level.toLowerCase()) {
                case 'error': return 'var(--danger-color)';
                case 'warning': return 'var(--warning-color)';
                case 'info': return 'var(--info-color)';
                default: return 'var(--text-primary)';
            }
        }

        function refreshSystemData() {
            loadSystemData();
            OctaneUtils.showAlert('🔄 System data refreshed', 'info');
        }

        function refreshLogs() {
            loadSystemLogs();
            OctaneUtils.showAlert('📋 Logs refreshed', 'info');
        }

        function restartService(serviceName) {
            if (!confirm(`Restart ${serviceName} service?`)) return;

            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            btn.disabled = true;

            fetch('/api/system/restart-service', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ service: serviceName })
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    OctaneUtils.showAlert(`✅ ${serviceName} restarted successfully`, 'success');
                    setTimeout(loadServices, 2000);
                } else {
                    OctaneUtils.showAlert(`❌ Failed to restart ${serviceName}: ` + result.error, 'danger');
                }
            })
            .catch(error => {
                OctaneUtils.showAlert(`❌ Error restarting ${serviceName}: ` + error.message, 'danger');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }

        function restartSystem() {
            if (!confirm('Restart the entire system? This will cause temporary downtime.')) return;
            OctaneUtils.showAlert('🔄 System restart feature coming soon...', 'info');
        }

        function clearLogs() {
            if (!confirm('Clear old system logs?')) return;
            OctaneUtils.showAlert('🧹 Clear logs feature coming soon...', 'info');
        }

        function cleanupDatabase() {
            if (!confirm('Cleanup database? This will remove old entries.')) return;
            OctaneUtils.showAlert('🗄️ Database cleanup feature coming soon...', 'info');
        }

        function backupSystem() {
            OctaneUtils.showAlert('💾 Creating system backup...', 'info');
            // Mock backup process
            setTimeout(() => {
                OctaneUtils.showAlert('✅ System backup created successfully', 'success');
            }, 3000);
        }

        function exportSystemReport() {
            OctaneUtils.showAlert('📊 Exporting system report...', 'info');
            // Mock export process
            setTimeout(() => {
                OctaneUtils.showAlert('✅ System report exported', 'success');
            }, 2000);
        }

        function viewDetailedStats() {
            OctaneUtils.showAlert('📈 Detailed stats feature coming soon...', 'info');
        }

        function runHealthCheck() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            btn.disabled = true;

            fetch('/api/system/health-check', { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ System health check passed', 'success');
                    } else {
                        OctaneUtils.showAlert('⚠️ Health check issues detected: ' + result.issues.join(', '), 'warning');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Health check failed: ' + error.message, 'danger');
                })
                .finally(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                });
        }

        function viewServiceLogs(serviceName) {
            OctaneUtils.showAlert(`📋 Viewing logs for ${serviceName}...`, 'info');
        }
    </script>
</body>
</html>
