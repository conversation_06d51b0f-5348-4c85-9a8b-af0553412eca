#!/bin/bash

# Octane Auth Backend Deployment Script
# This script deploys the backend to the VPS

set -e  # Exit on any error

# Configuration
VPS_HOST="your-vps-ip"
VPS_USER="root"
VPS_PATH="/opt/octane-auth"
LOCAL_PATH="."

echo "🚀 Starting deployment to VPS..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we have the VPS connection details
if [ -z "$VPS_HOST" ] || [ "$VPS_HOST" = "your-vps-ip" ]; then
    print_error "Please configure VPS_HOST in the script"
    exit 1
fi

# Create deployment package
print_status "Creating deployment package..."
TEMP_DIR=$(mktemp -d)
PACKAGE_NAME="octane-auth-$(date +%Y%m%d-%H%M%S).tar.gz"

# Copy files to temp directory
cp -r . "$TEMP_DIR/octane-auth"
cd "$TEMP_DIR"

# Remove unnecessary files
rm -rf octane-auth/.git
rm -rf octane-auth/node_modules
rm -rf octane-auth/.env
rm -rf octane-auth/database.db
rm -rf octane-auth/logs
rm -f octane-auth/deploy.sh

# Create package
tar -czf "$PACKAGE_NAME" octane-auth/
print_success "Package created: $PACKAGE_NAME"

# Upload to VPS
print_status "Uploading to VPS..."
scp "$PACKAGE_NAME" "$VPS_USER@$VPS_HOST:/tmp/"

# Deploy on VPS
print_status "Deploying on VPS..."
ssh "$VPS_USER@$VPS_HOST" << EOF
    set -e
    
    echo "📦 Extracting package..."
    cd /tmp
    tar -xzf "$PACKAGE_NAME"
    
    echo "🛑 Stopping services..."
    pm2 stop octane-auth || true
    pm2 stop discord-bot || true
    
    echo "📁 Backing up current installation..."
    if [ -d "$VPS_PATH" ]; then
        mv "$VPS_PATH" "$VPS_PATH.backup.$(date +%Y%m%d-%H%M%S)"
    fi
    
    echo "📋 Installing new version..."
    mv octane-auth "$VPS_PATH"
    cd "$VPS_PATH"
    
    echo "📦 Installing dependencies..."
    npm install --production
    
    echo "🔧 Setting up environment..."
    # Copy environment file if it exists in backup
    if [ -f "$VPS_PATH.backup."*"/.env" ]; then
        cp "$VPS_PATH.backup."*"/.env" .env
    else
        echo "⚠️  No .env file found in backup, creating default..."
        cat > .env << 'ENVEOF'
NODE_ENV=production
PORT=3000
DISCORD_TOKEN=your_discord_token_here
DATABASE_PATH=./database.db
ENVEOF
    fi
    
    echo "🗄️  Setting up database..."
    # Copy database if it exists in backup
    if [ -f "$VPS_PATH.backup."*"/database.db" ]; then
        cp "$VPS_PATH.backup."*"/database.db" database.db
        echo "✅ Database restored from backup"
    else
        echo "⚠️  No database found in backup, will create new one"
    fi
    
    echo "📁 Creating directories..."
    mkdir -p logs
    mkdir -p .pm2/logs
    
    echo "🔐 Setting permissions..."
    chown -R root:root "$VPS_PATH"
    chmod +x "$VPS_PATH"/*.js
    
    echo "🚀 Starting services..."
    pm2 start ecosystem.config.js
    pm2 save
    
    echo "🧹 Cleaning up..."
    rm -f "/tmp/$PACKAGE_NAME"
    
    echo "✅ Deployment completed successfully!"
    
    echo "📊 Service status:"
    pm2 status
    
    echo "🌐 Testing endpoints..."
    sleep 5
    curl -f http://localhost:3000/health || echo "⚠️  Health check failed"
    
EOF

# Cleanup local temp files
cd - > /dev/null
rm -rf "$TEMP_DIR"

print_success "Deployment completed!"
print_status "You can now access the admin panel at: http://$VPS_HOST/admin"
print_status "Login page: http://$VPS_HOST/login"

# Show final status
print_status "Checking final status..."
ssh "$VPS_USER@$VPS_HOST" "pm2 status && df -h && free -h"

print_success "🎉 Deployment successful! 🎉"
