using System;

namespace RecoilController.Views.Components
{
    public static class MainWindowStyles
    {
        public static string GetCssContent()
        {
            return @"
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0f1419 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .title-bar {
            background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 100%);
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            -webkit-app-region: drag;
            border-bottom: 2px solid;
            border-image: linear-gradient(90deg, #00d4ff 0%, #00ff88 100%) 1;
        }

        .title-text {
            font-size: 14px;
            font-weight: 600;
            background: linear-gradient(90deg, #00d4ff 0%, #00ff88 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }



        .main-content {
            flex: 1;
            padding: 0;
        }

        .tab-nav {
            display: flex;
            background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 100%);
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px 10px 0 0;
            margin-bottom: 20px;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            color: rgba(255,255,255,0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            border-right: 1px solid rgba(255,255,255,0.1);
        }

        .tab-btn:last-child {
            border-right: none;
        }

        .tab-btn:hover {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
            color: #ffffff;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            color: #000000;
            font-weight: 600;
        }

        .content-area {
            flex: 1;
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(42, 42, 42, 0.8) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 0 0 10px 10px;
            padding: 20px;
            overflow-y: auto;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .section-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .section-card {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(42, 42, 42, 0.8) 100%);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
        }

        .section-card h3 {
            color: #00ff88;
            margin-bottom: 20px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-row {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-row label {
            color: rgba(255,255,255,0.9);
            font-size: 13px;
            font-weight: 500;
        }

        .form-row select,
        .form-row input[type=text] {
            padding: 10px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.5) 0%, rgba(26, 26, 26, 0.5) 100%);
            border: 1px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
        }

        .form-row select:focus,
        .form-row input[type=text]:focus {
            outline: none;
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .form-row input[type=range] {
            -webkit-appearance: none;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.3) 0%, rgba(0, 255, 136, 0.3) 100%);
            border-radius: 3px;
            outline: none;
        }

        .form-row input[type=range]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: linear-gradient(135deg, #00d4ff 0%, #00ff88 100%);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.5);
        }

        .form-row input[type=checkbox] {
            width: 18px;
            height: 18px;
            accent-color: #00ff88;
        }

        .status-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .status-btn.active {
            background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
            color: #000000;
        }

        .status-btn:not(.active) {
            background: linear-gradient(135deg, rgba(255, 0, 0, 0.7) 0%, rgba(200, 0, 0, 0.7) 100%);
            color: #ffffff;
        }

        .simple-content {
            text-align: center;
            padding: 40px;
            color: rgba(255,255,255,0.7);
        }

        .simple-content h3 {
            color: #00ff88;
            margin-bottom: 20px;
        }

        /* Keybind capture styles */
        .form-row input[readonly] {
            cursor: pointer;
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
            border: 2px solid rgba(0, 212, 255, 0.3);
        }

        .form-row input[readonly]:hover {
            border-color: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
        }

        .form-row input.capturing {
            border-color: #00d4ff;
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 15px rgba(0, 212, 255, 0.5); }
            50% { box-shadow: 0 0 25px rgba(0, 212, 255, 0.8); }
            100% { box-shadow: 0 0 15px rgba(0, 212, 255, 0.5); }
        }

        /* Loadout list styles */
        .loadout-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(26, 26, 26, 0.3) 100%);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .loadout-item:hover {
            border-color: #00ff88;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
        }

        .loadout-name {
            color: #ffffff;
            font-weight: 500;
            flex: 1;
        }

        .loadout-key {
            color: #00d4ff;
            font-family: monospace;
            font-weight: 600;
            background: rgba(0, 212, 255, 0.1);
            padding: 2px 8px;
            border-radius: 4px;
            margin: 0 10px;
        }

        .loadout-delete {
            background: rgba(255, 0, 0, 0.2);
            border: none;
            color: #ff4757;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .loadout-delete:hover {
            background: rgba(255, 0, 0, 0.4);
            color: #ffffff;
        }";
        }
    }
}
