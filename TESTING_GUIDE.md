# Octane ESP32 HID Mouse Testing Guide

This guide provides comprehensive testing procedures for the Octane ESP32 HID Mouse firmware and flasher system.

## 🧪 Automated Testing

### Build System Testing
```bash
# Test all build systems and compilation
test_build.bat
```

**What it tests:**
- ESP-IDF availability and configuration
- PlatformIO availability and configuration
- Source file validation
- Firmware compilation (both ESP-IDF and PlatformIO)
- C# flasher compilation
- Output file validation

### Flasher Application Testing
```bash
# Test flasher functionality and validation
test_flasher.bat
```

**What it tests:**
- Flasher executable validation
- Firmware file presence and size validation
- Help/version output functionality
- File validation logic
- COM port detection
- Esptool availability

## 🔧 Manual Testing Procedures

### 1. Firmware Compilation Testing

#### ESP-IDF Build Test
```bash
cd esp32-firmware
idf.py set-target esp32s2
idf.py build
```

**Expected Results:**
- ✅ Build completes without errors
- ✅ `build/octane_hid_firmware.bin` created (>50KB)
- ✅ `build/bootloader/bootloader.bin` created (>20KB)
- ✅ `build/partition_table/partition-table.bin` created (>1KB)

#### PlatformIO Build Test
```bash
cd esp32-firmware
pio run -e esp32-s2-saola-1
```

**Expected Results:**
- ✅ Build completes without errors
- ✅ `.pio/build/esp32-s2-saola-1/firmware.bin` created (>50KB)
- ✅ Bootloader and partition files created

### 2. Flasher Application Testing

#### Basic Functionality Test
```bash
cd OctaneFlasher
OctaneFlasher.exe
```

**Test Scenarios:**
1. **Startup Test**
   - ✅ Application starts without crash
   - ✅ Version information displayed
   - ✅ UI elements render correctly

2. **File Validation Test**
   - ✅ Detects missing firmware files
   - ✅ Validates file sizes correctly
   - ✅ Shows appropriate error messages

3. **COM Port Detection Test**
   - ✅ Lists available COM ports
   - ✅ Handles no COM ports gracefully
   - ✅ Validates user input

### 3. Hardware Testing (ESP32-S2)

#### Prerequisites
- ESP32-S2 Mini board
- USB cable
- Windows 10/11 PC
- Valid Octane license key

#### Flashing Test
1. **Prepare ESP32-S2**
   - Hold BOOT button
   - Plug in USB while holding BOOT
   - Release BOOT button
   - Verify ESP32 appears in Device Manager

2. **Flash Firmware**
   ```bash
   cd OctaneFlasher
   OctaneFlasher.exe
   ```
   - ✅ ESP32 detected automatically
   - ✅ License validation succeeds
   - ✅ Flashing completes without errors
   - ✅ Post-flash verification succeeds

3. **Verify Firmware Operation**
   - ✅ Press RESET button on ESP32
   - ✅ LED on GPIO15 starts flashing (500ms interval)
   - ✅ ESP32 appears as HID mouse in Device Manager
   - ✅ Serial communication works at 921600 baud

#### HID Mouse Functionality Test
1. **Device Recognition**
   - ✅ ESP32 appears in Device Manager > Human Interface Devices
   - ✅ Shows as "HID-compliant mouse" or "Octane HID Mouse"
   - ✅ No driver installation required

2. **Serial Command Test**
   - Connect to ESP32 serial port at 921600 baud
   - Send commands and verify responses:

   ```
   PING → PONG
   STATUS → STATUS:OK,UPTIME:123,QUEUE:0,USB:READY,CONN:NO,VER:2.0.0
   VERSION → VERSION:2.0.0,BUILD:...,GIT:...,BOARD:ESP32-S2,MAJOR:2,MINOR:0,PATCH:0
   M10,5 → (Mouse moves 10 pixels right, 5 pixels down)
   CLICK_LEFT_DOWN → (Left mouse button pressed)
   CLICK_LEFT_UP → (Left mouse button released)
   ```

3. **LED Status Test**
   - ✅ LED flashes when desktop app not connected
   - ✅ LED becomes steady when desktop app connects
   - ✅ LED returns to flashing when desktop app disconnects

### 4. Desktop App Integration Testing

#### Connection Test
1. **Start Desktop App**
   - ✅ Desktop app detects ESP32 HID mouse
   - ✅ Serial communication established
   - ✅ LED becomes steady on ESP32

2. **Mouse Movement Test**
   - ✅ Desktop app sends movement commands
   - ✅ Mouse cursor moves smoothly
   - ✅ No lag or stuttering
   - ✅ Command queue prevents saturation

3. **Click Test**
   - ✅ Desktop app sends click commands
   - ✅ Mouse clicks register correctly
   - ✅ Both left and right clicks work
   - ✅ Click timing is accurate

### 5. Performance Testing

#### High-Frequency Movement Test
```
Test Parameters:
- Duration: 5 minutes
- Movement frequency: 100 commands/second
- Movement pattern: Random X/Y values
```

**Expected Results:**
- ✅ No command queue overflow
- ✅ Smooth mouse movement
- ✅ No USB disconnections
- ✅ Stable serial communication
- ✅ LED remains steady

#### Extended Operation Test
```
Test Parameters:
- Duration: 2 hours
- Continuous operation
- Mixed movement and click commands
```

**Expected Results:**
- ✅ No memory leaks
- ✅ Stable operation
- ✅ No performance degradation
- ✅ Error reporting functional

### 6. Error Handling Testing

#### Firmware Error Test
1. **Invalid Commands**
   - Send invalid serial commands
   - ✅ Firmware reports errors via serial
   - ✅ Error messages forwarded to desktop app
   - ✅ Discord webhooks receive error reports

2. **USB Disconnection Test**
   - Unplug ESP32 during operation
   - ✅ Desktop app detects disconnection
   - ✅ Graceful error handling
   - ✅ Automatic reconnection when plugged back

#### Flasher Error Test
1. **Missing Files Test**
   - Remove firmware files
   - ✅ Flasher detects missing files
   - ✅ Clear error messages displayed
   - ✅ Helpful troubleshooting info provided

2. **Invalid ESP32 Test**
   - Connect non-ESP32 device
   - ✅ Flasher detects incompatible device
   - ✅ Appropriate error message shown
   - ✅ No damage to connected device

## 📊 Test Results Documentation

### Test Report Template
```
Octane ESP32 HID Mouse Test Report
Date: [DATE]
Tester: [NAME]
Version: [VERSION]

Build System Tests:
[ ] ESP-IDF compilation
[ ] PlatformIO compilation
[ ] C# flasher compilation

Flasher Tests:
[ ] File validation
[ ] COM port detection
[ ] Esptool integration
[ ] User interface

Hardware Tests:
[ ] ESP32-S2 flashing
[ ] HID mouse recognition
[ ] Serial communication
[ ] LED status indicators

Integration Tests:
[ ] Desktop app connection
[ ] Mouse movement accuracy
[ ] Click functionality
[ ] Error reporting

Performance Tests:
[ ] High-frequency commands
[ ] Extended operation
[ ] Memory stability
[ ] USB stability

Issues Found:
[List any issues discovered during testing]

Overall Result: PASS/FAIL
```

## 🔧 Troubleshooting Common Issues

### Build Failures
- **ESP-IDF not found**: Run from ESP-IDF Command Prompt
- **TinyUSB errors**: Ensure ESP-IDF v4.4+ is used
- **Missing dependencies**: Install required components

### Flashing Failures
- **ESP32 not detected**: Check download mode procedure
- **Flashing timeout**: Try different USB cable/port
- **Permission errors**: Run as administrator

### Runtime Issues
- **HID not recognized**: Check USB descriptors
- **Serial communication fails**: Verify baud rate (921600)
- **LED not working**: Check GPIO15 configuration

## ✅ Test Completion Checklist

Before releasing any version:

### Development Testing
- [ ] All automated tests pass
- [ ] Manual compilation tests pass
- [ ] Code review completed
- [ ] Documentation updated

### Integration Testing
- [ ] Hardware flashing tested
- [ ] HID mouse functionality verified
- [ ] Desktop app integration working
- [ ] Error handling tested

### Performance Testing
- [ ] High-frequency operation tested
- [ ] Extended operation tested
- [ ] Memory usage validated
- [ ] USB stability confirmed

### User Acceptance Testing
- [ ] End-user scenarios tested
- [ ] Documentation accuracy verified
- [ ] Installation process validated
- [ ] Support procedures tested

### Release Preparation
- [ ] Version numbers consistent
- [ ] Distribution packages created
- [ ] Test results documented
- [ ] Release notes prepared
