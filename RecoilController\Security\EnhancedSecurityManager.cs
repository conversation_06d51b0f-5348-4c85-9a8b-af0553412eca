using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Net;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Timers;
using Newtonsoft.Json;
using RecoilController.Models;

namespace RecoilController.Security
{
    /// <summary>
    /// Enhanced security manager with Discord integration and hardware fingerprinting
    /// </summary>
    public class EnhancedSecurityManager : IDisposable
    {
        private readonly SecurityConfig _config;
        private readonly Timer _monitoringTimer;
        private readonly HttpClient _httpClient;
        private readonly Dictionary<string, DateTime> _alertCooldowns;
        private readonly HardwareFingerprint _hardwareFingerprint;
        private readonly string _encryptedWebhookUrl;
        private bool _disposed = false;

        // Obfuscated webhook URL (base64 encoded in parts)
        private readonly string[] _webhookParts = {
            "aHR0cHM6Ly9kaXNjb3JkLmNvbS9hcGkvd2ViaG9va3Mv", // https://discord.com/api/webhooks/
            "MTM5ODUwNjQ2NTg4MjQxMTA3OS8=", // 1398506465882411079/
            "NDlvQ3pyMDRSMHY3V29Eb2NQYTZGWVBheWtNSkplZ2tOZGRKamgycUNzYllrWQ==" // 49oCzr04R0v7WoDocPa6FYPaykMJJegkNddJjqh2qCsbYkY
        };

        public event EventHandler<SecurityAlert>? SecurityAlertTriggered;

        public EnhancedSecurityManager(SecurityConfig? config = null)
        {
            _config = config ?? GetCurrentConfig();
            _httpClient = new HttpClient();
            _alertCooldowns = new Dictionary<string, DateTime>();
            _hardwareFingerprint = CollectHardwareFingerprint();
            _encryptedWebhookUrl = DecryptWebhookUrl();

            if (_config.EnableProcessMonitoring)
            {
                _monitoringTimer = new Timer(5000); // Check every 5 seconds
                _monitoringTimer.Elapsed += OnMonitoringTimerElapsed;
                _monitoringTimer.Start();
            }

            if (_config.EnableAntiDebugger)
            {
                StartAntiDebuggerProtection();
            }
        }

        private SecurityConfig GetCurrentConfig()
        {
#if DEBUG
            return SecurityConfig.GetDebugConfig();
#else
            return SecurityConfig.GetProductionConfig();
#endif
        }

        private string DecryptWebhookUrl()
        {
            if (!_config.EnableDiscordWebhooks) return "";

            try
            {
                // For testing, use direct URL (in production, use obfuscated version)
                var url = "https://discord.com/api/webhooks/1398506465882411079/49oCzr04R0v7WoDocPa6FYPaykMJJegkNddJjqh2qCsbYkY";

                // Debug: Log the webhook URL (remove in production)
                Debug.WriteLine($"Webhook URL: {url}");

                return url;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to decrypt webhook URL: {ex.Message}");
                return "";
            }
        }

        // Test method to verify webhook
        public async Task TestDiscordWebhook()
        {
            var testAlert = new SecurityAlert("TEST_ALERT", "Test security alert from Octane")
            {
                ComputerName = Environment.MachineName,
                Username = Environment.UserName,
                IpAddress = "127.0.0.1",
                HardwareId = "TEST_HARDWARE_ID"
            };

            await SendDiscordAlert(testAlert, ThreatLevel.Low);
        }

        private HardwareFingerprint CollectHardwareFingerprint()
        {
            var fingerprint = new HardwareFingerprint
            {
                ComputerName = Environment.MachineName,
                WindowsProductId = GetWindowsProductId(),
                MachineGuid = GetMachineGuid()
            };

            if (!_config.EnableHardwareFingerprinting) return fingerprint;

            try
            {
                // Get processor ID
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        fingerprint.ProcessorId = obj["ProcessorId"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get motherboard serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        fingerprint.MotherboardSerial = obj["SerialNumber"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get disk serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        fingerprint.DiskSerial = obj["SerialNumber"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get BIOS serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        fingerprint.BiosSerial = obj["SerialNumber"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get MAC address
                var networkInterface = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(nic => nic.OperationalStatus == OperationalStatus.Up && 
                                          nic.NetworkInterfaceType != NetworkInterfaceType.Loopback);
                if (networkInterface != null)
                {
                    fingerprint.MacAddress = networkInterface.GetPhysicalAddress().ToString();
                }
            }
            catch (Exception ex)
            {
                // Log error but continue
                Debug.WriteLine($"Hardware fingerprinting error: {ex.Message}");
            }

            return fingerprint;
        }

        private string GetWindowsProductId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_OperatingSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        return obj["SerialNumber"]?.ToString() ?? "";
                    }
                }
            }
            catch { }
            return "";
        }

        private string GetMachineGuid()
        {
            try
            {
                using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Cryptography"))
                {
                    return key?.GetValue("MachineGuid")?.ToString() ?? "";
                }
            }
            catch { }
            return "";
        }

        private void StartAntiDebuggerProtection()
        {
            Task.Run(async () =>
            {
                while (!_disposed)
                {
                    try
                    {
                        // Check for debugger attachment
                        if (Debugger.IsAttached)
                        {
                            await TriggerSecurityAlert("DEBUGGER_ATTACHED", "Debugger detected attached to process", ThreatLevel.High);
                        }

                        // Check for known debugging processes
                        var runningProcesses = Process.GetProcesses().Select(p => p.ProcessName.ToLower()).ToList();
                        var detectedThreats = _config.MonitoredProcesses
                            .Where(threat => runningProcesses.Contains(threat.ToLower()))
                            .ToList();

                        foreach (var threat in detectedThreats)
                        {
                            var threatLevel = _config.BlockedProcesses.Contains(threat) ? ThreatLevel.Critical : ThreatLevel.Medium;
                            await TriggerSecurityAlert("SUSPICIOUS_PROCESS", $"Detected process: {threat}", threatLevel, threat);
                        }

                        await Task.Delay(2000); // Check every 2 seconds
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Anti-debugger protection error: {ex.Message}");
                        await Task.Delay(5000);
                    }
                }
            });
        }

        private void OnMonitoringTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            if (!_config.EnableProcessMonitoring) return;

            Task.Run(async () =>
            {
                try
                {
                    var processes = Process.GetProcesses();
                    var suspiciousProcesses = processes.Where(p => 
                        _config.MonitoredProcesses.Any(monitored => 
                            p.ProcessName.ToLower().Contains(monitored.ToLower()))).ToList();

                    foreach (var process in suspiciousProcesses)
                    {
                        var threatLevel = _config.BlockedProcesses.Contains(process.ProcessName.ToLower()) 
                            ? ThreatLevel.Critical : ThreatLevel.Medium;
                        
                        await TriggerSecurityAlert("PROCESS_MONITORING", 
                            $"Suspicious process detected: {process.ProcessName}", 
                            threatLevel, process.ProcessName);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Process monitoring error: {ex.Message}");
                }
            });
        }

        public async Task TriggerSecurityAlert(string alertType, string description, ThreatLevel threatLevel, string? processName = null)
        {
            if (!_config.EnableSecurityAlerts) return;

            // Check cooldown
            var cooldownKey = $"{alertType}_{processName}";
            if (_alertCooldowns.ContainsKey(cooldownKey))
            {
                var timeSinceLastAlert = DateTime.UtcNow - _alertCooldowns[cooldownKey];
                if (timeSinceLastAlert.TotalSeconds < _config.AlertCooldownSeconds)
                {
                    return; // Still in cooldown
                }
            }

            _alertCooldowns[cooldownKey] = DateTime.UtcNow;

            var alert = new SecurityAlert(alertType, description)
            {
                ProcessName = processName ?? "",
                HardwareId = _hardwareFingerprint.GenerateFingerprint(),
                IpAddress = await GetPublicIpAddress(),
                ComputerName = _hardwareFingerprint.ComputerName,
                Username = Environment.UserName,
                WindowsVersion = Environment.OSVersion.ToString(),
                CpuInfo = _hardwareFingerprint.ProcessorId,
                MotherboardSerial = _hardwareFingerprint.MotherboardSerial,
                MacAddress = _hardwareFingerprint.MacAddress,
                DiskSerial = _hardwareFingerprint.DiskSerial,
                BiosSerial = _hardwareFingerprint.BiosSerial,
                IsBlocked = threatLevel == ThreatLevel.Critical
            };

            // Add comprehensive system and security information
            alert.AdditionalData["ThreatLevel"] = threatLevel.ToString();
            alert.AdditionalData["ProcessorCount"] = Environment.ProcessorCount.ToString();
            alert.AdditionalData["SystemDirectory"] = Environment.SystemDirectory;
            alert.AdditionalData["UserDomainName"] = Environment.UserDomainName;
            alert.AdditionalData["WorkingSet"] = Environment.WorkingSet.ToString();
            alert.AdditionalData["TickCount"] = Environment.TickCount.ToString();
            alert.AdditionalData["Is64BitOS"] = Environment.Is64BitOperatingSystem.ToString();
            alert.AdditionalData["Is64BitProcess"] = Environment.Is64BitProcess.ToString();
            alert.AdditionalData["CLRVersion"] = Environment.Version.ToString();

            // Add detailed debugger information
            await AddDebuggerInformation(alert);

            // Add process information
            await AddProcessInformation(alert, processName);

            // Add memory information
            AddMemoryInformation(alert);

            // Add timing information
            AddTimingInformation(alert);

            // Log to local file for detailed analysis
            await LogSecurityEventToFile(alert, threatLevel);

            SecurityAlertTriggered?.Invoke(this, alert);

            // Send to Discord webhook
            if (_config.EnableDiscordWebhooks && !string.IsNullOrEmpty(_encryptedWebhookUrl))
            {
                await SendDiscordAlert(alert, threatLevel);
            }

            // Send to admin panel API
            await SendToAdminPanel(alert);

            // Take action based on threat level
            if (threatLevel == ThreatLevel.Critical && alert.IsBlocked)
            {
                await HandleCriticalThreat(alert);
            }
        }

        private async Task<string> GetPublicIpAddress()
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(5);
                return await client.GetStringAsync("https://api.ipify.org");
            }
            catch
            {
                return "Unknown";
            }
        }

        private async Task SendDiscordAlert(SecurityAlert alert, ThreatLevel threatLevel)
        {
            try
            {
                var color = threatLevel switch
                {
                    ThreatLevel.Low => 0x00FF00,      // Green
                    ThreatLevel.Medium => 0xFFFF00,   // Yellow
                    ThreatLevel.High => 0xFF8000,     // Orange
                    ThreatLevel.Critical => 0xFF0000, // Red
                    _ => 0x808080                     // Gray
                };

                var embed = new
                {
                    title = $"🚨 Security Alert - {alert.AlertType}",
                    description = alert.Description,
                    color = color,
                    timestamp = alert.Timestamp.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new { name = "🖥️ Computer", value = alert.ComputerName, inline = true },
                        new { name = "👤 User", value = alert.Username, inline = true },
                        new { name = "🌐 IP Address", value = alert.IpAddress, inline = true },
                        new { name = "🔧 Process", value = string.IsNullOrEmpty(alert.ProcessName) ? "N/A" : alert.ProcessName, inline = true },
                        new { name = "⚠️ Threat Level", value = threatLevel.ToString(), inline = true },
                        new { name = "🚫 Blocked", value = alert.IsBlocked ? "Yes" : "No", inline = true },
                        new { name = "🆔 Hardware ID", value = alert.HardwareId.Substring(0, Math.Min(16, alert.HardwareId.Length)) + "...", inline = false },
                        new { name = "💻 System Info", value = $"OS: {alert.WindowsVersion}\nMAC: {alert.MacAddress}", inline = false }
                    },
                    footer = new
                    {
                        text = "Octane Security System",
                        icon_url = "https://cdn.discordapp.com/emojis/1234567890123456789.png"
                    }
                };

                var payload = new
                {
                    username = "Octane Security",
                    avatar_url = "https://cdn.discordapp.com/emojis/1234567890123456789.png",
                    embeds = new[] { embed }
                };

                var json = JsonConvert.SerializeObject(payload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(_encryptedWebhookUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"Discord webhook failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Discord webhook error: {ex.Message}");
            }
        }

        private async Task SendToAdminPanel(SecurityAlert alert)
        {
            try
            {
                // Send to admin panel API endpoint
                var apiUrl = "https://octane-auth.com/api/security/log-event"; // Updated to actual backend URL
                var json = JsonConvert.SerializeObject(alert);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(apiUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    Debug.WriteLine($"Admin panel API failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Admin panel API error: {ex.Message}");
            }
        }

        private async Task HandleCriticalThreat(SecurityAlert alert)
        {
            try
            {
                // Terminate suspicious processes
                if (!string.IsNullOrEmpty(alert.ProcessName))
                {
                    var processes = Process.GetProcessesByName(alert.ProcessName);
                    foreach (var process in processes)
                    {
                        try
                        {
                            process.Kill();
                            process.WaitForExit(5000);
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Failed to terminate process {alert.ProcessName}: {ex.Message}");
                        }
                    }
                }

                // Log security threat silently (no popup to avoid alerting user)
                Debug.WriteLine($"Security threat detected: {alert.Description} - Application will exit silently");

                // Exit application
                await Task.Delay(2000);
                Environment.Exit(1);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Critical threat handling error: {ex.Message}");
            }
        }

        private async Task AddDebuggerInformation(SecurityAlert alert)
        {
            try
            {
                // Check for various debugger types
                alert.AdditionalData["IsDebuggerPresent"] = System.Diagnostics.Debugger.IsAttached.ToString();

                // Check for remote debuggers
                var currentProcess = Process.GetCurrentProcess();
                alert.AdditionalData["ProcessId"] = currentProcess.Id.ToString();
                alert.AdditionalData["ProcessName"] = currentProcess.ProcessName;
                alert.AdditionalData["StartTime"] = currentProcess.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                alert.AdditionalData["WorkingSet64"] = currentProcess.WorkingSet64.ToString();
                alert.AdditionalData["VirtualMemorySize64"] = currentProcess.VirtualMemorySize64.ToString();

                // Check for known debugger processes
                var suspiciousProcesses = new List<string>();
                var allProcesses = Process.GetProcesses();

                var knownDebuggers = new[]
                {
                    "x64dbg", "x32dbg", "ollydbg", "windbg", "ida", "ida64", "idaq", "idaq64",
                    "cheatengine", "ce", "dnspy", "reflexil", "hxd", "immunity", "softice",
                    "processhacker", "procexp", "procmon", "wireshark", "fiddler", "burpsuite"
                };

                foreach (var process in allProcesses)
                {
                    try
                    {
                        var processName = process.ProcessName.ToLower();
                        if (knownDebuggers.Any(debugger => processName.Contains(debugger)))
                        {
                            suspiciousProcesses.Add($"{process.ProcessName} (PID: {process.Id})");
                        }
                    }
                    catch { /* Ignore access denied */ }
                }

                alert.AdditionalData["SuspiciousProcesses"] = string.Join(", ", suspiciousProcesses);
                alert.AdditionalData["SuspiciousProcessCount"] = suspiciousProcesses.Count.ToString();

                // Check for DLL injection indicators
                var loadedModules = new List<string>();
                foreach (ProcessModule module in currentProcess.Modules)
                {
                    try
                    {
                        var moduleName = module.ModuleName.ToLower();
                        if (moduleName.Contains("inject") || moduleName.Contains("hook") ||
                            moduleName.Contains("detour") || moduleName.Contains("patch"))
                        {
                            loadedModules.Add($"{module.ModuleName} ({module.FileName})");
                        }
                    }
                    catch { /* Ignore access denied */ }
                }

                alert.AdditionalData["SuspiciousModules"] = string.Join(", ", loadedModules);
                alert.AdditionalData["LoadedModuleCount"] = currentProcess.Modules.Count.ToString();
            }
            catch (Exception ex)
            {
                alert.AdditionalData["DebuggerCheckError"] = ex.Message;
            }
        }

        private async Task AddProcessInformation(SecurityAlert alert, string? processName)
        {
            try
            {
                if (!string.IsNullOrEmpty(processName))
                {
                    var processes = Process.GetProcessesByName(processName);
                    if (processes.Length > 0)
                    {
                        var process = processes[0];
                        alert.AdditionalData["TargetProcessId"] = process.Id.ToString();
                        alert.AdditionalData["TargetProcessStartTime"] = process.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                        alert.AdditionalData["TargetProcessFileName"] = process.MainModule?.FileName ?? "Unknown";
                        alert.AdditionalData["TargetProcessCommandLine"] = GetProcessCommandLine(process.Id);
                        alert.AdditionalData["TargetProcessParentId"] = GetParentProcessId(process.Id).ToString();
                    }
                }

                // Add information about all running processes for context
                var processCount = Process.GetProcesses().Length;
                alert.AdditionalData["TotalRunningProcesses"] = processCount.ToString();

                // Check for processes with suspicious names or behaviors
                var highCpuProcesses = new List<string>();
                foreach (var process in Process.GetProcesses())
                {
                    try
                    {
                        if (process.TotalProcessorTime.TotalSeconds > 60) // High CPU usage
                        {
                            highCpuProcesses.Add($"{process.ProcessName} (CPU: {process.TotalProcessorTime.TotalSeconds:F1}s)");
                        }
                    }
                    catch { /* Ignore access denied */ }
                }

                alert.AdditionalData["HighCpuProcesses"] = string.Join(", ", highCpuProcesses.Take(5));
            }
            catch (Exception ex)
            {
                alert.AdditionalData["ProcessInfoError"] = ex.Message;
            }
        }

        private void AddMemoryInformation(SecurityAlert alert)
        {
            try
            {
                var gc = GC.GetTotalMemory(false);
                alert.AdditionalData["ManagedMemory"] = gc.ToString();
                alert.AdditionalData["GCGen0Collections"] = GC.CollectionCount(0).ToString();
                alert.AdditionalData["GCGen1Collections"] = GC.CollectionCount(1).ToString();
                alert.AdditionalData["GCGen2Collections"] = GC.CollectionCount(2).ToString();

                // Get system memory information using WMI or performance counters
                var currentProcess = Process.GetCurrentProcess();
                alert.AdditionalData["ProcessWorkingSet"] = currentProcess.WorkingSet64.ToString();
                alert.AdditionalData["ProcessVirtualMemory"] = currentProcess.VirtualMemorySize64.ToString();
                alert.AdditionalData["ProcessPrivateMemory"] = currentProcess.PrivateMemorySize64.ToString();
            }
            catch (Exception ex)
            {
                alert.AdditionalData["MemoryInfoError"] = ex.Message;
            }
        }

        private void AddTimingInformation(SecurityAlert alert)
        {
            try
            {
                alert.AdditionalData["SystemUptime"] = TimeSpan.FromMilliseconds(Environment.TickCount).ToString();
                alert.AdditionalData["ProcessUptime"] = (DateTime.Now - Process.GetCurrentProcess().StartTime).ToString();
                alert.AdditionalData["LastBootTime"] = (DateTime.Now - TimeSpan.FromMilliseconds(Environment.TickCount)).ToString("yyyy-MM-dd HH:mm:ss");
                alert.AdditionalData["TimeZone"] = TimeZoneInfo.Local.DisplayName;
                alert.AdditionalData["UTCOffset"] = TimeZoneInfo.Local.GetUtcOffset(DateTime.Now).ToString();
            }
            catch (Exception ex)
            {
                alert.AdditionalData["TimingInfoError"] = ex.Message;
            }
        }

        private string GetProcessCommandLine(int processId)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT CommandLine FROM Win32_Process WHERE ProcessId = {processId}");
                using var objects = searcher.Get();
                return objects.Cast<ManagementBaseObject>().SingleOrDefault()?["CommandLine"]?.ToString() ?? "";
            }
            catch
            {
                return "Access Denied";
            }
        }

        private int GetParentProcessId(int processId)
        {
            try
            {
                using var searcher = new ManagementObjectSearcher($"SELECT ParentProcessId FROM Win32_Process WHERE ProcessId = {processId}");
                using var objects = searcher.Get();
                return Convert.ToInt32(objects.Cast<ManagementBaseObject>().SingleOrDefault()?["ParentProcessId"] ?? 0);
            }
            catch
            {
                return 0;
            }
        }

        private async Task LogSecurityEventToFile(SecurityAlert alert, ThreatLevel threatLevel)
        {
            try
            {
                var logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "OctaneRecoil", "SecurityLogs");
                Directory.CreateDirectory(logDirectory);

                var logFileName = $"security_log_{DateTime.Now:yyyy-MM-dd}.json";
                var logFilePath = Path.Combine(logDirectory, logFileName);

                var logEntry = new
                {
                    Timestamp = DateTime.UtcNow,
                    ThreatLevel = threatLevel.ToString(),
                    Alert = alert,
                    SystemSnapshot = new
                    {
                        Environment.MachineName,
                        Environment.UserName,
                        Environment.OSVersion,
                        Environment.ProcessorCount,
                        Environment.SystemDirectory,
                        Environment.TickCount,
                        Environment.WorkingSet,
                        CurrentDirectory = Environment.CurrentDirectory,
                        CommandLine = Environment.CommandLine
                    }
                };

                var jsonOptions = new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                };

                var logJson = System.Text.Json.JsonSerializer.Serialize(logEntry, jsonOptions);

                // Append to daily log file
                await File.AppendAllTextAsync(logFilePath, logJson + Environment.NewLine + "," + Environment.NewLine);

                // Keep only last 30 days of logs
                CleanupOldLogs(logDirectory);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to log security event to file: {ex.Message}");
            }
        }

        private void CleanupOldLogs(string logDirectory)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(logDirectory, "security_log_*.json");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to cleanup old logs: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;
            _monitoringTimer?.Stop();
            _monitoringTimer?.Dispose();
            _httpClient?.Dispose();
        }
    }
}
