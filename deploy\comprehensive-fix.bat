@echo off
echo.
echo 🔧 OCTANE COMPREHENSIVE FIX SCRIPT
echo =================================
echo.

set VPS_IP=*************
set SSH_KEY=%~dp0octane_key

echo 🔍 STEP 1: Fix Admin Panel Duration Issue
echo ========================================
echo Checking current admin panel JavaScript...
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth/public/js && grep -n 'selectedDuration' admin-panel.js | head -5"
echo.

echo 🔍 STEP 2: Test License Creation API Directly
echo ============================================
echo Testing with different duration formats...
echo Testing 1hour:
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -X POST http://localhost:3000/api/admin/licenses -H 'Content-Type: application/json' -H 'Authorization: Bearer firebase-auth' -d '{\"duration\":\"1hour\",\"notes\":\"Test 1hour\"}' -w '\nHTTP Status: %%{http_code}\n'"
echo.

echo Testing 1day:
ssh -i "%SSH_KEY%" root@%VPS_IP% "curl -X POST http://localhost:3000/api/admin/licenses -H 'Content-Type: application/json' -H 'Authorization: Bearer firebase-auth' -d '{\"duration\":\"1day\",\"notes\":\"Test 1day\"}' -w '\nHTTP Status: %%{http_code}\n'"
echo.

echo 🔍 STEP 3: Check Database Function
echo =================================
echo Testing database createLicense function directly...
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && node -e 'const db=require(\"./models/database\"); db.initialize().then(()=>db.createLicense(\"1hour\",\"Direct test\")).then(license=>console.log(\"✅ License created:\",license.key)).catch(e=>console.log(\"❌ Error:\",e.message))'"
echo.

echo 🔍 STEP 4: Fix Discord Bot Intents
echo =================================
echo Checking Discord service configuration...
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && grep -A10 -B5 'GatewayIntentBits' services/discord.js"
echo.

echo 🔍 STEP 5: Test Admin Panel Frontend
echo ===================================
echo Checking if admin panel files are properly cached...
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth/public && ls -la js/ css/"
echo.

echo 🔍 STEP 6: Server Status Check
echo =============================
ssh -i "%SSH_KEY%" root@%VPS_IP% "sudo -u octane pm2 status"
echo.

echo 🔍 STEP 7: Recent Error Logs
echo ===========================
ssh -i "%SSH_KEY%" root@%VPS_IP% "cd /opt/octane-auth && sudo -u octane pm2 logs octane-auth --err --lines 5"
echo.

echo.
echo 📋 ANALYSIS COMPLETE!
echo ====================
echo.
echo Next steps based on results:
echo 1. Fix any duration issues found
echo 2. Fix Discord bot intents
echo 3. Clear browser cache for admin panel
echo 4. Test desktop app integration
echo.
pause
