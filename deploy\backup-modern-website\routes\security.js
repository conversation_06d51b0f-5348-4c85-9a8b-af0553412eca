const express = require('express');
const router = express.Router();
const SecurityEvent = require('../models/SecurityEvent');

// Get security events and statistics
router.get('/security/events', async (req, res) => {
    try {
        // Get recent security events
        const events = await SecurityEvent.find()
            .sort({ timestamp: -1 })
            .limit(50)
            .lean();

        // Calculate statistics
        const stats = {
            total: await SecurityEvent.countDocuments(),
            critical: await SecurityEvent.countDocuments({ severity: 'critical' }),
            blocked: await SecurityEvent.countDocuments({ type: 'user_blocked' }),
            active: await SecurityEvent.countDocuments({ 
                timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
            })
        };

        res.json({
            success: true,
            stats,
            events
        });
    } catch (error) {
        console.error('Error fetching security events:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch security events'
        });
    }
});

// Clear security log
router.post('/security/clear-log', async (req, res) => {
    try {
        await SecurityEvent.deleteMany({});
        
        // Log the clear action
        await SecurityEvent.create({
            type: 'log_cleared',
            severity: 'medium',
            description: 'Security log was cleared by admin',
            timestamp: new Date(),
            ipAddress: req.ip,
            username: 'admin'
        });

        res.json({
            success: true,
            message: 'Security log cleared successfully'
        });
    } catch (error) {
        console.error('Error clearing security log:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to clear security log'
        });
    }
});

// Export security log
router.get('/security/export-log', async (req, res) => {
    try {
        const events = await SecurityEvent.find()
            .sort({ timestamp: -1 })
            .lean();

        const exportData = {
            exportDate: new Date().toISOString(),
            totalEvents: events.length,
            events: events
        };

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename=security-log-${new Date().toISOString().split('T')[0]}.json`);
        res.json(exportData);
    } catch (error) {
        console.error('Error exporting security log:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to export security log'
        });
    }
});

// Block suspicious users
router.post('/security/block-suspicious', async (req, res) => {
    try {
        // Find suspicious events (multiple failed attempts, etc.)
        const suspiciousEvents = await SecurityEvent.find({
            severity: { $in: ['critical', 'high'] },
            type: { $in: ['failed_auth', 'suspicious_activity', 'multiple_attempts'] }
        }).lean();

        // Extract unique hardware IDs
        const suspiciousHardwareIds = [...new Set(
            suspiciousEvents
                .filter(event => event.hardwareId)
                .map(event => event.hardwareId)
        )];

        let blockedCount = 0;

        // Block each suspicious user
        for (const hardwareId of suspiciousHardwareIds) {
            // Log the block action
            await SecurityEvent.create({
                type: 'user_blocked',
                severity: 'high',
                description: `User blocked due to suspicious activity`,
                timestamp: new Date(),
                hardwareId: hardwareId,
                ipAddress: req.ip,
                username: 'admin'
            });
            
            blockedCount++;
        }

        res.json({
            success: true,
            blocked: blockedCount,
            message: `Blocked ${blockedCount} suspicious users`
        });
    } catch (error) {
        console.error('Error blocking suspicious users:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to block suspicious users'
        });
    }
});

// Log a security event (for use by other parts of the application)
router.post('/security/log-event', async (req, res) => {
    try {
        // Handle both simple and comprehensive security alert formats
        let eventData;

        if (req.body.AlertType) {
            // Comprehensive SecurityAlert format from C# application
            const alert = req.body;
            eventData = {
                type: alert.AlertType || 'unknown',
                severity: alert.AdditionalData?.ThreatLevel?.toLowerCase() || 'medium',
                description: alert.Description || 'Security event logged',
                timestamp: new Date(alert.Timestamp || Date.now()),
                username: alert.Username || 'unknown',
                hardwareId: alert.HardwareId,
                ipAddress: alert.IpAddress || req.ip,
                metadata: {
                    processName: alert.ProcessName,
                    computerName: alert.ComputerName,
                    windowsVersion: alert.WindowsVersion,
                    cpuInfo: alert.CpuInfo,
                    motherboardSerial: alert.MotherboardSerial,
                    macAddress: alert.MacAddress,
                    diskSerial: alert.DiskSerial,
                    biosSerial: alert.BiosSerial,
                    isBlocked: alert.IsBlocked,
                    isPermanentBan: alert.IsPermanentBan,
                    licenseKey: alert.LicenseKey,
                    additionalData: alert.AdditionalData || {}
                }
            };
        } else {
            // Simple format
            const { type, severity, description, username, hardwareId, ipAddress, metadata } = req.body;
            eventData = {
                type: type || 'unknown',
                severity: severity || 'medium',
                description: description || 'Security event logged',
                timestamp: new Date(),
                username: username || 'unknown',
                hardwareId: hardwareId,
                ipAddress: ipAddress || req.ip,
                metadata: metadata || {}
            };
        }

        const event = await SecurityEvent.create(eventData);

        // Log critical events to console for immediate attention
        if (eventData.severity === 'critical') {
            console.log(`🚨 CRITICAL SECURITY EVENT: ${eventData.description}`);
            console.log(`User: ${eventData.username}, Hardware: ${eventData.hardwareId?.substring(0, 16)}...`);
            console.log(`IP: ${eventData.ipAddress}, Process: ${eventData.metadata?.processName || 'N/A'}`);
        }

        res.json({
            success: true,
            eventId: event._id,
            message: 'Security event logged successfully',
            severity: eventData.severity
        });
    } catch (error) {
        console.error('Error logging security event:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to log security event',
            details: error.message
        });
    }
});

// Get security statistics only
router.get('/security/stats', async (req, res) => {
    try {
        const stats = {
            total: await SecurityEvent.countDocuments(),
            critical: await SecurityEvent.countDocuments({ severity: 'critical' }),
            high: await SecurityEvent.countDocuments({ severity: 'high' }),
            medium: await SecurityEvent.countDocuments({ severity: 'medium' }),
            low: await SecurityEvent.countDocuments({ severity: 'low' }),
            blocked: await SecurityEvent.countDocuments({ type: 'user_blocked' }),
            active: await SecurityEvent.countDocuments({ 
                timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
            }),
            lastEvent: await SecurityEvent.findOne().sort({ timestamp: -1 }).lean()
        };

        res.json({
            success: true,
            stats
        });
    } catch (error) {
        console.error('Error fetching security stats:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch security statistics'
        });
    }
});

// This route is duplicate - removed to fix 500 error

// Ban a user by hardware ID
router.post('/security/ban-user', async (req, res) => {
    try {
        const { hardwareId, username, reason } = req.body;

        if (!hardwareId) {
            return res.status(400).json({
                success: false,
                error: 'Hardware ID is required'
            });
        }

        // Log the ban event
        await SecurityEvent.create({
            type: 'user_blocked',
            severity: 'high',
            description: `User banned: ${username || 'Unknown'} - Reason: ${reason || 'Security violation'}`,
            timestamp: new Date(),
            username: username || 'unknown',
            hardwareId: hardwareId,
            ipAddress: req.ip,
            metadata: {
                action: 'ban',
                reason: reason || 'Security violation',
                bannedBy: 'admin',
                bannedAt: new Date().toISOString()
            }
        });

        // TODO: Add to banned users list/database
        // For now, just log the event

        res.json({
            success: true,
            message: `User ${username || 'Unknown'} has been banned`,
            hardwareId: hardwareId
        });
    } catch (error) {
        console.error('Error banning user:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to ban user'
        });
    }
});

// Allow a user by hardware ID (unban)
router.post('/security/allow-user', async (req, res) => {
    try {
        const { hardwareId, username } = req.body;

        if (!hardwareId) {
            return res.status(400).json({
                success: false,
                error: 'Hardware ID is required'
            });
        }

        // Log the unban event
        await SecurityEvent.create({
            type: 'user_unblocked',
            severity: 'medium',
            description: `User unbanned: ${username || 'Unknown'}`,
            timestamp: new Date(),
            username: username || 'unknown',
            hardwareId: hardwareId,
            ipAddress: req.ip,
            metadata: {
                action: 'unban',
                unbannedBy: 'admin',
                unbannedAt: new Date().toISOString()
            }
        });

        // TODO: Remove from banned users list/database
        // For now, just log the event

        res.json({
            success: true,
            message: `User ${username || 'Unknown'} has been allowed`,
            hardwareId: hardwareId
        });
    } catch (error) {
        console.error('Error allowing user:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to allow user'
        });
    }
});

module.exports = router;
