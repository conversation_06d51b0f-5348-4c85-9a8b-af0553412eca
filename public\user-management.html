<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - User Management</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>

            <!-- User Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3>📈 User Statistics</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--success-color);" id="activeUsers">0</div>
                            <div style="color: var(--text-secondary);">Active Users</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--warning-color);" id="boundUsers">0</div>
                            <div style="color: var(--text-secondary);">Bound to HWID</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--danger-color);" id="bannedUsers">0</div>
                            <div style="color: var(--text-secondary);">Banned Users</div>
                        </div>
                        <div style="text-align: center; padding: 20px; background: var(--bg-secondary); border-radius: var(--border-radius);">
                            <div style="font-size: 2rem; font-weight: bold; color: var(--info-color);" id="onlineUsers">0</div>
                            <div style="color: var(--text-secondary);">Online Now</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management Tools -->
            <div class="card">
                <div class="card-header">
                    <h3>🔧 Management Tools</h3>
                </div>
                <div class="card-content">
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <button class="btn btn-secondary" onclick="refreshUsers()">
                            <i class="fas fa-sync-alt"></i> Refresh Data
                        </button>
                        <button class="btn btn-warning" onclick="resetAllHWID()">
                            <i class="fas fa-undo"></i> Reset All HWID
                        </button>
                        <button class="btn btn-info" onclick="exportUsers()">
                            <i class="fas fa-download"></i> Export Users
                        </button>
                        <button class="btn btn-success" onclick="sendBroadcast()">
                            <i class="fas fa-bullhorn"></i> Send Broadcast
                        </button>
                    </div>
                </div>
            </div>

            <!-- Active Users List -->
            <div class="card">
                <div class="card-header">
                    <h3>👥 Active Users</h3>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="searchUsers" placeholder="🔍 Search users..." class="form-control" style="max-width: 300px;">
                        <select id="statusFilter" class="form-control" style="max-width: 200px;">
                            <option value="all">All Status</option>
                            <option value="online">Online</option>
                            <option value="offline">Offline</option>
                            <option value="banned">Banned</option>
                        </select>
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>License Key</th>
                                    <th>HWID</th>
                                    <th>Status</th>
                                    <th>Last Seen</th>
                                    <th>IP Address</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="6" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading users...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h3>📊 Recent Activity</h3>
                </div>
                <div class="card-content">
                    <div id="recentActivity" style="max-height: 400px; overflow-y: auto;">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> Loading activity...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                loadUserData();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });

        // User management functions
        function loadUserData() {
            loadUserStats();
            loadActiveUsers();
            loadRecentActivity();
        }

        function loadUserStats() {
            Promise.all([
                fetch('/api/users/active'),
                fetch('/api/users/stats')
            ]).then(async ([activeRes, statsRes]) => {
                const active = await activeRes.json();
                const stats = await statsRes.json();

                document.getElementById('activeUsers').textContent = active.count || 0;
                document.getElementById('boundUsers').textContent = stats.bound || 0;
                document.getElementById('bannedUsers').textContent = stats.banned || 0;
                document.getElementById('onlineUsers').textContent = stats.online || 0;
            }).catch(error => {
                console.error('Error loading user stats:', error);
            });
        }

        function loadActiveUsers() {
            fetch('/api/admin/users')
                .then(response => response.json())
                .then(result => {
                    const tbody = document.getElementById('usersTableBody');

                    if (result.success && result.users.length > 0) {
                        tbody.innerHTML = result.users.map(user => `
                            <tr>
                                <td><code style="background: var(--bg-secondary); padding: 4px 8px; border-radius: 4px;">${user.license_key}</code></td>
                                <td><span class="hwid-display" title="${user.hwid || 'Not bound'}">${user.hwid ? user.hwid.substring(0, 12) + '...' : 'Not Bound'}</span></td>
                                <td><span class="status-badge ${user.is_online ? 'active' : 'inactive'}">${user.is_online ? 'Online' : 'Offline'}</span></td>
                                <td>${user.last_seen ? OctaneUtils.formatDate(user.last_seen) : 'Never'}</td>
                                <td>${user.ip_address || 'Unknown'}</td>
                                <td>
                                    <div style="display: flex; gap: 5px;">
                                        <button class="btn btn-sm btn-warning" onclick="resetUserHWID('${user.license_key}')" title="Reset HWID">
                                            🔄
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="banUser('${user.license_key}')" title="Ban User">
                                            🚫
                                        </button>
                                        <button class="btn btn-sm btn-info" onclick="viewUserDetails('${user.license_key}')" title="View Details">
                                            👁️
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('');
                    } else {
                        tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--text-muted);">No active users found</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    document.getElementById('usersTableBody').innerHTML = '<tr><td colspan="6" style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading users</td></tr>';
                });
        }

        function loadRecentActivity() {
            fetch('/api/admin/activity')
                .then(response => response.json())
                .then(result => {
                    const container = document.getElementById('recentActivity');

                    if (result.success && result.activities.length > 0) {
                        container.innerHTML = result.activities.map(activity => `
                            <div style="padding: 10px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>${activity.action}</strong> - ${activity.license_key}
                                    <div style="font-size: 0.8rem; color: var(--text-muted);">${activity.details}</div>
                                </div>
                                <div style="font-size: 0.8rem; color: var(--text-secondary);">
                                    ${OctaneUtils.formatDate(activity.timestamp)}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<div style="text-align: center; padding: 20px; color: var(--text-muted);">No recent activity</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading activity:', error);
                    document.getElementById('recentActivity').innerHTML = '<div style="text-align: center; padding: 20px; color: var(--danger-color);">Error loading activity</div>';
                });
        }

        function refreshUsers() {
            loadUserData();
            OctaneUtils.showAlert('🔄 User data refreshed', 'info');
        }

        function resetUserHWID(licenseKey) {
            if (!confirm('Reset HWID for this user? They will be able to use their license on a different device.')) return;

            fetch(`/api/admin/licenses/${licenseKey}/reset-hwid`, { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ HWID reset successfully', 'success');
                        loadActiveUsers();
                    } else {
                        OctaneUtils.showAlert('❌ Failed to reset HWID: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error resetting HWID: ' + error.message, 'danger');
                });
        }

        function banUser(licenseKey) {
            if (!confirm('Ban this user? Their license will be deactivated.')) return;

            fetch(`/api/admin/users/${licenseKey}/ban`, { method: 'POST' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        OctaneUtils.showAlert('✅ User banned successfully', 'success');
                        loadUserData();
                    } else {
                        OctaneUtils.showAlert('❌ Failed to ban user: ' + result.error, 'danger');
                    }
                })
                .catch(error => {
                    OctaneUtils.showAlert('❌ Error banning user: ' + error.message, 'danger');
                });
        }

        function viewUserDetails(licenseKey) {
            OctaneUtils.showAlert('👁️ User details feature coming soon...', 'info');
        }

        function resetAllHWID() {
            if (!confirm('Reset HWID for ALL users? This will allow all users to rebind their licenses.')) return;
            OctaneUtils.showAlert('🔄 Bulk HWID reset feature coming soon...', 'info');
        }

        function exportUsers() {
            OctaneUtils.showAlert('📊 Export users feature coming soon...', 'info');
        }

        function sendBroadcast() {
            const message = prompt('Enter broadcast message to send to all users:');
            if (message) {
                OctaneUtils.showAlert('📢 Broadcast feature coming soon...', 'info');
            }
        }
    </script>
</body>
</html>
