const express = require('express');
const router = express.Router();

// Discord webhook function
async function sendDiscordWebhook(webhookUrl, message) {
    try {
        if (!webhookUrl) {
            console.error('No webhook URL provided');
            return false;
        }

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Octane-Auth-Bot/1.0'
            },
            body: JSON.stringify(message)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Webhook failed: ${response.status} ${response.statusText} - ${errorText}`);
            return false;
        }

        console.log('✅ Webhook sent successfully');
        return true;
    } catch (error) {
        console.error('Discord webhook error:', error);
        return false;
    }
}

// Test Discord webhooks
router.post('/api/admin/test-discord', async (req, res) => {
    try {
        console.log('Testing Discord webhooks...');
        
        const webhooks = {
            'Security Alerts': 'https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
            'ESP32 Errors': 'https://discord.com/api/webhooks/1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
            'Backend Errors': 'https://discord.com/api/webhooks/1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
        };

        const results = {};
        
        for (const [name, webhook] of Object.entries(webhooks)) {
            const testMessage = {
                embeds: [{
                    title: `🧪 Webhook Test - ${name}`,
                    description: `Testing webhook connectivity for ${name}`,
                    color: 0x4776E6,
                    fields: [
                        {
                            name: 'Test Time',
                            value: new Date().toISOString(),
                            inline: true
                        },
                        {
                            name: 'Status',
                            value: 'Testing...',
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString()
                }]
            };

            const success = await sendDiscordWebhook(webhook, testMessage);
            results[name] = success;
        }

        const successCount = Object.values(results).filter(r => r).length;
        const totalCount = Object.keys(results).length;

        res.json({
            success: successCount > 0,
            message: `Webhook test completed: ${successCount}/${totalCount} webhooks working`,
            results: results
        });
    } catch (error) {
        console.error('Discord test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test Discord webhooks',
            error: error.message
        });
    }
});

// Send security alert webhook
router.post('/api/admin/security-alert', async (req, res) => {
    try {
        const { title, description, severity = 'warning' } = req.body;
        
        if (!title || !description) {
            return res.status(400).json({
                success: false,
                message: 'Title and description are required'
            });
        }

        const colors = {
            info: 0x4776E6,
            warning: 0xFFAA00,
            error: 0xFF0000,
            critical: 0x8B0000
        };

        const securityWebhook = 'https://discord.com/api/webhooks/1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy';
        
        const alertMessage = {
            embeds: [{
                title: `🚨 Security Alert - ${title}`,
                description: description,
                color: colors[severity] || colors.warning,
                fields: [
                    {
                        name: 'Severity',
                        value: severity.toUpperCase(),
                        inline: true
                    },
                    {
                        name: 'Time',
                        value: new Date().toISOString(),
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString()
            }]
        };

        const success = await sendDiscordWebhook(securityWebhook, alertMessage);
        
        res.json({
            success: success,
            message: success ? 'Security alert sent successfully' : 'Failed to send security alert'
        });
    } catch (error) {
        console.error('Security alert error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to send security alert',
            error: error.message
        });
    }
});

module.exports = router;
