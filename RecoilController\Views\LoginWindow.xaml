<Window x:Class="RecoilController.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Octane Recoil - Professional Authentication"
        Height="550" Width="450"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF6600"/>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#e55a00"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#cc5200"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="Foreground" Value="#000000"/>
            <Setter Property="BorderBrush" Value="#666666"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="5,5"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="CaretBrush" Value="#000000"/>
            <Setter Property="SelectionBrush" Value="#FF6600"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ScrollViewer x:Name="PART_ContentHost"
                                         Margin="{TemplateBinding Padding}"
                                         VerticalAlignment="Center"
                                         HorizontalAlignment="Stretch"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#FF6600"/>
                                <Setter Property="Background" Value="#FFFFFF"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#FF6600"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Container with Gradient Background -->
    <Border CornerRadius="15">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#0a0a0a" Offset="0"/>
                <GradientStop Color="#1a1a1a" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Border.Effect>
            <DropShadowEffect Color="Black" BlurRadius="20" ShadowDepth="0" Opacity="0.8"/>
        </Border.Effect>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="70"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- Header with Drag Support -->
            <Border Grid.Row="0" Background="Transparent" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="⚡" FontSize="28" Margin="0,0,15,0" Foreground="#FFD700"/>
                        <StackPanel>
                            <TextBlock Text="OCTANE" FontSize="24" FontWeight="Bold" Foreground="#FF6600"/>
                            <TextBlock Text="Recoil Scripts" FontSize="11" Foreground="#888" Margin="0,-5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                    <Button Name="CloseButton" Content="×" Width="35" Height="35"
                            HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,10,15,0"
                            Background="Transparent" Foreground="#888" BorderThickness="0"
                            FontSize="20" FontWeight="Bold" Click="CloseButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="17">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#FF4444"/>
                                                    <Setter Property="Foreground" Value="White"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>
            
            <!-- Main Content -->
            <StackPanel Grid.Row="1" Margin="50,20" VerticalAlignment="Center">
                <!-- Welcome Section -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <TextBlock Text="Welcome Back" FontSize="22" FontWeight="Bold" Foreground="White"
                              HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock Text="Enter your license key to continue" FontSize="13" Foreground="#888"
                              HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- License Key Input -->
                <StackPanel Margin="0,0,0,25">
                    <TextBlock Text="License Key" FontSize="12" FontWeight="Bold" Foreground="#BBB"
                              Margin="0,0,0,8"/>
                    <TextBox Name="LicenseKeyTextBox" Height="50"
                             Style="{StaticResource ModernTextBoxStyle}"
                             Text="" Margin="0,0,0,0"
                             FontFamily="Consolas"
                             CharacterCasing="Upper"
                             MaxLength="11"
                             ToolTip="Enter your license key (XXX-XXX-XXX format)"
                             TabIndex="1"
                             IsTabStop="True"/>
                </StackPanel>

                <!-- Remember Me -->
                <CheckBox Name="RememberMeCheckBox" Margin="0,0,0,30" HorizontalAlignment="Left">
                    <CheckBox.Style>
                        <Style TargetType="CheckBox">
                            <Setter Property="Foreground" Value="#BBB"/>
                            <Setter Property="FontSize" Value="12"/>
                        </Style>
                    </CheckBox.Style>
                    Remember this device
                </CheckBox>

                <!-- Login Button -->
                <Button Name="LoginButton" Height="50"
                        Style="{StaticResource ModernButtonStyle}"
                        Click="LoginButton_Click" Margin="0,0,0,20">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🚀" FontSize="16" Margin="0,0,10,0"/>
                        <TextBlock Text="AUTHENTICATE" FontWeight="Bold"/>
                    </StackPanel>
                </Button>

                <!-- Status Message -->
                <TextBlock Name="StatusTextBlock" Text="" FontSize="12"
                          HorizontalAlignment="Center" Margin="0,10,0,0"
                          Foreground="#FF6B6B"/>
            </StackPanel>
            
            <!-- Footer -->
            <Border Grid.Row="2" Background="Transparent">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="Octane Recoil Scripts v4.2.1"
                              FontSize="10" Foreground="#666"
                              HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock Text="Secure License Authentication System"
                              FontSize="9" Foreground="#444"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
