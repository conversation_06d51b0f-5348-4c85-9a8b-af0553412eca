{"Security": {"Debug": {"EnableSecurityAlerts": false, "EnableDiscordWebhooks": false, "EnableAntiDebugger": false, "EnableProcessMonitoring": false, "EnableHardwareFingerprinting": false, "AlertCooldownSeconds": 0, "MonitoredProcesses": [], "BlockedProcesses": []}, "Production": {"EnableSecurityAlerts": true, "EnableDiscordWebhooks": true, "EnableAntiDebugger": true, "EnableProcessMonitoring": true, "EnableHardwareFingerprinting": true, "AlertCooldownSeconds": 300, "MonitoredProcesses": ["x64dbg", "x32dbg", "ollydbg", "windbg", "ida", "ida64", "cheatengine", "processhacker", "procmon", "wireshark", "fiddler", "burpsuite", "dnspy", "reflexil", "ilspy", "dotpeek", "jetbrains.dotpeek", "hxd", "010editor", "g<PERSON><PERSON>", "radare2", "cutter", "immunity", "softice", "apimonitor", "detours", "hookshark", "roh<PERSON><PERSON>", "lord<PERSON>", "peid", "exeinfope", "pestudio", "die", "resourcehacker", "reshacker", "xn_resource_editor"], "BlockedProcesses": ["x64dbg", "x32dbg", "ollydbg", "cheatengine", "dnspy", "reflexil", "hxd", "immunity", "softice"]}}, "AdminPanel": {"ApiUrl": "https://your-admin-panel.com/api", "SecurityAlertsEndpoint": "/security-alerts", "SecurityActionEndpoint": "/security-action"}, "Discord": {"WebhookUrl": "ENCRYPTED_IN_CODE", "Username": "Octane Security", "AvatarUrl": "https://cdn.discordapp.com/emojis/1234567890123456789.png"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}