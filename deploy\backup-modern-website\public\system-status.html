<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status - Octane Recoil Scripts</title>
    <link rel="stylesheet" href="css/admin-modern.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-crosshairs"></i>
                    <span>Octane Admin</span>
                </div>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="/admin" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/key-maintenance" class="nav-link">
                        <i class="fas fa-key"></i>
                        <span>License Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/user-management" class="nav-link">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/discord" class="nav-link">
                        <i class="fab fa-discord"></i>
                        <span>Discord Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/security-alerts" class="nav-link">
                        <i class="fas fa-shield-alt"></i>
                        <span>Security Alerts</span>
                    </a>
                </li>
                <li class="nav-item active">
                    <a href="/system-status" class="nav-link">
                        <i class="fas fa-server"></i>
                        <span>System Status</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reminders" class="nav-link">
                        <i class="fas fa-bell"></i>
                        <span>Reminders</span>
                    </a>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <i class="fas fa-user-shield"></i>
                    <span>Administrator</span>
                </div>
                <div class="logout-btn">
                    <a href="/login" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-server"></i> System Status</h1>
                <p>Monitor server health, performance metrics, and system resources</p>
            </div>

            <div class="dashboard-grid">
                <!-- System Health Overview -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-heartbeat"></i> System Health</h3>
                        <div class="health-indicator" id="system-health">
                            <i class="fas fa-circle"></i>
                            <span>Checking...</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="health-metrics">
                            <div class="metric">
                                <span class="metric-label">Overall Status</span>
                                <span class="metric-value" id="overall-status">Checking...</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Uptime</span>
                                <span class="metric-value" id="system-uptime">0h 0m</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Load Average</span>
                                <span class="metric-value" id="load-average">0.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CPU Usage -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-microchip"></i> CPU Usage</h3>
                    </div>
                    <div class="card-content">
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="cpu-progress" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">
                                <span id="cpu-percentage">0%</span>
                                <span id="cpu-cores">0 cores</span>
                            </div>
                        </div>
                        <div class="metric-details">
                            <div class="metric">
                                <span class="metric-label">User</span>
                                <span class="metric-value" id="cpu-user">0%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">System</span>
                                <span class="metric-value" id="cpu-system">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Memory Usage -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-memory"></i> Memory Usage</h3>
                    </div>
                    <div class="card-content">
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="memory-progress" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">
                                <span id="memory-percentage">0%</span>
                                <span id="memory-total">0 GB</span>
                            </div>
                        </div>
                        <div class="metric-details">
                            <div class="metric">
                                <span class="metric-label">Used</span>
                                <span class="metric-value" id="memory-used">0 MB</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Free</span>
                                <span class="metric-value" id="memory-free">0 MB</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disk Usage -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-hdd"></i> Disk Usage</h3>
                    </div>
                    <div class="card-content">
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="disk-progress" style="width: 0%"></div>
                            </div>
                            <div class="progress-text">
                                <span id="disk-percentage">0%</span>
                                <span id="disk-total">0 GB</span>
                            </div>
                        </div>
                        <div class="metric-details">
                            <div class="metric">
                                <span class="metric-label">Used</span>
                                <span class="metric-value" id="disk-used">0 GB</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Available</span>
                                <span class="metric-value" id="disk-available">0 GB</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Network Statistics -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-network-wired"></i> Network</h3>
                    </div>
                    <div class="card-content">
                        <div class="metric-details">
                            <div class="metric">
                                <span class="metric-label">Bytes In</span>
                                <span class="metric-value" id="network-in">0 MB</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Bytes Out</span>
                                <span class="metric-value" id="network-out">0 MB</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Connections</span>
                                <span class="metric-value" id="network-connections">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Process Information -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-tasks"></i> Process Info</h3>
                    </div>
                    <div class="card-content">
                        <div class="metric-details">
                            <div class="metric">
                                <span class="metric-label">Node.js PID</span>
                                <span class="metric-value" id="process-pid">-</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Memory Usage</span>
                                <span class="metric-value" id="process-memory">0 MB</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">CPU Usage</span>
                                <span class="metric-value" id="process-cpu">0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Status -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-cogs"></i> Service Status</h3>
                        <button id="refresh-services" class="btn btn-secondary">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="services-grid">
                            <div class="service-item">
                                <div class="service-icon">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="service-info">
                                    <h4>Authentication API</h4>
                                    <div class="service-status" id="api-status">
                                        <i class="fas fa-circle"></i>
                                        <span>Checking...</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="service-item">
                                <div class="service-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="service-info">
                                    <h4>Database</h4>
                                    <div class="service-status" id="database-status">
                                        <i class="fas fa-circle"></i>
                                        <span>Checking...</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="service-item">
                                <div class="service-icon">
                                    <i class="fab fa-discord"></i>
                                </div>
                                <div class="service-info">
                                    <h4>Discord Bot</h4>
                                    <div class="service-status" id="discord-status">
                                        <i class="fas fa-circle"></i>
                                        <span>Checking...</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="service-item">
                                <div class="service-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="service-info">
                                    <h4>Security Monitor</h4>
                                    <div class="service-status" id="security-status">
                                        <i class="fas fa-circle"></i>
                                        <span>Checking...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Logs -->
                <div class="dashboard-card full-width">
                    <div class="card-header">
                        <h3><i class="fas fa-file-alt"></i> Recent System Logs</h3>
                        <div class="card-actions">
                            <select id="log-level">
                                <option value="">All Levels</option>
                                <option value="error">Error</option>
                                <option value="warn">Warning</option>
                                <option value="info">Info</option>
                            </select>
                            <button id="refresh-logs" class="btn btn-secondary">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button id="clear-logs" class="btn btn-warning">
                                <i class="fas fa-trash"></i> Clear
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="logs-container" id="logs-container">
                            <div class="loading-state">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>Loading logs...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/shared-utils.js"></script>
    <script>
        // System Status specific functionality
        let refreshInterval;

        document.addEventListener('DOMContentLoaded', function() {
            AdminNav.init();
            loadSystemStatus();
            loadServiceStatus();
            loadSystemLogs();
            setupEventListeners();
            startAutoRefresh();
        });

        function setupEventListeners() {
            document.getElementById('refresh-services').addEventListener('click', loadServiceStatus);
            document.getElementById('refresh-logs').addEventListener('click', loadSystemLogs);
            document.getElementById('clear-logs').addEventListener('click', clearSystemLogs);
            document.getElementById('log-level').addEventListener('change', loadSystemLogs);
        }

        function startAutoRefresh() {
            // Refresh system metrics every 5 seconds
            refreshInterval = setInterval(() => {
                loadSystemStatus();
            }, 5000);
        }

        async function loadSystemStatus() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/system-status');
                if (response.success) {
                    updateSystemMetrics(response.metrics);
                }
            } catch (error) {
                console.error('Failed to load system status:', error);
                updateSystemHealth('error', 'System Error');
            }
        }

        function updateSystemMetrics(metrics) {
            // Update system health
            const healthStatus = metrics.cpu < 80 && metrics.memory < 90 && metrics.disk < 95 ? 'healthy' : 'warning';
            updateSystemHealth(healthStatus, healthStatus === 'healthy' ? 'Healthy' : 'Warning');

            // Update uptime
            document.getElementById('system-uptime').textContent = AdminUtils.formatUptime(metrics.uptime || 0);
            document.getElementById('load-average').textContent = (metrics.loadAverage || 0).toFixed(2);

            // Update CPU
            updateProgressBar('cpu', metrics.cpu || 0);
            document.getElementById('cpu-cores').textContent = `${metrics.cpuCores || 0} cores`;
            document.getElementById('cpu-user').textContent = `${(metrics.cpuUser || 0).toFixed(1)}%`;
            document.getElementById('cpu-system').textContent = `${(metrics.cpuSystem || 0).toFixed(1)}%`;

            // Update Memory
            updateProgressBar('memory', metrics.memory || 0);
            document.getElementById('memory-total').textContent = formatBytes(metrics.memoryTotal || 0);
            document.getElementById('memory-used').textContent = formatBytes(metrics.memoryUsed || 0);
            document.getElementById('memory-free').textContent = formatBytes(metrics.memoryFree || 0);

            // Update Disk
            updateProgressBar('disk', metrics.disk || 0);
            document.getElementById('disk-total').textContent = formatBytes(metrics.diskTotal || 0);
            document.getElementById('disk-used').textContent = formatBytes(metrics.diskUsed || 0);
            document.getElementById('disk-available').textContent = formatBytes(metrics.diskAvailable || 0);

            // Update Network
            document.getElementById('network-in').textContent = formatBytes(metrics.networkIn || 0);
            document.getElementById('network-out').textContent = formatBytes(metrics.networkOut || 0);
            document.getElementById('network-connections').textContent = metrics.networkConnections || 0;

            // Update Process Info
            document.getElementById('process-pid').textContent = metrics.processPid || process.pid || '-';
            document.getElementById('process-memory').textContent = formatBytes(metrics.processMemory || 0);
            document.getElementById('process-cpu').textContent = `${(metrics.processCpu || 0).toFixed(1)}%`;
        }

        function updateSystemHealth(status, text) {
            const healthIndicator = document.getElementById('system-health');
            const statusElement = document.getElementById('overall-status');
            
            healthIndicator.className = `health-indicator ${status}`;
            healthIndicator.querySelector('span').textContent = text;
            statusElement.textContent = text;
        }

        function updateProgressBar(type, percentage) {
            const progressFill = document.getElementById(`${type}-progress`);
            const percentageElement = document.getElementById(`${type}-percentage`);
            
            progressFill.style.width = `${Math.min(percentage, 100)}%`;
            percentageElement.textContent = `${percentage.toFixed(1)}%`;
            
            // Update color based on usage
            progressFill.className = 'progress-fill';
            if (percentage > 90) {
                progressFill.classList.add('danger');
            } else if (percentage > 70) {
                progressFill.classList.add('warning');
            } else {
                progressFill.classList.add('success');
            }
        }

        async function loadServiceStatus() {
            try {
                const response = await AdminUtils.apiCall('/api/admin/service-status');
                if (response.success) {
                    updateServiceStatus(response.services);
                }
            } catch (error) {
                console.error('Failed to load service status:', error);
                AdminUtils.showNotification('Failed to load service status', 'error');
            }
        }

        function updateServiceStatus(services) {
            const serviceElements = {
                'api-status': services.api || { status: 'unknown', message: 'Unknown' },
                'database-status': services.database || { status: 'unknown', message: 'Unknown' },
                'discord-status': services.discord || { status: 'unknown', message: 'Unknown' },
                'security-status': services.security || { status: 'unknown', message: 'Unknown' }
            };

            Object.entries(serviceElements).forEach(([elementId, service]) => {
                const element = document.getElementById(elementId);
                element.className = `service-status ${service.status}`;
                element.querySelector('span').textContent = service.message;
            });
        }

        async function loadSystemLogs() {
            try {
                const level = document.getElementById('log-level').value;
                const params = level ? `?level=${level}` : '';
                
                AdminUtils.showLoading('logs-container', 'Loading logs...');
                
                const response = await AdminUtils.apiCall(`/api/admin/system-logs${params}`);
                if (response.success) {
                    displaySystemLogs(response.logs);
                }
            } catch (error) {
                console.error('Failed to load system logs:', error);
                document.getElementById('logs-container').innerHTML = 
                    '<div class="error-state">Failed to load system logs</div>';
            }
        }

        function displaySystemLogs(logs) {
            const container = document.getElementById('logs-container');
            
            if (logs.length === 0) {
                container.innerHTML = '<div class="empty-state"><i class="fas fa-file-alt"></i><p>No logs found</p></div>';
                return;
            }

            container.innerHTML = `
                <div class="logs-list">
                    ${logs.map(log => `
                        <div class="log-entry ${log.level}">
                            <div class="log-timestamp">${AdminUtils.formatTime(log.timestamp)}</div>
                            <div class="log-level">${log.level.toUpperCase()}</div>
                            <div class="log-message">${log.message}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        async function clearSystemLogs() {
            if (!confirm('Are you sure you want to clear all system logs?')) return;
            
            try {
                const response = await AdminUtils.apiCall('/api/admin/system-logs', {
                    method: 'DELETE'
                });
                
                if (response.success) {
                    AdminUtils.showNotification('System logs cleared', 'success');
                    loadSystemLogs();
                } else {
                    throw new Error(response.message);
                }
            } catch (error) {
                AdminUtils.showNotification('Failed to clear logs: ' + error.message, 'error');
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        });
    </script>
</body>
</html>
