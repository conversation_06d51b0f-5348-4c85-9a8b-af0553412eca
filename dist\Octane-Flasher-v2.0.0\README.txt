Octane ESP32-S2 HID Mouse Flasher v2.0.0
=============================================

QUICK START:
1. Put your ESP32-S2 in download mode:
   - Hold BOOT button
   - Plug in USB while holding BOOT
   - Release BOOT button

2. Run OctaneFlasher.exe
3. Enter your license key
4. Follow the on-screen instructions

REQUIREMENTS:
- Windows 10/11
- ESP32-S2 board
- USB cable
- Valid Octane license key

FILES INCLUDED:
- OctaneFlasher.exe          (Main flasher application)
- octane_auth_firmware.bin   (ESP32-S2 HID mouse firmware)
- bootloader.bin             (ESP32-S2 bootloader)
- partitions.bin             (Partition table)
- esptool.exe                (ESP32 flashing tool - optional)

TROUBLESHOOTING:
- If ESP32 not detected: Check download mode and USB connection
- If flashing fails: Try different USB port or cable
- If license fails: Check internet connection and key validity

SUPPORT:
For technical support, contact the Octane team.

Copyright (c) 2024 Octane Team. All rights reserved.
