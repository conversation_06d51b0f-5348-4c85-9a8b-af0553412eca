
/*
 * Real ESP32-S2 LED Blink Firmware
 * Compiled with Arduino CLI for proper ESP32-S2 compatibility
 */

#define LED_PIN 15

void setup() {
  Serial.begin(115200);
  pinMode(LED_PIN, OUTPUT);
  
  Serial.println("Octane ESP32-S2 LED Test Firmware v1.0");
  Serial.println("Hardware: ESP32-S2 Mini");
  Serial.println("Firmware loaded successfully!");
  
  // Boot sequence - 3 quick flashes
  for (int i = 0; i < 3; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(200);
    digitalWrite(LED_PIN, LOW);
    delay(200);
  }
  
  Serial.println("Starting LED blink loop...");
}

void loop() {
  static unsigned long lastPrint = 0;
  static unsigned long bootTime = millis();
  
  // Blink LED
  digitalWrite(LED_PIN, HIGH);
  delay(500);
  digitalWrite(LED_PIN, LOW);
  delay(500);
  
  // Print status every 10 seconds
  if (millis() - lastPrint > 10000) {
    unsigned long uptime = (millis() - bootTime) / 1000;
    Serial.print("Uptime: ");
    Serial.print(uptime);
    Serial.println(" seconds - LED blinking");
    lastPrint = millis();
  }
}
