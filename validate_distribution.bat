@echo off
setlocal enabledelayedexpansion

echo ╔══════════════════════════════════════════════════════════════╗
echo ║            🔍 OCTANE DISTRIBUTION VALIDATOR 🔍              ║
echo ║                      by Octane Team                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Validating distribution files...
echo.

REM Define file requirements
set "FLASHER_FILE=OctaneFlasher\OctaneFlasher.exe"
set "FIRMWARE_FILE=OctaneFlasher\octane_auth_firmware.bin"
set "BOOTLOADER_FILE=OctaneFlasher\bootloader.bin"
set "PARTITIONS_FILE=OctaneFlasher\partitions.bin"

REM Define minimum file sizes (in bytes)
set FLASHER_MIN_SIZE=500000
set FIRMWARE_MIN_SIZE=50000
set BOOTLOADER_MIN_SIZE=20000
set PARTITIONS_MIN_SIZE=1000

REM Define maximum file sizes (in bytes)
set FLASHER_MAX_SIZE=10485760
set FIRMWARE_MAX_SIZE=1048576
set BOOTLOADER_MAX_SIZE=104857
set PARTITIONS_MAX_SIZE=10240

set VALIDATION_PASSED=1
set TOTAL_ISSUES=0

echo 📁 CORE FILES VALIDATION
echo ========================

REM Validate OctaneFlasher.exe
if exist "%FLASHER_FILE%" (
    for %%F in ("%FLASHER_FILE%") do set FLASHER_SIZE=%%~zF
    
    if !FLASHER_SIZE! GEQ %FLASHER_MIN_SIZE% (
        if !FLASHER_SIZE! LEQ %FLASHER_MAX_SIZE% (
            echo ✅ OctaneFlasher.exe: !FLASHER_SIZE! bytes ^(Valid^)
        ) else (
            echo ❌ OctaneFlasher.exe: !FLASHER_SIZE! bytes ^(Too large, max: %FLASHER_MAX_SIZE%^)
            set VALIDATION_PASSED=0
            set /a TOTAL_ISSUES+=1
        )
    ) else (
        echo ❌ OctaneFlasher.exe: !FLASHER_SIZE! bytes ^(Too small, min: %FLASHER_MIN_SIZE%^)
        set VALIDATION_PASSED=0
        set /a TOTAL_ISSUES+=1
    )
) else (
    echo ❌ OctaneFlasher.exe: NOT FOUND
    set VALIDATION_PASSED=0
    set /a TOTAL_ISSUES+=1
)

REM Validate firmware file
if exist "%FIRMWARE_FILE%" (
    for %%F in ("%FIRMWARE_FILE%") do set FIRMWARE_SIZE=%%~zF
    
    if !FIRMWARE_SIZE! GEQ %FIRMWARE_MIN_SIZE% (
        if !FIRMWARE_SIZE! LEQ %FIRMWARE_MAX_SIZE% (
            echo ✅ octane_auth_firmware.bin: !FIRMWARE_SIZE! bytes ^(Valid^)
        ) else (
            echo ❌ octane_auth_firmware.bin: !FIRMWARE_SIZE! bytes ^(Too large, max: %FIRMWARE_MAX_SIZE%^)
            set VALIDATION_PASSED=0
            set /a TOTAL_ISSUES+=1
        )
    ) else (
        echo ❌ octane_auth_firmware.bin: !FIRMWARE_SIZE! bytes ^(Too small, min: %FIRMWARE_MIN_SIZE%^)
        set VALIDATION_PASSED=0
        set /a TOTAL_ISSUES+=1
    )
) else (
    echo ❌ octane_auth_firmware.bin: NOT FOUND
    set VALIDATION_PASSED=0
    set /a TOTAL_ISSUES+=1
)

REM Validate bootloader file
if exist "%BOOTLOADER_FILE%" (
    for %%F in ("%BOOTLOADER_FILE%") do set BOOTLOADER_SIZE=%%~zF
    
    if !BOOTLOADER_SIZE! GEQ %BOOTLOADER_MIN_SIZE% (
        if !BOOTLOADER_SIZE! LEQ %BOOTLOADER_MAX_SIZE% (
            echo ✅ bootloader.bin: !BOOTLOADER_SIZE! bytes ^(Valid^)
        ) else (
            echo ❌ bootloader.bin: !BOOTLOADER_SIZE! bytes ^(Too large, max: %BOOTLOADER_MAX_SIZE%^)
            set VALIDATION_PASSED=0
            set /a TOTAL_ISSUES+=1
        )
    ) else (
        echo ❌ bootloader.bin: !BOOTLOADER_SIZE! bytes ^(Too small, min: %BOOTLOADER_MIN_SIZE%^)
        set VALIDATION_PASSED=0
        set /a TOTAL_ISSUES+=1
    )
) else (
    echo ❌ bootloader.bin: NOT FOUND
    set VALIDATION_PASSED=0
    set /a TOTAL_ISSUES+=1
)

REM Validate partitions file
if exist "%PARTITIONS_FILE%" (
    for %%F in ("%PARTITIONS_FILE%") do set PARTITIONS_SIZE=%%~zF
    
    if !PARTITIONS_SIZE! GEQ %PARTITIONS_MIN_SIZE% (
        if !PARTITIONS_SIZE! LEQ %PARTITIONS_MAX_SIZE% (
            echo ✅ partitions.bin: !PARTITIONS_SIZE! bytes ^(Valid^)
        ) else (
            echo ❌ partitions.bin: !PARTITIONS_SIZE! bytes ^(Too large, max: %PARTITIONS_MAX_SIZE%^)
            set VALIDATION_PASSED=0
            set /a TOTAL_ISSUES+=1
        )
    ) else (
        echo ❌ partitions.bin: !PARTITIONS_SIZE! bytes ^(Too small, min: %PARTITIONS_MIN_SIZE%^)
        set VALIDATION_PASSED=0
        set /a TOTAL_ISSUES+=1
    )
) else (
    echo ❌ partitions.bin: NOT FOUND
    set VALIDATION_PASSED=0
    set /a TOTAL_ISSUES+=1
)

echo.
echo 🔧 OPTIONAL FILES VALIDATION
echo ============================

REM Check optional esptool
if exist "OctaneFlasher\esptool.exe" (
    for %%F in ("OctaneFlasher\esptool.exe") do set ESPTOOL_SIZE=%%~zF
    echo ✅ esptool.exe: !ESPTOOL_SIZE! bytes ^(Optional - Present^)
) else (
    echo ⚠️ esptool.exe: NOT FOUND ^(Optional - Will use system esptool^)
)

echo.
echo 📊 DISTRIBUTION STATISTICS
echo ==========================

REM Calculate total size
set TOTAL_SIZE=0
if defined FLASHER_SIZE set /a TOTAL_SIZE+=!FLASHER_SIZE!
if defined FIRMWARE_SIZE set /a TOTAL_SIZE+=!FIRMWARE_SIZE!
if defined BOOTLOADER_SIZE set /a TOTAL_SIZE+=!BOOTLOADER_SIZE!
if defined PARTITIONS_SIZE set /a TOTAL_SIZE+=!PARTITIONS_SIZE!

echo Total core files size: %TOTAL_SIZE% bytes
set /a TOTAL_MB=%TOTAL_SIZE%/1048576
echo Total core files size: %TOTAL_MB% MB

echo.
echo 🔍 INTEGRITY CHECKS
echo ===================

REM Check if files are not empty and have valid headers
if exist "%FIRMWARE_FILE%" (
    REM Check if firmware file starts with valid ESP32 header
    powershell -Command "if ((Get-Content '%FIRMWARE_FILE%' -Encoding Byte -TotalCount 4 | ForEach-Object { '{0:X2}' -f $_ }) -join '' -eq 'E9000000') { Write-Host '✅ Firmware header: Valid ESP32 binary' } else { Write-Host '⚠️ Firmware header: May not be valid ESP32 binary' }"
)

if exist "%BOOTLOADER_FILE%" (
    REM Check if bootloader file starts with valid ESP32 header
    powershell -Command "if ((Get-Content '%BOOTLOADER_FILE%' -Encoding Byte -TotalCount 4 | ForEach-Object { '{0:X2}' -f $_ }) -join '' -eq 'E9000000') { Write-Host '✅ Bootloader header: Valid ESP32 binary' } else { Write-Host '⚠️ Bootloader header: May not be valid ESP32 binary' }"
)

echo.
echo 🎯 VALIDATION SUMMARY
echo ====================

if %VALIDATION_PASSED% EQU 1 (
    echo ✅ VALIDATION PASSED
    echo    All required files are present and valid
    echo    Distribution is ready for release
) else (
    echo ❌ VALIDATION FAILED
    echo    Found %TOTAL_ISSUES% issue^(s^) that need to be resolved
    echo.
    echo 🔧 RECOMMENDED ACTIONS:
    echo    1. Build firmware: cd esp32-firmware ^&^& build.bat
    echo    2. Build flasher: cd OctaneFlasher ^&^& dotnet publish -c Release
    echo    3. Run validation again
)

echo.
echo 📋 DISTRIBUTION CHECKLIST
echo =========================
echo [ ] All core files present and valid sizes
echo [ ] Firmware tested on ESP32-S2 hardware
echo [ ] Flasher tested on clean Windows system
echo [ ] License validation working
echo [ ] Documentation updated
echo [ ] Version numbers consistent
echo [ ] Archive created and tested
echo [ ] Upload to distribution channels
echo.

if %VALIDATION_PASSED% EQU 0 (
    echo ❌ Distribution validation failed - do not release
    pause
    exit /b 1
) else (
    echo ✅ Distribution validation passed - ready for release
    pause
    exit /b 0
)
