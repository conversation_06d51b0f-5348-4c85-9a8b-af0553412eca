<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Octane Admin Panel - Key Maintenance</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/main-theme.css">
    <link rel="stylesheet" href="/css/key-maintenance.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Admin Panel -->
        <div id="adminPanel" class="admin-panel">
            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>📊 Admin Dashboard</h2>
                    <div class="nav-buttons">
                        <a href="/key-maintenance" class="btn btn-primary">🔧 Key Maintenance</a>
                        <a href="/user-management" class="btn btn-primary">👥 User Management</a>
                        <a href="/discord" class="btn btn-primary">🤖 Discord Management</a>
                        <a href="/security-alerts" class="btn btn-primary">🛡️ Security Alerts</a>
                        <a href="/system-status" class="btn btn-primary">📊 System Status</a>
                        <a href="/settings" class="btn btn-primary">⚙️ Settings</a>
                        <a href="/reminders" class="btn btn-primary">📋 Reminders</a>
                        <button id="logoutBtn" class="btn btn-danger">🚪 Logout</button>
                    </div>
                </div>
            </div>
        
        <div id="alertContainer"></div>


            <!-- Panel Header -->
            <div class="card">
                <div class="card-header">
                    <h2>🔧 Key Maintenance Dashboard</h2>
                    <div class="header-stats">
                        <div class="stat-item">
                            <span class="stat-value" id="totalKeys">0</span>
                            <span class="stat-label">Total Keys</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="activeKeys">0</span>
                            <span class="stat-label">Active</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value" id="expiredKeys">0</span>
                            <span class="stat-label">Expired</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create New License Section -->
            <div class="card">
                <div class="card-header">
                    <h3>🆕 Create New License</h3>
                    <p>Generate a new license key with custom settings</p>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div class="form-group">
                            <label for="duration">Duration:</label>
                            <select id="duration" class="form-control">
                                <option value="1day">1 Day</option>
                                <option value="1week">1 Week</option>
                                <option value="1month" selected>1 Month</option>
                                <option value="3months">3 Months</option>
                                <option value="6months">6 Months</option>
                                <option value="1year">1 Year</option>
                                <option value="lifetime">Lifetime</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="notes">Notes (Optional):</label>
                            <input type="text" id="notes" class="form-control" placeholder="Customer name, order ID, etc.">
                        </div>
                        <div class="form-group">
                            <button id="createLicenseBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create License
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3>⚡ Quick Actions</h3>
                </div>
                <div class="card-content">
                    <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                        <button class="btn btn-secondary" onclick="refreshLicenses()">
                            <i class="fas fa-sync-alt"></i> Refresh List
                        </button>
                        <button class="btn btn-warning" onclick="deleteExpired()">
                            <i class="fas fa-trash"></i> Delete Expired
                        </button>
                        <button class="btn btn-info" onclick="exportLicenses()">
                            <i class="fas fa-download"></i> Export CSV
                        </button>
                        <button class="btn btn-success" onclick="bulkCreate()">
                            <i class="fas fa-layer-group"></i> Bulk Create
                        </button>
                    </div>
                </div>
            </div>

            <!-- License List -->
            <div class="card">
                <div class="card-header">
                    <h3>📋 All Licenses</h3>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="searchInput" placeholder="🔍 Search licenses..." class="form-control" style="max-width: 300px;">
                        <button class="btn btn-secondary" onclick="refreshLicenses()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>License Key</th>
                                    <th>Duration</th>
                                    <th>Status</th>
                                    <th>HWID</th>
                                    <th>Created</th>
                                    <th>Expires</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="licensesTableBody">
                                <tr>
                                    <td colspan="8" class="loading">
                                        <i class="fas fa-spinner fa-spin"></i> Loading licenses...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/shared-utils.js"></script>
    <script src="/js/key-maintenance.js"></script>
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyCIEOWu0aXUtLBekgs5Xtjs8V8pdDIsTSY",
            authDomain: "authenticator-678a2.firebaseapp.com",
            projectId: "authenticator-678a2",
            storageBucket: "authenticator-678a2.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef123456"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Check authentication
        firebase.auth().onAuthStateChanged((user) => {
            if (!user) {
                window.location.href = '/';
            } else {
                // User is authenticated, load page content
                loadStats();
                loadLicenses();
            }
        });

        // Logout functionality
        document.getElementById('logoutBtn').addEventListener('click', () => {
            OctaneAuth.logout();
        });
    </script>
</body>
</html>
