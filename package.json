{"name": "octane-auth-backend", "version": "1.0.0", "description": "Authentication backend for Octane recoil control system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["octane", "authentication", "license", "api"], "author": "Lag", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "discord.js": "^14.21.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}