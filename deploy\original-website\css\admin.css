/* Octane Admin Panel - Dark Theme with Subtle Orange Accents */
:root {
    --primary-color: #FF6600;
    --primary-dark: #e55a00;
    --secondary-color: #FF8C00;
    --bg-dark: #0a0a0a;
    --bg-card: #1a1a1a;
    --bg-secondary: #252525;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: #333333;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.4);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-dark) 0%, #1a1a1a 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.header h1 {
    color: var(--primary-color);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(255, 102, 0, 0.3);
}

.header .subtitle {
    color: var(--text-secondary);
    font-size: 1.2rem;
    font-weight: 300;
}

/* Login Form */
.login-form {
    max-width: 400px;
    margin: 0 auto;
    background: var(--bg-card);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.login-form h2 {
    color: var(--primary-color);
    margin-bottom: 30px;
    text-align: center;
    font-size: 1.8rem;
}

/* Form Elements */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px 16px;
    background: var(--bg-dark);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition);
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.1);
}

/* Buttons */
.btn {
    background: var(--primary-color);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: #333;
}

.btn-danger {
    background: var(--danger-color);
}

.btn-danger:hover {
    background: #c82333;
}

.btn-success {
    background: var(--success-color);
}

.btn-success:hover {
    background: #218838;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* Admin Panel */
.admin-panel {
    display: none;
}

.admin-panel.active {
    display: block;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.panel-header h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
    padding: 25px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card h3 {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.stat-card p {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

/* Create License Section */
.create-license-section {
    background: var(--bg-card);
    padding: 30px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.create-license-section h3 {
    color: var(--primary-color);
    margin-bottom: 25px;
    font-size: 1.5rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* Duration Selector */
.duration-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.duration-btn {
    padding: 12px;
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    font-weight: 500;
}

.duration-btn:hover {
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.duration-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Licenses Table */
.licenses-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.licenses-header {
    padding: 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.licenses-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.licenses-table {
    width: 100%;
    border-collapse: collapse;
}

.licenses-table th, .licenses-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.licenses-table th {
    background: var(--bg-secondary);
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.licenses-table tr:hover {
    background: rgba(255, 102, 0, 0.05);
}

.license-key {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: var(--bg-dark);
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-inactive {
    background: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.status-expired {
    background: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

/* Actions */
.actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* Alerts */
.alert {
    padding: 15px 20px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    font-weight: 500;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left-color: var(--success-color);
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-left-color: var(--info-color);
}

/* Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .duration-selector {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .actions {
        flex-direction: column;
    }
    
    .licenses-table {
        font-size: 12px;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Hidden */
.hidden {
    display: none !important;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-card);
    padding: 30px;
    border-radius: var(--border-radius);
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.modal-content h3 {
    margin-top: 0;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.modal-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Discord Status */
.stat-card small {
    display: block;
    font-size: 0.8em;
    color: var(--text-muted);
    margin-top: 5px;
    font-weight: normal;
}

/* Admin Tabs */
.admin-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 30px;
    border-bottom: 2px solid var(--border-color);
}

.tab-btn {
    padding: 12px 20px;
    background: var(--bg-secondary);
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95em;
    transition: all 0.3s ease;
    border-radius: 8px 8px 0 0;
}

.tab-btn:hover {
    color: var(--text-primary);
    background: var(--bg-card);
    border-bottom-color: var(--text-muted);
}

.tab-btn.active {
    color: var(--text-primary);
    border-bottom-color: var(--text-primary);
    background: var(--bg-card);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Key Maintenance */
.key-maintenance {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 1px solid var(--border-color);
}

.maintenance-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.search-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    color: var(--text-primary);
}

.filter-section {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-section select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    color: var(--text-primary);
}

.bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    flex-wrap: wrap;
}

.selected-count {
    margin-left: auto;
    font-weight: bold;
    color: var(--primary-color);
}

.keys-table-container {
    overflow-x: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.keys-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
}

.keys-table th,
.keys-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.keys-table th {
    background: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
}

.keys-table tr:hover {
    background: var(--bg-secondary);
}

.key-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

.key-status.active { background: #28a745; color: white; }
.key-status.expired { background: #dc3545; color: white; }
.key-status.suspended { background: #ffc107; color: black; }

.key-actions {
    display: flex;
    gap: 5px;
}

.key-actions .btn {
    padding: 4px 8px;
    font-size: 0.8em;
}

.key-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.key-cell {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.key-cell.expandable {
    cursor: pointer;
}

.key-cell.expandable:hover {
    background: var(--bg-secondary);
}

@media (max-width: 768px) {
    .admin-tabs {
        flex-direction: column;
    }

    .maintenance-controls {
        padding: 15px;
    }

    .search-section,
    .filter-section,
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .selected-count {
        margin-left: 0;
        margin-top: 10px;
    }
}

/* Security Alerts Styles */
.security-alerts-section {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 25px;
    border: 1px solid var(--border-color);
}

.security-alerts-section h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.security-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.security-stats .stat-card {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-dark) 100%);
    border: 1px solid var(--border-color);
}

.security-stats .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.security-stats .stat-number.critical {
    color: var(--danger-color);
}

.security-stats .stat-number.warning {
    color: var(--warning-color);
}

.security-stats .stat-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.recent-alerts h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 18px;
}

.alerts-list {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.alert-item {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid;
    border: 1px solid var(--border-color);
}

.alert-item.critical {
    border-left-color: var(--danger-color);
}

.alert-item.high {
    border-left-color: var(--warning-color);
}

.alert-item.medium {
    border-left-color: var(--info-color);
}

.alert-item.low {
    border-left-color: var(--success-color);
}

.alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.alert-title {
    font-weight: 600;
    color: var(--text-primary);
}

.alert-time {
    font-size: 12px;
    color: var(--text-muted);
}

.alert-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.alert-details {
    font-size: 12px;
    color: var(--text-muted);
}

.threat-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.threat-badge.critical {
    background: var(--danger-color);
    color: white;
}

.threat-badge.high {
    background: var(--warning-color);
    color: white;
}

.threat-badge.medium {
    background: var(--info-color);
    color: white;
}

.threat-badge.low {
    background: var(--success-color);
    color: white;
}

/* Security Alerts Specific Styles */
.alert {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: var(--border-radius);
    border-left: 4px solid;
    position: relative;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    border-left-color: var(--info-color);
    color: var(--info-color);
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.config-item:last-child {
    border-bottom: none;
}

.bg-hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Responsive Design for Security Alerts */
@media (max-width: 768px) {
    .security-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        margin-bottom: 10px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn-small {
        margin-bottom: 5px;
    }

    .alert-table {
        font-size: 0.8em;
    }

    .metadata-grid {
        grid-template-columns: 1fr;
    }

    .stats-row {
        grid-template-columns: repeat(2, 1fr);
    }
}
