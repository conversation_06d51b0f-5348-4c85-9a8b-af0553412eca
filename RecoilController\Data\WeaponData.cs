using System.Collections.Generic;
using RecoilController.Models;

namespace RecoilController.Data
{
    /// <summary>
    /// Contains hardcoded weapon data and recoil patterns from latest Rust dumps
    /// </summary>
    public static class WeaponData
    {
        /// <summary>
        /// AK-47 recoil pattern (from latest Rust data dump)
        /// </summary>
        public static readonly Weapon AK47 = new Weapon(
            name: "AK-47",
            id: 1,
            delay: 133, // 133.3ms between shots
            maxBulletCount: 30,
            hipfireScale: 0.83f,
            isAutomatic: true,
            recoilPattern: new List<Vector2>
            {
                new Vector2(0.000000f, -2.257792f),
                new Vector2(0.323242f, -2.300758f),
                new Vector2(0.649593f, -2.299759f),
                new Vector2(0.848786f, -2.259034f),
                new Vector2(1.075408f, -2.323947f),
                new Vector2(1.268491f, -2.215956f),
                new Vector2(1.330963f, -2.236556f),
                new Vector2(1.336833f, -2.218203f),
                new Vector2(1.505516f, -2.143454f),
                new Vector2(1.504423f, -2.233091f),
                new Vector2(1.442116f, -2.270194f),
                new Vector2(1.478543f, -2.204318f),
                new Vector2(1.392874f, -2.165817f),
                new Vector2(1.480824f, -2.177887f),
                new Vector2(1.597069f, -2.270915f),
                new Vector2(1.449996f, -2.145893f),
                new Vector2(1.369179f, -2.270450f),
                new Vector2(1.582363f, -2.298334f),
                new Vector2(1.516872f, -2.235066f),
                new Vector2(1.498249f, -2.238401f),
                new Vector2(1.465769f, -2.331642f),
                new Vector2(1.564812f, -2.242621f),
                new Vector2(1.517519f, -2.303052f),
                new Vector2(1.422433f, -2.211946f),
                new Vector2(1.553195f, -2.248043f),
                new Vector2(1.510463f, -2.285327f),
                new Vector2(1.553878f, -2.240047f),
                new Vector2(1.520380f, -2.221839f),
                new Vector2(1.553878f, -2.240047f),
                new Vector2(1.553195f, -2.248043f)
            }
        );

        /// <summary>
        /// LR-300 Assault Rifle recoil pattern
        /// </summary>
        public static readonly Weapon LR300 = new Weapon(
            name: "LR-300",
            id: 2,
            delay: 120, // 120ms between shots
            maxBulletCount: 30,
            hipfireScale: 0.85f,
            isAutomatic: true,
            recoilPattern: new List<Vector2>
            {
                new Vector2(0.000000f, -1.800000f),
                new Vector2(0.200000f, -1.850000f),
                new Vector2(0.400000f, -1.900000f),
                new Vector2(0.300000f, -1.950000f),
                new Vector2(0.100000f, -2.000000f),
                new Vector2(-0.100000f, -2.050000f),
                new Vector2(-0.300000f, -2.100000f),
                new Vector2(-0.500000f, -2.150000f),
                new Vector2(-0.400000f, -2.200000f),
                new Vector2(-0.200000f, -2.250000f),
                new Vector2(0.000000f, -2.300000f),
                new Vector2(0.200000f, -2.350000f),
                new Vector2(0.400000f, -2.400000f),
                new Vector2(0.300000f, -2.450000f),
                new Vector2(0.100000f, -2.500000f),
                new Vector2(-0.100000f, -2.550000f),
                new Vector2(-0.300000f, -2.600000f),
                new Vector2(-0.500000f, -2.650000f),
                new Vector2(-0.400000f, -2.700000f),
                new Vector2(-0.200000f, -2.750000f),
                new Vector2(0.000000f, -2.800000f),
                new Vector2(0.200000f, -2.850000f),
                new Vector2(0.400000f, -2.900000f),
                new Vector2(0.300000f, -2.950000f),
                new Vector2(0.100000f, -3.000000f),
                new Vector2(-0.100000f, -3.050000f),
                new Vector2(-0.300000f, -3.100000f),
                new Vector2(-0.500000f, -3.150000f),
                new Vector2(-0.400000f, -3.200000f),
                new Vector2(-0.200000f, -3.250000f)
            }
        );

        /// <summary>
        /// MP5A4 SMG recoil pattern
        /// </summary>
        public static readonly Weapon MP5A4 = new Weapon(
            name: "MP5A4",
            id: 3,
            delay: 100, // 100ms between shots
            maxBulletCount: 30,
            hipfireScale: 0.90f,
            isAutomatic: true,
            recoilPattern: new List<Vector2>
            {
                new Vector2(0.000000f, -1.200000f),
                new Vector2(-0.100000f, -1.250000f),
                new Vector2(-0.200000f, -1.300000f),
                new Vector2(-0.300000f, -1.350000f),
                new Vector2(-0.400000f, -1.400000f),
                new Vector2(-0.500000f, -1.450000f),
                new Vector2(-0.600000f, -1.500000f),
                new Vector2(-0.700000f, -1.550000f),
                new Vector2(-0.800000f, -1.600000f),
                new Vector2(-0.700000f, -1.650000f),
                new Vector2(-0.600000f, -1.700000f),
                new Vector2(-0.500000f, -1.750000f),
                new Vector2(-0.400000f, -1.800000f),
                new Vector2(-0.300000f, -1.850000f),
                new Vector2(-0.200000f, -1.900000f),
                new Vector2(-0.100000f, -1.950000f),
                new Vector2(0.000000f, -2.000000f),
                new Vector2(0.100000f, -2.050000f),
                new Vector2(0.200000f, -2.100000f),
                new Vector2(0.300000f, -2.150000f),
                new Vector2(0.400000f, -2.200000f),
                new Vector2(0.300000f, -2.250000f),
                new Vector2(0.200000f, -2.300000f),
                new Vector2(0.100000f, -2.350000f),
                new Vector2(0.000000f, -2.400000f),
                new Vector2(-0.100000f, -2.450000f),
                new Vector2(-0.200000f, -2.500000f),
                new Vector2(-0.300000f, -2.550000f),
                new Vector2(-0.400000f, -2.600000f),
                new Vector2(-0.500000f, -2.650000f)
            }
        );

        /// <summary>
        /// Gets all available weapons
        /// </summary>
        public static List<Weapon> GetAllWeapons()
        {
            return new List<Weapon>
            {
                AK47,
                LR300,
                MP5A4
            };
        }

        /// <summary>
        /// Gets a weapon by its ID
        /// </summary>
        public static Weapon GetWeaponById(int id)
        {
            var weapons = GetAllWeapons();
            return weapons.Find(w => w.Id == id);
        }

        /// <summary>
        /// Gets a weapon by its name
        /// </summary>
        public static Weapon GetWeaponByName(string name)
        {
            var weapons = GetAllWeapons();
            return weapons.Find(w => w.Name.Equals(name, System.StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Attachment modifiers for weapons
        /// </summary>
        public static class AttachmentModifiers
        {
            public static readonly Dictionary<string, float> ScopeModifiers = new Dictionary<string, float>
            {
                { "none", 1.0f },
                { "holo", 0.98f },
                { "red-dot", 0.99f },
                { "acog", 0.95f },
                { "scope", 0.90f }
            };

            public static readonly Dictionary<string, float> MuzzleModifiers = new Dictionary<string, float>
            {
                { "none", 1.0f },
                { "compensator", 0.80f },
                { "flash-hider", 0.90f },
                { "muzzle-brake", 0.50f },
                { "silencer", 0.80f }
            };

            public static readonly Dictionary<string, float> BarrelModifiers = new Dictionary<string, float>
            {
                { "none", 1.0f },
                { "extended", 0.95f },
                { "heavy", 0.85f }
            };
        }
    }
}
