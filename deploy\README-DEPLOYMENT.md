# Octane Authentication Backend - VPS Deployment Guide

Deploy your Octane authentication backend to your Ubuntu 24.04 VPS.

## Server Details
- **IP**: *************
- **OS**: Ubuntu 24.04 + Plesk
- **User**: root
- **Resources**: 1 vCore, 1GB RAM, 10GB NVMe SSD

## Quick Deployment Steps

### 1. Connect to Your VPS
```bash
ssh root@*************
```

### 2. Run Server Setup
```bash
# Download and run setup script
curl -sSL https://raw.githubusercontent.com/your-repo/octane/main/auth-backend/deploy/setup-server.sh | bash

# Or manually upload and run:
chmod +x setup-server.sh
./setup-server.sh
```

### 3. Upload Application Files
```bash
# From your local machine, upload the auth-backend folder:
scp -r auth-backend/* root@*************:/opt/octane-auth/

# Or use git:
cd /opt/octane-auth
git clone https://github.com/your-repo/octane.git .
```

### 4. Deploy Application
```bash
cd /opt/octane-auth
chmod +x deploy/deploy.sh
./deploy/deploy.sh
```

### 5. Test Deployment
```bash
# Check if service is running
pm2 status

# Test API endpoint
curl http://*************/api/health

# Check logs
pm2 logs octane-auth
```

## Access Your Backend

### API Base URL
```
http://*************/api
```

### Admin Panel
```
http://*************/admin
```

### Admin Credentials
- **Username**: admin
- **Password**: (generated during deployment - check .env file)

## SSL Setup (Recommended)

### Option 1: With Domain Name
If you have a domain pointing to *************:
```bash
./deploy/ssl-setup.sh
```

### Option 2: Self-Signed Certificate
For development/testing:
```bash
# Generate self-signed certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/octane.key \
  -out /etc/ssl/certs/octane.crt

# Update Nginx config for SSL
# (Manual configuration required)
```

## Update Desktop Application

Update your desktop app's `AuthenticationService.cs`:
```csharp
private readonly string _apiBaseUrl = "http://*************/api";
// Or with SSL: "https://your-domain.com/api"
```

## Firewall Configuration

Your VPS firewall should allow:
- **Port 22** (SSH)
- **Port 80** (HTTP)
- **Port 443** (HTTPS)
- **Port 3000** (Node.js - optional, Nginx proxies this)

## Monitoring & Maintenance

### Check Application Status
```bash
pm2 status
pm2 logs octane-auth
```

### View System Resources
```bash
htop
df -h
free -h
```

### Restart Application
```bash
sudo -u octane pm2 restart octane-auth
```

### Update Application
```bash
cd /opt/octane-auth
git pull origin main
npm install --production
sudo -u octane pm2 restart octane-auth
```

### Backup Database
```bash
cp /opt/octane-auth/database/octane.json /backup/octane-$(date +%Y%m%d).json
```

## Security Recommendations

### 1. Change Default Credentials
```bash
cd /opt/octane-auth
nano .env
# Update ADMIN_USERNAME and ADMIN_PASSWORD
sudo -u octane pm2 restart octane-auth
```

### 2. Restrict Admin Panel Access
Edit `/etc/nginx/sites-available/octane-auth`:
```nginx
location /admin {
    allow YOUR_IP_ADDRESS;
    deny all;
    # ... rest of config
}
```

### 3. Enable Fail2Ban
```bash
apt install fail2ban
systemctl enable fail2ban
```

### 4. Regular Updates
```bash
apt update && apt upgrade -y
npm audit fix
```

## Troubleshooting

### Application Won't Start
```bash
# Check logs
pm2 logs octane-auth

# Check permissions
ls -la /opt/octane-auth
chown -R octane:octane /opt/octane-auth

# Check Node.js version
node --version  # Should be 18.x
```

### Nginx Issues
```bash
# Test configuration
nginx -t

# Check status
systemctl status nginx

# View error logs
tail -f /var/log/nginx/error.log
```

### Database Issues
```bash
# Check database file
ls -la /opt/octane-auth/database/
cat /opt/octane-auth/database/octane.json

# Reset database (CAUTION: This deletes all data)
rm /opt/octane-auth/database/octane.json
sudo -u octane pm2 restart octane-auth
```

### Port Issues
```bash
# Check what's using port 3000
netstat -tulpn | grep 3000

# Check firewall
ufw status
```

## API Endpoints

Once deployed, your API will be available at:

- `GET /api/health` - Health check
- `POST /api/validate` - License validation
- `POST /api/reset-hwid` - Hardware ID reset
- `POST /api/admin/login` - Admin login
- `GET /api/admin/licenses` - Get all licenses
- `POST /api/admin/licenses` - Create license

## Support

If you encounter issues:
1. Check the logs: `pm2 logs octane-auth`
2. Verify firewall settings: `ufw status`
3. Test API endpoints with curl
4. Check Nginx configuration: `nginx -t`

## VPS Cleanup Commands

If you need to clean up old/unnecessary files on the VPS server, run these commands:

```bash
# Connect to VPS
ssh root@*************

# Remove old/unnecessary files
cd /opt/octane-auth
rm -f deploy.bat deploy-simple.bat deploy.sh upload-to-vps.* test-deployment.bat setup-server.sh ssl-setup.sh
rm -f SETUP-GUIDE.md firebase-setup.md
rm -rf deploy/README.md deploy/firebase-setup.md deploy/deploy-simple.bat deploy/deploy.sh deploy/upload-to-vps.* deploy/test-deployment.bat deploy/setup-server.sh deploy/ssl-setup.sh

# Clean up any backup files
rm -f *.backup server.js.backup

# Restart the service
sudo -u octane pm2 restart octane-auth
```

## Octane by Lag
