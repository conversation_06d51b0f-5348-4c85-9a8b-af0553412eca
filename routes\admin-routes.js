const express = require('express');
const router = express.Router();
const os = require('os');
const fs = require('fs');

// Admin API endpoints

// Dashboard stats endpoint
router.get('/api/dashboard/stats', async (req, res) => {
    try {
        const { db } = req.app.locals;

        // Get license count
        const licenseCount = await new Promise((resolve, reject) => {
            db.get('SELECT COUNT(*) as count FROM licenses', (err, row) => {
                if (err) reject(err);
                else resolve(row.count);
            });
        });

        // Get active users count
        const activeUsers = await new Promise((resolve, reject) => {
            db.get('SELECT COUNT(*) as count FROM licenses WHERE is_active = 1', (err, row) => {
                if (err) reject(err);
                else resolve(row.count);
            });
        });

        res.json({
            success: true,
            stats: {
                totalLicenses: licenseCount,
                activeUsers: activeUsers,
                securityAlerts: 0,
                serverUptime: Math.floor(process.uptime())
            }
        });
    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get dashboard stats'
        });
    }
});

// License count endpoint
router.get('/api/licenses/count', async (req, res) => {
    try {
        const { db } = req.app.locals;

        db.get('SELECT COUNT(*) as count FROM licenses', (err, row) => {
            if (err) {
                console.error('License count error:', err);
                return res.status(500).json({ count: 0 });
            }
            res.json({ count: row.count });
        });
    } catch (error) {
        console.error('License count error:', error);
        res.json({ count: 0 });
    }
});

// Active users endpoint
router.get('/api/users/active', async (req, res) => {
    try {
        const { db } = req.app.locals;

        db.get('SELECT COUNT(*) as count FROM licenses WHERE is_active = 1', (err, row) => {
            if (err) {
                console.error('Active users error:', err);
                return res.status(500).json({ count: 0 });
            }
            res.json({ count: row.count });
        });
    } catch (error) {
        console.error('Active users error:', error);
        res.json({ count: 0 });
    }
});

// Recent activity endpoint
router.get('/api/admin/activity', (req, res) => {
    // Mock activity data for now
    const activities = [
        {
            type: 'license_created',
            title: 'New license created',
            timestamp: new Date().toISOString()
        },
        {
            type: 'user_login',
            title: 'User authenticated',
            timestamp: new Date(Date.now() - 300000).toISOString()
        }
    ];

    res.json({
        success: true,
        activities: activities
    });
});

router.get('/api/admin/licenses', (req, res) => {
    const { db } = req.app.locals;
    const limit = parseInt(req.query.limit) || 10;
    
    db.all(
        `SELECT * FROM licenses ORDER BY created_at DESC LIMIT ?`,
        [Math.min(limit, 50)],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

router.post('/api/admin/create-license', (req, res) => {
    const { db, generateLicenseKey, calculateExpiryDate } = req.app.locals;
    
    try {
        const { duration, notes } = req.body;

        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);

        db.run(
            `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
            [key, duration, expiresAt, notes || ''],
            function(err) {
                if (err) {
                    console.error('Create license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to create license'
                    });
                }

                console.log(`📝 License created: ${key} (${duration})`);

                res.json({
                    success: true,
                    message: 'License created successfully',
                    license: {
                        id: this.lastID,
                        key: key,
                        duration: duration,
                        expiresAt: expiresAt,
                        notes: notes || ''
                    }
                });
            }
        );
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

router.post('/api/admin/reset-hwid', (req, res) => {
    const { db } = req.app.locals;
    
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Reset HWID error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to reset hardware ID'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'License key not found'
                    });
                }

                console.log(`🔄 Hardware ID reset for license: ${licenseKey}`);

                res.json({
                    success: true,
                    message: 'Hardware ID reset successfully'
                });
            }
        );
    } catch (error) {
        console.error('Reset HWID error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// User Management API endpoints
router.get('/api/admin/user-stats', async (req, res) => {
    try {
        const { db } = req.app.locals;

        const stats = await new Promise((resolve, reject) => {
            db.all(`
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN is_banned = 1 THEN 1 ELSE 0 END) as banned
                FROM licenses
            `, (err, rows) => {
                if (err) reject(err);
                else resolve(rows[0]);
            });
        });

        res.json({
            success: true,
            stats: {
                total: stats.total || 0,
                active: stats.active || 0,
                banned: stats.banned || 0,
                online: 0 // Mock data
            }
        });
    } catch (error) {
        console.error('User stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get user stats'
        });
    }
});

router.get('/api/admin/users', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;

        // Get total count
        const totalCount = await new Promise((resolve, reject) => {
            db.get('SELECT COUNT(*) as count FROM licenses', (err, row) => {
                if (err) reject(err);
                else resolve(row.count);
            });
        });

        // Get users with pagination
        const users = await new Promise((resolve, reject) => {
            db.all(`
                SELECT license_key, duration, is_active, is_banned, hwid, last_ip, last_used, created_at, expires_at, notes
                FROM licenses
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            `, [limit, offset], (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });

        const totalPages = Math.ceil(totalCount / limit);

        res.json({
            success: true,
            users: users,
            pagination: {
                page: page,
                limit: limit,
                total: totalCount,
                totalPages: totalPages,
                start: offset + 1,
                end: Math.min(offset + limit, totalCount)
            }
        });
    } catch (error) {
        console.error('Users list error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get users'
        });
    }
});

router.get('/api/admin/users/:licenseKey', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const { licenseKey } = req.params;

        db.get('SELECT * FROM licenses WHERE license_key = ?', [licenseKey], (err, row) => {
            if (err) {
                console.error('User details error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to get user details'
                });
            }

            if (!row) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                user: row
            });
        });
    } catch (error) {
        console.error('User details error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get user details'
        });
    }
});

router.post('/api/admin/users/:licenseKey/reset-hwid', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const { licenseKey } = req.params;

        db.run('UPDATE licenses SET hwid = NULL WHERE license_key = ?', [licenseKey], function(err) {
            if (err) {
                console.error('Reset HWID error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to reset HWID'
                });
            }

            res.json({
                success: true,
                message: 'HWID reset successfully'
            });
        });
    } catch (error) {
        console.error('Reset HWID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset HWID'
        });
    }
});

router.post('/api/admin/users/:licenseKey/ban', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const { licenseKey } = req.params;

        db.run('UPDATE licenses SET is_banned = 1, is_active = 0 WHERE license_key = ?', [licenseKey], function(err) {
            if (err) {
                console.error('Ban user error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to ban user'
                });
            }

            res.json({
                success: true,
                message: 'User banned successfully'
            });
        });
    } catch (error) {
        console.error('Ban user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to ban user'
        });
    }
});

router.post('/api/admin/users/:licenseKey/unban', async (req, res) => {
    try {
        const { db } = req.app.locals;
        const { licenseKey } = req.params;

        db.run('UPDATE licenses SET is_banned = 0, is_active = 1 WHERE license_key = ?', [licenseKey], function(err) {
            if (err) {
                console.error('Unban user error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to unban user'
                });
            }

            res.json({
                success: true,
                message: 'User unbanned successfully'
            });
        });
    } catch (error) {
        console.error('Unban user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to unban user'
        });
    }
});

// System Status API endpoints
router.get('/api/admin/system-status', (req, res) => {
    try {
        const cpus = os.cpus();
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;

        // Mock disk usage (would need additional library for real disk stats)
        const diskTotal = 50 * 1024 * 1024 * 1024; // 50GB mock
        const diskUsed = 15 * 1024 * 1024 * 1024; // 15GB mock

        const metrics = {
            uptime: os.uptime(),
            loadAverage: os.loadavg()[0],
            cpu: Math.random() * 100, // Mock CPU usage
            cpuCores: cpus.length,
            cpuUser: Math.random() * 50,
            cpuSystem: Math.random() * 30,
            memory: (usedMem / totalMem) * 100,
            memoryTotal: totalMem,
            memoryUsed: usedMem,
            memoryFree: freeMem,
            disk: (diskUsed / diskTotal) * 100,
            diskTotal: diskTotal,
            diskUsed: diskUsed,
            diskAvailable: diskTotal - diskUsed,
            networkIn: Math.random() * 1000000, // Mock network stats
            networkOut: Math.random() * 1000000,
            networkConnections: Math.floor(Math.random() * 100),
            processPid: process.pid,
            processMemory: process.memoryUsage().rss,
            processCpu: process.cpuUsage().user / 1000000 // Convert to percentage
        };

        res.json({
            success: true,
            metrics: metrics
        });
    } catch (error) {
        console.error('System status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get system status'
        });
    }
});

router.get('/api/admin/service-status', (req, res) => {
    try {
        const services = {
            api: {
                status: 'online',
                message: 'API is running'
            },
            database: {
                status: 'online',
                message: 'Database connected'
            },
            discord: {
                status: 'online',
                message: 'Discord bot connected'
            },
            security: {
                status: 'online',
                message: 'Security monitoring active'
            }
        };

        res.json({
            success: true,
            services: services
        });
    } catch (error) {
        console.error('Service status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get service status'
        });
    }
});

router.get('/api/admin/system-logs', (req, res) => {
    try {
        const level = req.query.level;

        // Mock log data
        const logs = [
            {
                timestamp: new Date().toISOString(),
                level: 'info',
                message: 'System started successfully'
            },
            {
                timestamp: new Date(Date.now() - 300000).toISOString(),
                level: 'info',
                message: 'Database connection established'
            },
            {
                timestamp: new Date(Date.now() - 600000).toISOString(),
                level: 'warn',
                message: 'High memory usage detected'
            }
        ];

        const filteredLogs = level ? logs.filter(log => log.level === level) : logs;

        res.json({
            success: true,
            logs: filteredLogs
        });
    } catch (error) {
        console.error('System logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get system logs'
        });
    }
});

// Settings API endpoints
router.get('/api/admin/settings', (req, res) => {
    try {
        // Mock settings data
        const settings = {
            general: {
                systemName: 'Octane Recoil Scripts',
                adminEmail: '<EMAIL>',
                timezone: 'UTC',
                maintenanceMode: false
            },
            security: {
                maxLoginAttempts: 5,
                sessionTimeout: 60,
                rateLimitWindow: 15,
                rateLimitMax: 100,
                enable2FA: false,
                logSecurityEvents: true
            },
            license: {
                defaultDuration: '1month',
                maxHwidResets: 3,
                keyFormat: 'XXX.XXX.XXX',
                autoExpire: true,
                notifyExpiring: true
            },
            discord: {
                token: process.env.DISCORD_TOKEN || '',
                serverId: process.env.DISCORD_SERVER_ID || '',
                channelId: process.env.DISCORD_CHANNEL_ID || '',
                adminId: process.env.DISCORD_ADMIN_USER_ID || '',
                notifications: true,
                securityAlerts: true
            },
            database: {
                backupInterval: 24,
                maxBackups: 7,
                cleanupInterval: 30,
                autoBackup: true,
                autoCleanup: true
            }
        };

        res.json({
            success: true,
            settings: settings
        });
    } catch (error) {
        console.error('Settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get settings'
        });
    }
});

router.post('/api/admin/settings', (req, res) => {
    try {
        const { category, settings } = req.body;

        // In a real implementation, you would save these settings to a config file or database
        console.log(`Saving ${category} settings:`, settings);

        res.json({
            success: true,
            message: `${category} settings saved successfully`
        });
    } catch (error) {
        console.error('Save settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to save settings'
        });
    }
});

module.exports = router;
