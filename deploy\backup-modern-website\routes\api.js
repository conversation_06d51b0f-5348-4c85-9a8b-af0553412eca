const express = require('express');
const router = express.Router();
const database = require('../models/database');
const fs = require('fs');
const path = require('path');

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Octane Authentication API'
    });
});

// Middleware to check maintenance mode
function checkMaintenanceMode(req, res, next) {
    const maintenanceFile = '/opt/octane-auth/maintenance.flag';

    if (fs.existsSync(maintenanceFile)) {
        return res.status(503).json({
            success: false,
            message: 'System is currently under maintenance. Please try again later.',
            error: 'MAINTENANCE_MODE',
            maintenanceMode: true
        });
    }

    next();
}

// Validate license key
router.post('/validate', checkMaintenanceMode, async (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        const license = await database.getLicense(licenseKey);
        
        if (!license) {
            return res.status(404).json({
                success: false,
                message: 'Invalid license key'
            });
        }

        // Check if license is expired
        if (license.expiresAt && new Date() > new Date(license.expiresAt)) {
            return res.status(403).json({
                success: false,
                message: 'License has expired'
            });
        }

        // Update last used
        await database.updateLicense(licenseKey, {
            lastUsed: new Date().toISOString()
        });

        res.json({
            success: true,
            message: 'License is valid',
            license: {
                key: license.key,
                duration: license.duration,
                expiresAt: license.expiresAt,
                createdAt: license.createdAt
            }
        });
    } catch (error) {
        console.error('License validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

module.exports = router;
