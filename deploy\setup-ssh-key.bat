@echo off
echo.
echo Setting up SSH key authentication...
echo =====================================
echo.

set VPS_IP=*************
set VPS_USER=root

echo Step 1: Generating SSH key pair...
ssh-keygen -t rsa -b 4096 -f octane_key -N ""

if %ERRORLEVEL% NEQ 0 (
    echo Failed to generate SSH key!
    pause
    exit /b 1
)

echo.
echo Step 2: Copying public key to VPS...
echo You'll need to enter your password one last time:
type octane_key.pub | ssh %VPS_USER%@%VPS_IP% "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"

if %ERRORLEVEL% NEQ 0 (
    echo Failed to copy SSH key!
    pause
    exit /b 1
)

echo.
echo Step 3: Testing passwordless connection...
ssh -i octane_key %VPS_USER%@%VPS_IP% "echo 'SSH key authentication successful!'"

if %ERRORLEVEL% NEQ 0 (
    echo SSH key test failed!
    pause
    exit /b 1
)

echo.
echo ✅ SSH key authentication setup complete!
echo.
echo Now updating deployment script to use SSH key...

echo.
echo ✅ Setup complete! You can now deploy without entering passwords.
echo.
pause
