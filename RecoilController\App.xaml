<Application x:Class="RecoilController.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:ui="http://schemas.modernwpf.com/2019"
             xmlns:local="clr-namespace:RecoilController.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- ModernWpf Theme -->
                <ui:ThemeResources RequestedTheme="Dark" />
                <ui:XamlControlsResources />

                <!-- Custom Styles and Converters -->
                <ResourceDictionary>
                    <!-- Value Converters -->
                    <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
                    <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
                    <local:EnumToCollectionConverter x:Key="EnumToCollectionConverter"/>

                    <Style x:Key="LagBrandingStyle" TargetType="TextBlock">
                        <Setter Property="FontFamily" Value="Consolas, Monaco, 'Courier New', monospace"/>
                        <Setter Property="FontSize" Value="10"/>
                        <Setter Property="Foreground" Value="#666"/>
                        <Setter Property="HorizontalAlignment" Value="Right"/>
                        <Setter Property="VerticalAlignment" Value="Bottom"/>
                        <Setter Property="Margin" Value="0,0,10,5"/>
                    </Style>

                    <Style x:Key="HackerTextStyle" TargetType="TextBlock">
                        <Setter Property="FontFamily" Value="Consolas, Monaco, 'Courier New', monospace"/>
                        <Setter Property="Foreground" Value="#00FF00"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
