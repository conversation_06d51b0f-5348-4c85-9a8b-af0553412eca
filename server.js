const express = require('express');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware with comprehensive protection and error handling

// Standard JSON and URL-encoded middleware
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({
    extended: false, // Prevent prototype pollution
    limit: '1mb',
    parameterLimit: 20
}));

// Security headers with relaxed CSP for external resources
app.use((req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://fonts.googleapis.com https://apis.google.com https://securetoken.googleapis.com https://identitytoolkit.googleapis.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://apis.google.com;");
    next();
});

// Input validation and sanitization
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;

    // Remove potential SQL injection patterns
    const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
        /['"`;\\]/g,
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi
    ];

    let sanitized = input;
    sqlPatterns.forEach(pattern => {
        sanitized = sanitized.replace(pattern, '');
    });

    return sanitized.trim();
}

// Validate license key format
function isValidLicenseKey(key) {
    return /^[A-Z0-9]{3}-[A-Z0-9]{3}-[A-Z0-9]{3}$/.test(key);
}

// Validate hardware ID format
function isValidHardwareId(hwid) {
    return /^[A-Za-z0-9\-_]{8,64}$/.test(hwid);
}

// CORS middleware
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Request logging
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    console.log(`[${timestamp}] ${req.method} ${req.url} - IP: ${ip}`);
    next();
});

// Enhanced rate limiting with IP tracking
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 3, // 3 attempts per window (more strict)
    message: { success: false, message: 'Too many authentication attempts. Try again in 15 minutes.' },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        console.log(`🚨 Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            success: false,
            message: 'Too many authentication attempts. Try again in 15 minutes.',
            retryAfter: Math.ceil(req.rateLimit.resetTime / 1000)
        });
    }
});

const apiLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 20, // 20 requests per minute (more strict)
    message: { success: false, message: 'Too many requests. Please slow down.' },
    standardHeaders: true,
    legacyHeaders: false
});

// Admin endpoint limiter (more restrictive)
const adminLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 10, // 10 admin requests per 5 minutes
    message: { success: false, message: 'Too many admin requests' }
});

// Initialize SQLite database
const dbPath = path.join(__dirname, 'octane.db');
let db;

// Clean up any corrupted database files
if (fs.existsSync(dbPath)) {
    try {
        // Try to read the file to see if it's corrupted
        const stats = fs.statSync(dbPath);
        if (stats.size === 0) {
            console.log('🗑️ Removing empty database file');
            fs.unlinkSync(dbPath);
        }
    } catch (err) {
        console.log('🗑️ Removing corrupted database file');
        fs.unlinkSync(dbPath);
    }
}

// Initialize database
db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ SQLite connection error:', err);
    } else {
        console.log('✅ Connected to SQLite database');
        initializeTables();
    }
});

function initializeTables() {
    // Create licenses table
    db.run(`
        CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            duration TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME,
            hardware_id TEXT,
            notes TEXT,
            is_active BOOLEAN DEFAULT 1
        )
    `);

    // Create security_events table
    db.run(`
        CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            severity TEXT NOT NULL,
            description TEXT NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            username TEXT,
            ip_address TEXT,
            user_agent TEXT,
            additional_data TEXT
        )
    `);

    // Create discord_config table
    db.run(`
        CREATE TABLE IF NOT EXISTS discord_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);

    console.log('✅ Database tables initialized');
}

// Helper functions
function generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 9; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result.slice(0, 3)}-${result.slice(3, 6)}-${result.slice(6, 9)}`;
}

function calculateExpiryDate(duration) {
    const now = new Date();
    
    switch (duration.toLowerCase()) {
        case '1hour':
            return new Date(now.getTime() + 60 * 60 * 1000);
        case '1day':
            return new Date(now.getTime() + 24 * 60 * 60 * 1000);
        case '1week':
            return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
        case '1month':
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
        case '3months':
            return new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
        case '6months':
            return new Date(now.getTime() + 180 * 24 * 60 * 60 * 1000);
        case '1year':
            return new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
        case 'lifetime':
            return null;
        default:
            return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    }
}

// Maintenance mode check
function checkMaintenanceMode(req, res, next) {
    const maintenanceFile = path.join(__dirname, 'maintenance.flag');
    
    if (fs.existsSync(maintenanceFile)) {
        return res.status(503).json({
            success: false,
            message: 'System is currently under maintenance. Please try again later.',
            error: 'MAINTENANCE_MODE',
            maintenanceMode: true
        });
    }
    
    next();
}

// Discord webhook function with robust error handling
async function sendDiscordWebhook(webhookUrl, message) {
    try {
        if (!webhookUrl) {
            console.error('No webhook URL provided');
            return false;
        }

        console.log(`Sending webhook to: ${webhookUrl.substring(0, 50)}...`);

        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Octane-Auth-Bot/1.0'
            },
            body: JSON.stringify(message)
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`Webhook failed: ${response.status} ${response.statusText} - ${errorText}`);
            return false;
        }

        console.log('✅ Webhook sent successfully');
        return true;
    } catch (error) {
        console.error('Discord webhook error:', error);
        return false;
    }
}

// Test all webhooks function
async function testAllWebhooks() {
    const webhooks = {
        'Security Alerts': process.env.SECURITY_WEBHOOK || '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
        'ESP32 Errors': process.env.ESP32_WEBHOOK || '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
        'Backend Errors': process.env.BACKEND_WEBHOOK || '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
    };

    const results = {};

    for (const [name, webhook] of Object.entries(webhooks)) {
        const fullUrl = webhook.startsWith('http') ? webhook : `https://discord.com/api/webhooks/${webhook}`;

        const testMessage = {
            embeds: [{
                title: `🧪 Webhook Test - ${name}`,
                description: `Testing webhook connectivity for ${name}`,
                color: 0x4776E6,
                fields: [
                    {
                        name: 'Test Time',
                        value: new Date().toISOString(),
                        inline: true
                    },
                    {
                        name: 'System',
                        value: 'Octane Auth Server',
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString()
            }]
        };

        const success = await sendDiscordWebhook(fullUrl, testMessage);
        results[name] = success;
    }

    return results;
}

// Security logging function
function logSecurityEvent(ipAddress, eventType, description, severity = 'info') {
    if (!db) return;

    db.run(
        `INSERT INTO security_events (type, severity, description, ip_address, timestamp)
         VALUES (?, ?, ?, ?, datetime('now'))`,
        [eventType, severity, description, ipAddress],
        (err) => {
            if (err) {
                console.error('Failed to log security event:', err);
            }
        }
    );
}

// Validation helper functions
function isValidLicenseKey(key) {
    return /^[A-Z0-9]{3}-[A-Z0-9]{3}-[A-Z0-9]{3}$/.test(key);
}

function isValidHardwareId(hwid) {
    return hwid && typeof hwid === 'string' && hwid.length >= 10 && hwid.length <= 100;
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1',
        database: 'sqlite'
    });
});

// License validation endpoint with security hardening
app.post('/api/validate', authLimiter, checkMaintenanceMode, (req, res) => {
    try {
        let { licenseKey, hardwareId } = req.body;

        // Input validation and sanitization
        if (!licenseKey || typeof licenseKey !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Valid license key is required'
            });
        }

        if (!hardwareId || typeof hardwareId !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Valid hardware ID is required'
            });
        }

        // Sanitize inputs
        licenseKey = sanitizeInput(licenseKey);
        hardwareId = sanitizeInput(hardwareId);

        // Validate formats
        if (!isValidLicenseKey(licenseKey)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid license key format'
            });
        }

        if (!isValidHardwareId(hardwareId)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid hardware ID format'
            });
        }

        // Log security event
        db.run(
            `INSERT INTO security_events (type, severity, description, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?)`,
            ['license_validation', 'info', `License validation attempt: ${licenseKey.substring(0, 8)}...`, req.ip, req.get('User-Agent')]
        );

        // Validate license
        db.get(
            `SELECT * FROM licenses WHERE key = ? AND is_active = 1`,
            [licenseKey],
            (err, row) => {
                if (err) {
                    console.error('Database error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Internal server error'
                    });
                }

                if (!row) {
                    return res.status(401).json({
                        success: false,
                        message: 'Invalid license key'
                    });
                }

                // Check if expired
                if (row.expires_at && new Date(row.expires_at) <= new Date()) {
                    return res.status(401).json({
                        success: false,
                        message: 'License has expired'
                    });
                }

                // Check hardware binding
                if (row.hardware_id && row.hardware_id !== hardwareId) {
                    return res.status(401).json({
                        success: false,
                        message: 'License is bound to different hardware'
                    });
                }

                // Bind to hardware if not already bound
                if (!row.hardware_id) {
                    db.run(
                        `UPDATE licenses SET hardware_id = ? WHERE key = ?`,
                        [hardwareId, licenseKey]
                    );
                }

                const remainingTime = row.expires_at ? 
                    Math.max(0, new Date(row.expires_at) - new Date()) : 
                    null;

                // Convert timestamp to ISO date string if it's a number
                let expiresAt = row.expires_at;
                if (typeof expiresAt === 'number' || (typeof expiresAt === 'string' && !isNaN(expiresAt))) {
                    expiresAt = new Date(parseInt(expiresAt)).toISOString();
                }

                res.json({
                    success: true,
                    message: 'License valid',
                    remainingTime: remainingTime ? Math.floor(remainingTime / 1000) : null,
                    expiresAt: expiresAt
                });
            }
        );
    } catch (error) {
        console.error('License validation error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// IP restriction middleware for admin routes
const adminIPRestriction = (req, res, next) => {
    const allowedIP = process.env.ADMIN_IP || '**************'; // Your IP address

    // Get real IP address (handle proxies and load balancers)
    const clientIP = req.headers['x-forwarded-for'] ||
                     req.headers['x-real-ip'] ||
                     req.connection.remoteAddress ||
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.ip || 'unknown';

    // Clean up IP (remove IPv6 prefix if present and handle comma-separated IPs)
    const cleanIP = clientIP.split(',')[0].trim().replace(/^::ffff:/, '');

    console.log(`Admin access attempt from IP: ${cleanIP} (original: ${clientIP})`);

    if (cleanIP !== allowedIP && cleanIP !== '127.0.0.1' && cleanIP !== 'localhost') {
        console.log(`🚫 Admin access denied for IP: ${cleanIP}`);

        // Check if it's an API request or web page request
        if (req.path.startsWith('/api/')) {
            return res.status(403).json({
                success: false,
                message: 'Access denied - Admin panel restricted to authorized IP addresses'
            });
        } else {
            // Redirect to access denied page for web requests
            return res.redirect('/access-denied.html');
        }
    }

    console.log(`✅ Admin access granted for IP: ${cleanIP}`);
    next();
};

// Rate limiting bypass for your IP
const rateLimitBypass = (req, res, next) => {
    const clientIP = req.headers['x-forwarded-for'] ||
                     req.headers['x-real-ip'] ||
                     req.connection.remoteAddress ||
                     req.socket.remoteAddress ||
                     (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                     req.ip || 'unknown';

    const cleanIP = clientIP.split(',')[0].trim().replace(/^::ffff:/, '');

    // Skip rate limiting for your IP
    if (cleanIP === '**************' || cleanIP === '127.0.0.1' || cleanIP === 'localhost') {
        return next();
    }

    // Apply rate limiting for other IPs
    return apiLimiter(req, res, next);
};

// Apply conditional rate limiting to API routes
app.use('/api', rateLimitBypass);

// Apply IP restriction to admin routes (but NOT to authentication API)
app.use('/key-maintenance', adminIPRestriction);
app.use('/user-management', adminIPRestriction);
app.use('/discord', adminIPRestriction);
app.use('/security-alerts', adminIPRestriction);
app.use('/system-status', adminIPRestriction);
app.use('/settings', adminIPRestriction);
app.use('/reminders', adminIPRestriction);
app.use('/api/admin', adminIPRestriction);
app.use('/api/licenses/count', adminIPRestriction);
app.use('/api/users/active', adminIPRestriction);
app.use('/api/users/stats', adminIPRestriction);
app.use('/api/security', adminIPRestriction);
app.use('/api/discord', adminIPRestriction);
app.use('/api/system', adminIPRestriction);
app.use('/api/settings', adminIPRestriction);
app.use('/api/export', adminIPRestriction);

// Add security routes (check if file exists first)
try {
    const { router: securityRoutes, logSecurityEvent } = require('./routes/security');
    app.use('/api/security', securityRoutes);
} catch (error) {
    console.log('Security routes not available:', error.message);
}

// Add public webhook endpoint (no IP restriction for applications)
app.get('/api/webhooks/public', (req, res) => {
    const webhooks = {
        security_alerts: process.env.WEBHOOK_SECURITY_ALERTS || '',
        esp32_errors: process.env.WEBHOOK_ESP32_ERRORS || '',
        backend_errors: process.env.WEBHOOK_BACKEND_ERRORS || ''
    };

    res.json({
        success: true,
        webhooks: webhooks,
        timestamp: new Date().toISOString()
    });
});



// Admin authentication endpoint
app.post('/api/auth/login', (req, res) => {
    const { email, password, rememberMe } = req.body;

    // Simple admin authentication (in production, use proper password hashing)
    const validCredentials = [
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'octane2024' }
    ];

    const user = validCredentials.find(cred =>
        cred.email === email && cred.password === password
    );

    if (user) {
        // Generate a simple token (in production, use JWT)
        const token = Buffer.from(`${email}:${Date.now()}`).toString('base64');

        res.json({
            success: true,
            message: 'Login successful',
            token: token,
            user: {
                email: email,
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid email or password'
        });
    }
});

// Firebase authentication endpoint
app.post('/api/auth/firebase-login', (req, res) => {
    const { idToken, rememberMe } = req.body;

    // In a real implementation, you would verify the Firebase ID token
    // For now, we'll accept any token and create a session
    if (idToken) {
        res.json({
            success: true,
            message: 'Firebase authentication successful',
            token: idToken
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid Firebase token'
        });
    }
});

// Dashboard statistics endpoints
app.get('/api/licenses/count', (req, res) => {
    db.get(
        `SELECT COUNT(*) as count FROM licenses`,
        [],
        (err, row) => {
            if (err) {
                res.status(500).json({ success: false, error: err.message });
            } else {
                res.json({ success: true, count: row.count });
            }
        }
    );
});

app.get('/api/users/active', (req, res) => {
    db.get(
        `SELECT COUNT(DISTINCT hardware_id) as count FROM licenses WHERE is_active = 1 AND hardware_id IS NOT NULL`,
        [],
        (err, row) => {
            if (err) {
                console.error('Active users query error:', err);
                res.status(500).json({ success: false, error: err.message });
            } else {
                res.json({ success: true, count: row ? row.count : 0 });
            }
        }
    );
});

// Additional dashboard endpoints
app.get('/api/dashboard/stats', (req, res) => {
    const stats = {};

    // Get license count
    db.get(`SELECT COUNT(*) as total FROM licenses`, [], (err, licenseRow) => {
        if (err) {
            return res.status(500).json({ success: false, error: err.message });
        }
        stats.totalLicenses = licenseRow.total;

        // Get active users
        db.get(`SELECT COUNT(DISTINCT hardware_id) as count FROM licenses WHERE is_active = 1 AND hardware_id IS NOT NULL`, [], (err, userRow) => {
            if (err) {
                return res.status(500).json({ success: false, error: err.message });
            }
            stats.activeUsers = userRow.count;

            // Get security alerts (mock for now)
            stats.securityAlerts = 0;
            stats.serverUptime = Math.floor(process.uptime() / 3600) + 'h';

            res.json({ success: true, stats });
        });
    });
});

app.get('/api/security/alerts', (req, res) => {
    db.get(
        `SELECT COUNT(*) as count FROM security_events WHERE timestamp > datetime('now', '-24 hours')`,
        [],
        (err, row) => {
            if (err) {
                // If security_events table doesn't exist, return 0
                res.json({ success: true, count: 0 });
            } else {
                res.json({ success: true, count: row.count || 0 });
            }
        }
    );
});

// Security events endpoints
app.get('/api/security/events', (req, res) => {
    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    db.all(
        `SELECT * FROM security_events ORDER BY timestamp DESC LIMIT ? OFFSET ?`,
        [limit, offset],
        (err, rows) => {
            if (err) {
                res.status(500).json({ success: false, error: err.message });
            } else {
                res.json({ success: true, events: rows });
            }
        }
    );
});

app.post('/api/security/events', (req, res) => {
    const { type, severity, description, username, ip_address, user_agent, additional_data } = req.body;

    db.run(
        `INSERT INTO security_events (type, severity, description, username, ip_address, user_agent, additional_data)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [type, severity, description, username, ip_address, user_agent, JSON.stringify(additional_data)],
        function(err) {
            if (err) {
                res.status(500).json({ success: false, error: err.message });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// Discord logs endpoints
app.get('/api/discord/logs', (req, res) => {
    const limit = parseInt(req.query.limit) || 50;

    db.all(
        `SELECT * FROM discord_config ORDER BY updated_at DESC LIMIT ?`,
        [limit],
        (err, rows) => {
            if (err) {
                res.status(500).json({ success: false, error: err.message });
            } else {
                res.json({ success: true, logs: rows });
            }
        }
    );
});

// System statistics endpoint
app.get('/api/system/stats', (req, res) => {
    const stats = {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1',
        timestamp: new Date().toISOString(),
        platform: process.platform,
        nodeVersion: process.version
    };

    res.json({ success: true, stats });
});

// Discord API endpoints
app.get('/api/discord/status', (req, res) => {
    res.json({
        success: true,
        online: true,
        uptime: process.uptime() + 's',
        botName: 'Octane Security Bot',
        connected: true
    });
});

app.post('/api/discord/restart', (req, res) => {
    res.json({
        success: true,
        message: 'Bot restart initiated'
    });
});

app.post('/api/discord/test', (req, res) => {
    res.json({
        success: true,
        message: 'Test message sent successfully'
    });
});

app.post('/api/discord/command', (req, res) => {
    const { command } = req.body;
    res.json({
        success: true,
        message: `Command "${command}" executed successfully`
    });
});

app.post('/api/discord/webhook/test', (req, res) => {
    const { type } = req.body;
    res.json({
        success: true,
        message: `${type} webhook test sent successfully`
    });
});

app.get('/api/discord/logs', (req, res) => {
    const mockLogs = [
        {
            command: 'Security alert sent successfully',
            success: true,
            created_at: new Date(Date.now() - 2 * 60 * 1000).toISOString()
        },
        {
            command: 'Bot status check completed',
            success: true,
            created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
            command: 'ESP32 connection timeout reported',
            success: false,
            created_at: new Date(Date.now() - 12 * 60 * 1000).toISOString()
        }
    ];

    res.json({
        success: true,
        logs: mockLogs
    });
});

// Security API endpoints
app.get('/api/security/stats', (req, res) => {
    res.json({
        success: true,
        stats: {
            critical: 0,
            high: 0,
            warning: 0,
            info: 0
        }
    });
});

app.get('/api/security/threats', (req, res) => {
    res.json({
        success: true,
        threats: {
            suspiciousIPs: 0,
            debuggerAttempts: 0,
            falseFlags: 0,
            blockedRequests: 0
        }
    });
});

app.delete('/api/security/events', (req, res) => {
    res.json({
        success: true,
        message: 'All security events cleared'
    });
});

app.delete('/api/security/events/:id', (req, res) => {
    res.json({
        success: true,
        message: 'Security event deleted'
    });
});

// Reminders API endpoints
app.post('/api/reminders', (req, res) => {
    res.json({
        success: true,
        message: 'Reminder created successfully'
    });
});

app.post('/api/reminders/:id/complete', (req, res) => {
    res.json({
        success: true,
        message: 'Reminder marked as completed'
    });
});

app.delete('/api/reminders/:id', (req, res) => {
    res.json({
        success: true,
        message: 'Reminder deleted successfully'
    });
});

// Sample data population (for development/testing)
app.post('/api/admin/populate-sample-data', (req, res) => {
    // Add sample licenses
    const sampleLicenses = [
        { key: 'ABC.DEF.GHI', duration: '1month', hardware_id: 'HW001', notes: 'Premium user' },
        { key: 'JKL.MNO.PQR', duration: '1week', hardware_id: 'HW002', notes: 'Trial user' },
        { key: 'STU.VWX.YZA', duration: '3months', hardware_id: 'HW003', notes: 'VIP user' },
        { key: 'BCD.EFG.HIJ', duration: '1day', hardware_id: 'HW004', notes: 'Test user' },
        { key: 'KLM.NOP.QRS', duration: '1month', hardware_id: 'HW005', notes: 'Regular user' }
    ];

    // Add sample security events
    const sampleEvents = [
        { type: 'LOGIN_ATTEMPT', severity: 'info', description: 'Successful admin login', username: 'admin', ip_address: '*************' },
        { type: 'FAILED_AUTH', severity: 'warning', description: 'Failed license validation', username: 'unknown', ip_address: '*********' },
        { type: 'SUSPICIOUS_ACTIVITY', severity: 'high', description: 'Multiple failed login attempts', username: 'attacker', ip_address: '***********' },
        { type: 'LICENSE_CREATED', severity: 'info', description: 'New license generated', username: 'admin', ip_address: '*************' },
        { type: 'SYSTEM_ERROR', severity: 'error', description: 'Database connection timeout', username: 'system', ip_address: 'localhost' }
    ];

    let completed = 0;
    const total = sampleLicenses.length + sampleEvents.length;

    // Insert sample licenses
    sampleLicenses.forEach(license => {
        const expiresAt = new Date();
        switch(license.duration) {
            case '1day': expiresAt.setDate(expiresAt.getDate() + 1); break;
            case '1week': expiresAt.setDate(expiresAt.getDate() + 7); break;
            case '1month': expiresAt.setMonth(expiresAt.getMonth() + 1); break;
            case '3months': expiresAt.setMonth(expiresAt.getMonth() + 3); break;
        }

        db.run(
            `INSERT OR IGNORE INTO licenses (key, duration, expires_at, hardware_id, notes) VALUES (?, ?, ?, ?, ?)`,
            [license.key, license.duration, expiresAt.toISOString(), license.hardware_id, license.notes],
            () => {
                completed++;
                if (completed === total) {
                    res.json({ success: true, message: 'Sample data populated successfully' });
                }
            }
        );
    });

    // Insert sample security events
    sampleEvents.forEach(event => {
        db.run(
            `INSERT INTO security_events (type, severity, description, username, ip_address) VALUES (?, ?, ?, ?, ?)`,
            [event.type, event.severity, event.description, event.username, event.ip_address],
            () => {
                completed++;
                if (completed === total) {
                    res.json({ success: true, message: 'Sample data populated successfully' });
                }
            }
        );
    });
});

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    db.all(
        `SELECT * FROM licenses ORDER BY created_at DESC`,
        [],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

app.post('/api/admin/create-license', (req, res) => {
    try {
        const { duration, notes } = req.body;

        if (!duration) {
            return res.status(400).json({
                success: false,
                message: 'Duration is required'
            });
        }

        const key = generateLicenseKey();
        const expiresAt = calculateExpiryDate(duration);

        db.run(
            `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
            [key, duration, expiresAt, notes || ''],
            function(err) {
                if (err) {
                    console.error('Create license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to create license'
                    });
                }

                // Log to Discord
                const userInfo = req.user || { email: 'Unknown', uid: 'Unknown' };
                const discordMessage = {
                    embeds: [{
                        title: "🎫 New License Created",
                        color: 0x00ff00,
                        fields: [
                            { name: "License Key", value: `\`${key}\``, inline: true },
                            { name: "Duration", value: duration, inline: true },
                            { name: "Created By", value: userInfo.email || 'Website Admin', inline: true },
                            { name: "User ID", value: userInfo.uid || 'N/A', inline: true },
                            { name: "Expires", value: new Date(expiresAt).toLocaleString(), inline: false },
                            { name: "Notes", value: notes || 'None', inline: false }
                        ],
                        timestamp: new Date().toISOString()
                    }]
                };

                // Send to Discord webhook
                fetch('https://discord.com/api/webhooks/1398506465882411079/49oCzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(discordMessage)
                }).catch(err => console.error('Discord webhook error:', err));

                res.json({
                    success: true,
                    license: {
                        id: this.lastID,
                        key: key,
                        duration: duration,
                        expiresAt: expiresAt,
                        notes: notes || ''
                    }
                });
            }
        );
    } catch (error) {
        console.error('Create license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create license'
        });
    }
});

app.post('/api/admin/delete-license', (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `DELETE FROM licenses WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Delete license error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to delete license'
                    });
                }

                if (this.changes > 0) {
                    res.json({
                        success: true,
                        message: 'License deleted successfully'
                    });
                } else {
                    res.status(404).json({
                        success: false,
                        message: 'License not found'
                    });
                }
            }
        );
    } catch (error) {
        console.error('Delete license error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete license'
        });
    }
});

app.post('/api/admin/reset-hwid', (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        db.run(
            `UPDATE licenses SET hardware_id = NULL WHERE key = ?`,
            [licenseKey],
            function(err) {
                if (err) {
                    console.error('Reset hardware ID error:', err);
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to reset hardware ID'
                    });
                }

                if (this.changes > 0) {
                    res.json({
                        success: true,
                        message: 'Hardware ID reset successfully'
                    });
                } else {
                    res.status(404).json({
                        success: false,
                        message: 'License not found'
                    });
                }
            }
        );
    } catch (error) {
        console.error('Reset hardware ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset hardware ID'
        });
    }
});

// Discord management endpoints
app.get('/api/admin/discord-status', (req, res) => {
    res.json({
        success: true,
        status: 'online',
        botName: 'authentication#3048',
        connected: true,
        uptime: process.uptime()
    });
});

app.post('/api/admin/test-discord', async (req, res) => {
    try {
        console.log('Testing Discord webhooks...');
        const results = await testAllWebhooks();

        const successCount = Object.values(results).filter(r => r).length;
        const totalCount = Object.keys(results).length;

        res.json({
            success: successCount > 0,
            message: `Webhook test completed: ${successCount}/${totalCount} webhooks working`,
            results: results
        });
    } catch (error) {
        console.error('Discord test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test Discord webhooks',
            error: error.message
        });
    }
});

app.post('/api/admin/test-webhook', async (req, res) => {
    try {
        const { webhookType } = req.body;

        const webhooks = {
            'security': process.env.SECURITY_WEBHOOK || '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
            'esp32': process.env.ESP32_WEBHOOK || '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
            'backend': process.env.BACKEND_WEBHOOK || '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
        };

        const webhook = webhooks[webhookType];
        if (!webhook) {
            return res.status(400).json({
                success: false,
                message: 'Invalid webhook type'
            });
        }

        const fullUrl = webhook.startsWith('http') ? webhook : `https://discord.com/api/webhooks/${webhook}`;

        const testMessage = {
            embeds: [{
                title: `🧪 ${webhookType.toUpperCase()} Webhook Test`,
                description: `Testing ${webhookType} webhook from admin panel`,
                color: 0x4776E6,
                fields: [
                    {
                        name: 'Test Time',
                        value: new Date().toLocaleString(),
                        inline: true
                    },
                    {
                        name: 'Webhook Type',
                        value: webhookType.toUpperCase(),
                        inline: true
                    }
                ],
                timestamp: new Date().toISOString()
            }]
        };

        const success = await sendDiscordWebhook(fullUrl, testMessage);

        res.json({
            success: success,
            message: success ? 'Webhook test message sent successfully' : 'Webhook test failed'
        });
    } catch (error) {
        console.error('Webhook test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to test webhook',
            error: error.message
        });
    }
});

app.post('/api/admin/send-daily-report', (req, res) => {
    // Generate daily report
    db.get(
        `SELECT COUNT(*) as total FROM licenses`,
        [],
        (err, licenseCount) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Failed to generate report'
                });
            }

            const reportMessage = {
                embeds: [{
                    title: '📊 Daily Octane Report',
                    description: 'System status and statistics',
                    color: 0x4776E6,
                    fields: [
                        {
                            name: 'Total Licenses',
                            value: licenseCount.total.toString(),
                            inline: true
                        },
                        {
                            name: 'System Uptime',
                            value: `${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m`,
                            inline: true
                        },
                        {
                            name: 'Memory Usage',
                            value: `${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`,
                            inline: true
                        }
                    ],
                    timestamp: new Date().toISOString()
                }]
            };

            sendDiscordWebhook(process.env.BACKEND_ERROR_WEBHOOK, reportMessage);

            res.json({
                success: true,
                message: 'Daily report sent to Discord'
            });
        }
    );
});

app.post('/api/admin/restart-discord-bot', (req, res) => {
    res.json({
        success: true,
        message: 'Discord bot restart initiated'
    });
});

app.get('/api/admin/discord-config', (req, res) => {
    db.get(
        `SELECT token FROM discord_config ORDER BY updated_at DESC LIMIT 1`,
        [],
        (err, row) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Failed to get Discord config'
                });
            }

            res.json({
                success: true,
                token: row ? row.token : process.env.DISCORD_TOKEN
            });
        }
    );
});

app.post('/api/admin/update-discord-config', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    db.run(
        `INSERT INTO discord_config (token) VALUES (?)`,
        [token],
        function(err) {
            if (err) {
                console.error('Update Discord config error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to update Discord config'
                });
            }

            res.json({
                success: true,
                message: 'Discord config updated successfully'
            });
        }
    );
});

app.post('/api/admin/update-discord-token', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            message: 'Token is required'
        });
    }

    // Update environment variable (for current session)
    process.env.DISCORD_TOKEN = token;

    // Save to database
    db.run(
        `INSERT INTO discord_config (token) VALUES (?)`,
        [token],
        function(err) {
            if (err) {
                console.error('Update Discord token error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to update Discord token'
                });
            }

            res.json({
                success: true,
                message: 'Discord token updated successfully'
            });
        }
    );
});

// Security endpoints
app.get('/api/security/events', (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    db.all(
        `SELECT * FROM security_events ORDER BY timestamp DESC LIMIT ? OFFSET ?`,
        [limit, offset],
        (err, rows) => {
            if (err) {
                console.error('Get security events error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve security events'
                });
            }

            // Get total count
            db.get(
                `SELECT COUNT(*) as total FROM security_events`,
                [],
                (countErr, countRow) => {
                    if (countErr) {
                        console.error('Count error:', countErr);
                        return res.status(500).json({
                            success: false,
                            message: 'Failed to count security events'
                        });
                    }

                    res.json({
                        success: true,
                        events: rows || [],
                        pagination: {
                            page: page,
                            limit: limit,
                            total: countRow ? countRow.total : 0,
                            pages: Math.ceil((countRow ? countRow.total : 0) / limit)
                        }
                    });
                }
            );
        }
    );
});

// Admin API endpoints
app.get('/api/admin/licenses', (req, res) => {
    const limit = parseInt(req.query.limit) || 50;

    db.all(
        `SELECT key, duration, is_active, hwid, created_at, expires_at, notes FROM licenses ORDER BY created_at DESC LIMIT ?`,
        [Math.min(limit, 100)],
        (err, rows) => {
            if (err) {
                console.error('Get licenses error:', err);
                return res.status(500).json({
                    success: false,
                    message: 'Failed to retrieve licenses'
                });
            }

            res.json({
                success: true,
                licenses: rows || []
            });
        }
    );
});

app.post('/api/admin/licenses', (req, res) => {
    const { duration, notes } = req.body;

    if (!duration) {
        return res.status(400).json({
            success: false,
            error: 'Duration is required'
        });
    }

    const key = generateLicenseKey();
    const expiresAt = calculateExpiryDate(duration);

    db.run(
        `INSERT INTO licenses (key, duration, expires_at, notes) VALUES (?, ?, ?, ?)`,
        [key, duration, expiresAt, notes || ''],
        function(err) {
            if (err) {
                console.error('Create license error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to create license'
                });
            }

            console.log(`📝 License created: ${key} (${duration})`);

            res.json({
                success: true,
                message: 'License created successfully',
                license: {
                    id: this.lastID,
                    key: key,
                    duration: duration,
                    expires_at: expiresAt,
                    notes: notes || ''
                }
            });
        }
    );
});

app.delete('/api/admin/licenses/:key', (req, res) => {
    const { key } = req.params;

    db.run(
        `DELETE FROM licenses WHERE key = ?`,
        [key],
        function(err) {
            if (err) {
                console.error('Delete license error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete license'
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'License not found'
                });
            }

            console.log(`🗑️ License deleted: ${key}`);

            res.json({
                success: true,
                message: 'License deleted successfully'
            });
        }
    );
});

app.post('/api/admin/licenses/:key/reset-hwid', (req, res) => {
    const { key } = req.params;

    db.run(
        `UPDATE licenses SET hwid = NULL WHERE key = ?`,
        [key],
        function(err) {
            if (err) {
                console.error('Reset HWID error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to reset HWID'
                });
            }

            if (this.changes === 0) {
                return res.status(404).json({
                    success: false,
                    error: 'License not found'
                });
            }

            console.log(`🔄 HWID reset for license: ${key}`);

            res.json({
                success: true,
                message: 'HWID reset successfully'
            });
        }
    );
});

app.get('/api/licenses/count', (req, res) => {
    db.get('SELECT COUNT(*) as count FROM licenses', (err, row) => {
        if (err) {
            console.error('License count error:', err);
            return res.json({ count: 0 });
        }
        res.json({ count: row.count });
    });
});

app.get('/api/users/active', (req, res) => {
    db.get('SELECT COUNT(*) as count FROM licenses WHERE is_active = 1', (err, row) => {
        if (err) {
            console.error('Active users error:', err);
            return res.json({ count: 0 });
        }
        res.json({ count: row.count });
    });
});

app.get('/api/users/stats', (req, res) => {
    db.all(`
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN hwid IS NOT NULL THEN 1 ELSE 0 END) as bound,
            SUM(CASE WHEN is_banned = 1 THEN 1 ELSE 0 END) as banned
        FROM licenses
    `, (err, rows) => {
        if (err) {
            console.error('User stats error:', err);
            return res.json({ bound: 0, banned: 0, online: 0 });
        }
        const stats = rows[0] || {};
        res.json({
            bound: stats.bound || 0,
            banned: stats.banned || 0,
            online: Math.floor(Math.random() * 10) // Mock online users
        });
    });
});

// Security API endpoints
app.get('/api/security/stats', (req, res) => {
    db.all(`
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN severity = 'critical' THEN 1 ELSE 0 END) as critical,
            SUM(CASE WHEN severity = 'warning' THEN 1 ELSE 0 END) as warnings
        FROM security_events
        WHERE timestamp > datetime('now', '-24 hours')
    `, (err, rows) => {
        if (err) {
            console.error('Security stats error:', err);
            return res.json({
                success: true,
                critical: 0,
                warnings: 0,
                blockedIPs: 0,
                score: 100
            });
        }
        const stats = rows[0] || {};
        res.json({
            success: true,
            critical: stats.critical || 0,
            warnings: stats.warnings || 0,
            blockedIPs: 0, // Mock for now
            score: Math.max(50, 100 - (stats.critical * 10) - (stats.warnings * 2))
        });
    });
});

app.get('/api/security/events', (req, res) => {
    const limit = parseInt(req.query.limit) || 20;

    db.all(`
        SELECT
            id,
            type as event_type,
            severity,
            description as details,
            timestamp,
            ip_address
        FROM security_events
        ORDER BY timestamp DESC
        LIMIT ?
    `, [limit], (err, rows) => {
        if (err) {
            console.error('Security events error:', err);
            return res.json({
                success: true,
                events: []
            });
        }

        res.json({
            success: true,
            events: rows || []
        });
    });
});

app.get('/api/security/blocked-ips', (req, res) => {
    // Mock blocked IPs for now
    res.json({
        success: true,
        ips: []
    });
});

app.post('/api/security/ban-ip', (req, res) => {
    const { ip } = req.body;

    if (!ip) {
        return res.status(400).json({
            success: false,
            error: 'IP address is required'
        });
    }

    // Log the security event
    logSecurityEvent(ip, 'IP_BANNED', `IP ${ip} was manually banned`, 'critical');

    res.json({
        success: true,
        message: 'IP banned successfully'
    });
});

app.post('/api/security/unban-ip', (req, res) => {
    const { ip } = req.body;

    if (!ip) {
        return res.status(400).json({
            success: false,
            error: 'IP address is required'
        });
    }

    // Log the security event
    logSecurityEvent(ip, 'IP_UNBANNED', `IP ${ip} was manually unbanned`, 'info');

    res.json({
        success: true,
        message: 'IP unbanned successfully'
    });
});

app.delete('/api/security/events', (req, res) => {
    const { eventIds, deleteAll } = req.body;

    if (deleteAll) {
        // Delete all security events
        db.run('DELETE FROM security_events', (err) => {
            if (err) {
                console.error('Delete all security events error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete security events'
                });
            }

            console.log('🗑️ All security events deleted');
            res.json({
                success: true,
                message: 'All security events deleted successfully'
            });
        });
    } else if (eventIds && Array.isArray(eventIds)) {
        // Delete specific events
        const placeholders = eventIds.map(() => '?').join(',');
        db.run(`DELETE FROM security_events WHERE id IN (${placeholders})`, eventIds, function(err) {
            if (err) {
                console.error('Delete security events error:', err);
                return res.status(500).json({
                    success: false,
                    error: 'Failed to delete security events'
                });
            }

            console.log(`🗑️ Deleted ${this.changes} security events`);
            res.json({
                success: true,
                message: `Deleted ${this.changes} security events successfully`
            });
        });
    } else {
        res.status(400).json({
            success: false,
            error: 'Either eventIds array or deleteAll flag is required'
        });
    }
});

// Helper function to format uptime
function formatUptime(uptimeSeconds) {
    const days = Math.floor(uptimeSeconds / 86400);
    const hours = Math.floor((uptimeSeconds % 86400) / 3600);
    const minutes = Math.floor((uptimeSeconds % 3600) / 60);

    if (days > 0) {
        return `${days} day${days > 1 ? 's' : ''} ${hours.toString().padStart(2, '0')}h`;
    } else if (hours > 0) {
        return `${hours}h ${minutes.toString().padStart(2, '0')}m`;
    } else {
        return `${minutes}m`;
    }
}

// Discord API endpoints
app.get('/api/discord/status', (req, res) => {
    // Mock uptime (in production, get from actual bot)
    const mockUptimeSeconds = Math.floor(Math.random() * 604800) + 3600; // 1 hour to 1 week

    res.json({
        success: true,
        online: true,
        uptime: formatUptime(mockUptimeSeconds),
        guilds: 1,
        commandsToday: Math.floor(Math.random() * 50)
    });
});

app.get('/api/discord/activity', (req, res) => {
    const activities = [
        {
            type: 'COMMAND',
            description: 'Stats command executed',
            details: 'User requested system statistics',
            timestamp: new Date().toISOString()
        },
        {
            type: 'MESSAGE',
            description: 'Security alert sent',
            details: 'Invalid license attempt detected',
            timestamp: new Date(Date.now() - 300000).toISOString()
        }
    ];

    res.json({
        success: true,
        activities: activities
    });
});

app.post('/api/discord/test', (req, res) => {
    // Mock Discord test
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Discord bot test successful'
        });
    }, 1000);
});

app.post('/api/discord/daily-report', (req, res) => {
    // Mock daily report generation
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Daily report sent to Discord'
        });
    }, 2000);
});

app.post('/api/discord/restart', (req, res) => {
    // Mock Discord bot restart
    setTimeout(() => {
        res.json({
            success: true,
            message: 'Discord bot restarted successfully'
        });
    }, 3000);
});

// Webhook management endpoints
app.get('/api/discord/webhooks', (req, res) => {
    // Get current webhook configuration
    const webhooks = {
        security_alerts: '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
        esp32_errors: '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
        backend_errors: '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
    };

    res.json({
        success: true,
        webhooks: webhooks
    });
});

app.post('/api/discord/webhooks', (req, res) => {
    const { webhookType, webhookUrl } = req.body;

    if (!webhookType || !webhookUrl) {
        return res.status(400).json({
            success: false,
            error: 'Webhook type and URL are required'
        });
    }

    // Validate webhook URL format
    const webhookPattern = /^(\d+)\/([A-Za-z0-9_-]+)$/;
    if (!webhookPattern.test(webhookUrl)) {
        return res.status(400).json({
            success: false,
            error: 'Invalid webhook URL format. Use: channelId/token'
        });
    }

    // In production, save to database or config file
    // For now, just simulate success
    console.log(`📡 Webhook updated: ${webhookType} -> ${webhookUrl}`);

    res.json({
        success: true,
        message: `${webhookType} webhook updated successfully`
    });
});

app.post('/api/discord/test-webhook', (req, res) => {
    const { webhookType } = req.body;

    if (!webhookType) {
        return res.status(400).json({
            success: false,
            error: 'Webhook type is required'
        });
    }

    // Mock webhook test
    setTimeout(() => {
        res.json({
            success: true,
            message: `${webhookType} webhook test successful`
        });
    }, 1000);
});

// User management API endpoints
app.get('/api/admin/users', (req, res) => {
    const limit = parseInt(req.query.limit) || 20;

    db.all(`
        SELECT key as license_key, hwid, is_active, last_ip as ip_address, last_used as last_seen
        FROM licenses
        WHERE is_active = 1 OR last_used > datetime('now', '-7 days')
        ORDER BY last_used DESC
        LIMIT ?
    `, [limit], (err, rows) => {
        if (err) {
            console.error('Get users error:', err);
            return res.status(500).json({
                success: false,
                message: 'Failed to get users'
            });
        }

        const users = rows.map(user => ({
            ...user,
            is_online: Math.random() > 0.7 // Mock online status
        }));

        res.json({
            success: true,
            users: users
        });
    });
});

app.get('/api/admin/activity', (req, res) => {
    db.all(`
        SELECT
            'license_validation' as action,
            key as license_key,
            'User authenticated with license' as details,
            last_used as timestamp
        FROM licenses
        WHERE last_used IS NOT NULL
        ORDER BY last_used DESC
        LIMIT 10
    `, (err, rows) => {
        if (err) {
            console.error('Get activity error:', err);
            return res.json({
                success: true,
                activities: []
            });
        }

        res.json({
            success: true,
            activities: rows || []
        });
    });
});

// System API endpoints
app.get('/api/system/stats', (req, res) => {
    // Mock system stats (in production, get real system data)
    const uptimeSeconds = Math.floor(Math.random() * 2592000) + 86400; // 1 day to 30 days
    const memoryUsed = Math.floor(Math.random() * 600) + 200; // 200-800 MB
    const cpuUsage = Math.floor(Math.random() * 50) + 10; // 10-60%
    const diskUsage = Math.floor(Math.random() * 30) + 20; // 20-50%

    res.json({
        success: true,
        uptime: formatUptime(uptimeSeconds),
        memory: `${memoryUsed}Mi / 848Mi`,
        cpu: `${cpuUsage}%`,
        disk: `${diskUsage}%`
    });
});

app.get('/api/system/services', (req, res) => {
    const services = [
        {
            name: 'octane-auth',
            status: 'running',
            uptime: formatUptime(Math.floor(Math.random() * 86400) + 3600),
            memory: `${Math.floor(Math.random() * 100) + 50}Mi`,
            cpu: `${Math.floor(Math.random() * 20) + 5}%`
        },
        {
            name: 'discord-bot',
            status: Math.random() > 0.2 ? 'running' : 'stopped',
            uptime: formatUptime(Math.floor(Math.random() * 43200) + 1800),
            memory: `${Math.floor(Math.random() * 50) + 20}Mi`,
            cpu: `${Math.floor(Math.random() * 10) + 2}%`
        },
        {
            name: 'nginx',
            status: 'running',
            uptime: formatUptime(Math.floor(Math.random() * 172800) + 7200),
            memory: `${Math.floor(Math.random() * 30) + 10}Mi`,
            cpu: `${Math.floor(Math.random() * 5) + 1}%`
        }
    ];

    res.json({
        success: true,
        services: services
    });
});

app.get('/api/system/logs', (req, res) => {
    const logs = [
        { timestamp: new Date(Date.now() - 300000).toISOString(), level: 'info', message: 'System startup completed successfully' },
        { timestamp: new Date(Date.now() - 600000).toISOString(), level: 'warning', message: 'High memory usage detected: 85%' },
        { timestamp: new Date(Date.now() - 900000).toISOString(), level: 'info', message: 'Discord bot connected successfully' },
        { timestamp: new Date(Date.now() - 1200000).toISOString(), level: 'error', message: 'Failed to connect to external API' },
        { timestamp: new Date(Date.now() - 1500000).toISOString(), level: 'info', message: 'Database backup completed' }
    ];

    res.json({
        success: true,
        logs: logs
    });
});

app.post('/api/system/restart-service', (req, res) => {
    const { service } = req.body;

    if (!service) {
        return res.status(400).json({
            success: false,
            error: 'Service name is required'
        });
    }

    // Mock service restart
    setTimeout(() => {
        console.log(`🔄 Service restarted: ${service}`);
    }, 1000);

    res.json({
        success: true,
        message: `${service} restarted successfully`
    });
});

app.post('/api/system/health-check', (req, res) => {
    // Mock health check
    const issues = [];
    if (Math.random() > 0.8) issues.push('High memory usage');
    if (Math.random() > 0.9) issues.push('Disk space low');

    res.json({
        success: issues.length === 0,
        issues: issues
    });
});

// Settings API endpoints
app.get('/api/settings', (req, res) => {
    // Mock settings (in production, load from database or config file)
    const settings = {
        general: {
            systemName: 'Octane Recoil Scripts',
            maxLicenses: 1000,
            defaultDuration: '1month'
        },
        security: {
            maxFailedAttempts: 5,
            sessionTimeout: 60,
            enableRateLimit: true,
            enableSecurityLogs: true,
            enableHWIDBinding: true
        },
        discord: {
            guildId: '**********',
            webhooks: {
                security: '1398506465882411079/49oCxzr04R0v7JwYhpxveww1x3_H6pW7VoDocPa6FYPaykMJJegkNddJjkqh2CsbYbKy',
                esp32: '1398551599818866719/JqAUxaYYv4V2o1ncPfZeB2TtloxU3roht5r4sT3S_7MQ4hN3m3AU3Ah8EAKCQIFxRDLW',
                backend: '1398551716869443654/FIcTJWf78O1reAqOMAM-1vLylabv_9QAe9LZ7EtHPB-QGsLcL55iJp04VcxcZx7Xv63i'
            }
        }
    };

    res.json({
        success: true,
        settings: settings
    });
});

app.post('/api/settings/general', (req, res) => {
    const { systemName, maxLicenses, defaultDuration } = req.body;

    // In production, save to database or config file
    console.log('💾 General settings updated:', { systemName, maxLicenses, defaultDuration });

    res.json({
        success: true,
        message: 'General settings saved successfully'
    });
});

app.post('/api/settings/security', (req, res) => {
    const settings = req.body;

    // In production, save to database or config file
    console.log('🔐 Security settings updated:', settings);

    res.json({
        success: true,
        message: 'Security settings saved successfully'
    });
});

app.post('/api/settings/discord', (req, res) => {
    const settings = req.body;

    // In production, save to database or config file and restart Discord bot
    console.log('🤖 Discord settings updated:', settings);

    res.json({
        success: true,
        message: 'Discord settings saved successfully'
    });
});

// Export endpoints
app.get('/api/export/licenses', (req, res) => {
    db.all('SELECT * FROM licenses', (err, rows) => {
        if (err) {
            return res.status(500).json({ success: false, error: 'Export failed' });
        }

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=licenses-export.json');
        res.json(rows);
    });
});

app.get('/api/export/security-logs', (req, res) => {
    db.all('SELECT * FROM security_events', (err, rows) => {
        if (err) {
            return res.status(500).json({ success: false, error: 'Export failed' });
        }

        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=security-logs-export.json');
        res.json(rows);
    });
});

app.get('/api/export/full-backup', (req, res) => {
    // Mock full backup (in production, create actual backup)
    const backup = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        data: {
            licenses: [],
            security_events: [],
            settings: {}
        }
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=octane-backup.json');
    res.json(backup);
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '4.2.1'
    });
});

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

app.get('/key-maintenance', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'key-maintenance.html'));
});

app.get('/reminders', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'reminders.html'));
});

app.get('/discord', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'discord.html'));
});

app.get('/security-alerts', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'security-alerts.html'));
});

app.get('/user-management', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'user-management.html'));
});

app.get('/system-status', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'system-status.html'));
});

app.get('/settings', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'settings.html'));
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Octane Auth Server running on port ${PORT}`);
    console.log(`🌐 Admin Panel: http://localhost:${PORT}/admin`);
    console.log(`💾 Database: SQLite (lightweight)`);
    console.log(`🚀 Ready for development`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    if (db) db.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    if (db) db.close();
    process.exit(0);
});
